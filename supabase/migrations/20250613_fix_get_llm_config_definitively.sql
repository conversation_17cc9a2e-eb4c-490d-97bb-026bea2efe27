-- Fix get_llm_config function definitively
-- This migration drops all existing versions and recreates the function correctly

-- Step 1: Drop all existing versions of get_llm_config
DROP FUNCTION IF EXISTS public.get_llm_config(uuid) CASCADE;
DROP FUNCTION IF EXISTS public.get_llm_config(text) CASCADE;
DROP FUNCTION IF EXISTS public.get_llm_config_v2(uuid) CASCADE;
DROP FUNCTION IF EXISTS public.get_llm_config_v2(text) CASCADE;

-- Step 2: Create the correct get_llm_config function
CREATE OR REPLACE FUNCTION public.get_llm_config(p_org_id uuid)
RETURNS TABLE(provider text, api_key text, default_model text, openai_org_id text) 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result_row RECORD;
  decrypted_key text;
BEGIN
  -- Get the data with proper joins
  -- IMPORTANT: We use s.default_model from org_llm_settings, NOT c.default_model
  SELECT 
    c.provider,
    c.api_key,
    COALESCE(s.default_model, 'gpt-4') as default_model,
    c.openai_org_id
  INTO result_row
  FROM org_llm_credentials c
  LEFT JOIN org_llm_settings s 
    ON s.organisation_id = c.organisation_id 
    AND s.preferred_provider = c.provider
  WHERE c.organisation_id = p_org_id
  ORDER BY 
    CASE WHEN s.preferred_provider IS NOT NULL THEN 0 ELSE 1 END,
    c.created_at DESC
  LIMIT 1;
  
  -- If no result found, return NULL row
  IF NOT FOUND THEN
    RETURN QUERY SELECT NULL::text, NULL::text, NULL::text, NULL::text;
    RETURN;
  END IF;
  
  -- Try to decrypt if encrypted, otherwise use as-is
  BEGIN
    -- Check if decrypt_api_key function exists
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'decrypt_api_key') THEN
      IF length(result_row.api_key) > 100 THEN
        decrypted_key := decrypt_api_key(result_row.api_key);
      ELSE
        decrypted_key := result_row.api_key;
      END IF;
    ELSE
      decrypted_key := result_row.api_key;
    END IF;
  EXCEPTION WHEN OTHERS THEN
    decrypted_key := result_row.api_key;
  END;
  
  -- Return the result
  RETURN QUERY SELECT 
    result_row.provider,
    decrypted_key,
    result_row.default_model,
    result_row.openai_org_id;
END;
$$;

-- Step 3: Create text-based overload
CREATE OR REPLACE FUNCTION public.get_llm_config(p_org text)
RETURNS TABLE(provider text, api_key text, default_model text, openai_org_id text) 
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT * FROM get_llm_config(p_org::uuid);
$$;

-- Step 4: Grant permissions
GRANT EXECUTE ON FUNCTION public.get_llm_config(uuid) TO authenticated, service_role, anon;
GRANT EXECUTE ON FUNCTION public.get_llm_config(text) TO authenticated, service_role, anon;

-- Step 5: Add a comment to document the fix
COMMENT ON FUNCTION public.get_llm_config(uuid) IS 'Fixed version that correctly references s.default_model instead of c.default_model';