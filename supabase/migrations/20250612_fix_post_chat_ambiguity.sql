/*
  # Fix post_chat Function Ambiguity
  
  This migration resolves the PGRST203 error by:
  1. Documenting the current state of post_chat functions
  2. Removing all ambiguous versions
  3. Keeping only the deprecated 4-parameter version for backward compatibility
  4. Adding clear documentation about the event-based approach
  
  Timeline of post_chat function evolution:
  - 20250515181348: Created post_chat(p_org uuid, p_user_id uuid, p_message text)
  - 20250515183632: Same signature, added SECURITY DEFINER
  - 20250609000001: Added p_metadata jsonb parameter (4 params)
  - 20250111: Updated to use bus_notify (newest version)
  - 20250612010016: Attempted to drop 3-param version
  - 20250612: Marked as DEPRECATED in favor of events
  
  The error indicates something is calling with p_channel_id instead of p_org,
  which suggests either:
  - Old cached frontend code
  - Misconfigured API endpoint
  - Third-party integration using wrong parameter names
*/

-- First, let's check what functions currently exist
DO $$
DECLARE
  v_count INTEGER;
  v_functions TEXT;
BEGIN
  -- Count post_chat functions
  SELECT COUNT(*), STRING_AGG(
    format('%s(%s)', p.proname, pg_catalog.pg_get_function_identity_arguments(p.oid)), 
    ', '
  )
  INTO v_count, v_functions
  FROM pg_catalog.pg_proc p
  WHERE p.proname = 'post_chat';
  
  RAISE NOTICE 'Found % post_chat function(s): %', v_count, v_functions;
END $$;

-- Drop ALL versions of post_chat to eliminate ambiguity
DROP FUNCTION IF EXISTS post_chat(uuid, uuid, text);
DROP FUNCTION IF EXISTS post_chat(uuid, uuid, text, jsonb);
-- Also try to drop any other potential signatures
DROP FUNCTION IF EXISTS post_chat(uuid, text, jsonb);
DROP FUNCTION IF EXISTS post_chat(uuid, text);

-- Recreate ONLY the 4-parameter version as DEPRECATED
-- This maintains backward compatibility while encouraging migration
CREATE OR REPLACE FUNCTION post_chat(
  p_org uuid,
  p_user_id uuid,
  p_message text,
  p_metadata jsonb DEFAULT NULL
) RETURNS uuid AS $$
DECLARE
  new_id uuid := gen_random_uuid();
  chat_id uuid := gen_random_uuid();
  v_payload jsonb;
BEGIN
  -- Log deprecation warning
  RAISE WARNING 'post_chat() is DEPRECATED. Use event-based communication instead. Frontend should publish chat_posted events.';
  
  -- Insert the message into chat_log with metadata
  INSERT INTO chat_log(id, chat_id, sender, message, metadata, created_at, user_id)
  VALUES (new_id, chat_id, 'user', p_message, COALESCE(p_metadata, '{}'::jsonb), now(), p_user_id);
  
  -- Record the organisation ownership
  INSERT INTO chat_log_organisations(chat_id, organisations_id)
  VALUES (chat_id, p_org);

  -- Build the payload
  v_payload := json_build_object(
    'chat_id', chat_id,
    'user_id', p_user_id,
    'message', p_message,
    'metadata', p_metadata,
    'timestamp', now()
  )::jsonb;

  -- Use bus_notify to create the event (this will insert into bus_events and trigger webhooks)
  PERFORM bus_notify('chat_posted', p_org, v_payload);
  
  RETURN chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a wrapper function that logs parameter mismatches
-- This will help identify what's calling with wrong parameters
CREATE OR REPLACE FUNCTION post_chat_diagnostic(
  p_channel_id uuid DEFAULT NULL,
  p_org uuid DEFAULT NULL,
  p_user_id uuid DEFAULT NULL,
  p_message text DEFAULT NULL,
  p_metadata jsonb DEFAULT NULL
) RETURNS jsonb AS $$
DECLARE
  v_result jsonb;
BEGIN
  -- Log the call attempt
  v_result := jsonb_build_object(
    'error', 'Parameter mismatch detected',
    'received_params', jsonb_build_object(
      'p_channel_id', p_channel_id,
      'p_org', p_org,
      'p_user_id', p_user_id,
      'p_message', p_message,
      'p_metadata', p_metadata
    ),
    'expected_params', jsonb_build_array(
      'p_org uuid',
      'p_user_id uuid', 
      'p_message text',
      'p_metadata jsonb (optional)'
    ),
    'recommendation', 'Use event-based communication: publish chat_posted events instead of calling RPC',
    'timestamp', now()
  );
  
  -- Log to a diagnostic table if it exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'diagnostic_logs') THEN
    INSERT INTO diagnostic_logs (event_type, payload, created_at)
    VALUES ('post_chat_parameter_mismatch', v_result, now());
  END IF;
  
  -- Raise an informative error
  RAISE EXCEPTION 'post_chat called with wrong parameters. Expected (p_org, p_user_id, p_message, p_metadata) but received (p_channel_id=%, p_org=%, p_user_id=%, p_message=%, p_metadata=%). Use event-based communication instead.', 
    p_channel_id, p_org, p_user_id, p_message, p_metadata;
    
  RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION post_chat(uuid, uuid, text, jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION post_chat_diagnostic(uuid, uuid, uuid, text, jsonb) TO authenticated;

-- Add comprehensive documentation
COMMENT ON FUNCTION post_chat(uuid, uuid, text, jsonb) IS 
  'DEPRECATED: Legacy RPC function for posting chat messages. 
   DO NOT USE IN NEW CODE.
   
   The frontend should publish chat_posted events directly to bus channels instead.
   This function is kept only for backward compatibility and will be removed in future.
   
   Parameters:
   - p_org: Organization ID
   - p_user_id: User ID
   - p_message: Chat message text
   - p_metadata: Optional metadata (jsonb)
   
   For new implementations, use:
   mcpPublish("chat_posted", { chat_id, user_id, message, timestamp }, orgId)';

COMMENT ON FUNCTION post_chat_diagnostic(uuid, uuid, uuid, text, jsonb) IS 
  'Diagnostic function to catch and log incorrect post_chat calls.
   This helps identify code that needs to be updated to use the correct parameters
   or migrated to event-based communication.';

-- Create an index to help identify what might be calling the function
CREATE INDEX IF NOT EXISTS idx_bus_events_payload_post_chat 
  ON bus_events USING gin(payload) 
  WHERE payload::text LIKE '%post_chat%';