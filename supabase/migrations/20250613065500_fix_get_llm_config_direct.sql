
    -- First ensure all old versions are dropped
    DROP FUNCTION IF EXISTS public.get_llm_config(uuid) CASCADE;
    DROP FUNCTION IF EXISTS public.get_llm_config(text) CASCADE;
    DROP FUNCTION IF EXISTS public.get_llm_config_v2(uuid) CASCADE;
    DROP FUNCTION IF EXISTS public.get_llm_config_v2(text) CASCADE;
    
    -- Create the correct version that properly handles the schema
    CREATE OR REPLACE FUNCTION public.get_llm_config(p_org_id uuid)
    RETURNS TABLE (
      provider text,
      api_key text,
      default_model text,
      openai_org_id text
    )
    LANGUAGE plpgsql
    SECURITY DEFINER
    SET search_path = public
    AS $$
    DECLARE
      v_provider text;
      v_api_key text;
      v_model text;
      v_openai_org_id text;
    BEGIN
      -- First try to get settings from org_llm_settings and org_llm_credentials
      -- Note: org_llm_settings has 'model' column, org_llm_credentials has 'default_model'
      SELECT 
        COALESCE(s.provider, c.provider, 'openai'),
        c.api_key,
        COALESCE(s.model, c.default_model, 'gpt-4-turbo-preview'),
        c.openai_org_id
      INTO v_provider, v_api_key, v_model, v_openai_org_id
      FROM org_llm_credentials c
      LEFT JOIN org_llm_settings s 
        ON s.organisation_id = c.organisation_id 
        AND s.provider = c.provider
      WHERE c.organisation_id = p_org_id
        AND c.is_active = true
      ORDER BY c.updated_at DESC
      LIMIT 1;
      
      -- If no credentials found, try api_keys table as fallback
      IF v_api_key IS NULL THEN
        SELECT 
          COALESCE(provider, 'openai'),
          key,
          'gpt-4-turbo-preview',
          NULL
        INTO v_provider, v_api_key, v_model, v_openai_org_id
        FROM api_keys
        WHERE organisation_id = p_org_id
        ORDER BY created_at DESC
        LIMIT 1;
      END IF;
      
      -- Return the result
      RETURN QUERY SELECT v_provider, v_api_key, v_model, v_openai_org_id;
    END;
    $$;
    
    -- Create text parameter variant for backward compatibility
    CREATE OR REPLACE FUNCTION public.get_llm_config(p_org_id text)
    RETURNS TABLE (
      provider text,
      api_key text,
      default_model text,
      openai_org_id text
    )
    LANGUAGE sql
    SECURITY DEFINER
    SET search_path = public
    AS $$
      SELECT * FROM get_llm_config(p_org_id::uuid);
    $$;
    
    -- Create v2 variants as aliases
    CREATE OR REPLACE FUNCTION public.get_llm_config_v2(p_org_id uuid)
    RETURNS TABLE (
      provider text,
      api_key text,
      default_model text,
      openai_org_id text
    )
    LANGUAGE sql
    SECURITY DEFINER
    AS $$
      SELECT * FROM get_llm_config(p_org_id);
    $$;
    
    CREATE OR REPLACE FUNCTION public.get_llm_config_v2(p_org_id text)
    RETURNS TABLE (
      provider text,
      api_key text,
      default_model text,
      openai_org_id text
    )
    LANGUAGE sql
    SECURITY DEFINER
    AS $$
      SELECT * FROM get_llm_config(p_org_id);
    $$;
    
    -- Grant necessary permissions
    GRANT EXECUTE ON FUNCTION public.get_llm_config(uuid) TO authenticated;
    GRANT EXECUTE ON FUNCTION public.get_llm_config(uuid) TO service_role;
    GRANT EXECUTE ON FUNCTION public.get_llm_config(uuid) TO anon;
    
    GRANT EXECUTE ON FUNCTION public.get_llm_config(text) TO authenticated;
    GRANT EXECUTE ON FUNCTION public.get_llm_config(text) TO service_role;
    GRANT EXECUTE ON FUNCTION public.get_llm_config(text) TO anon;
    
    GRANT EXECUTE ON FUNCTION public.get_llm_config_v2(uuid) TO authenticated;
    GRANT EXECUTE ON FUNCTION public.get_llm_config_v2(uuid) TO service_role;
    GRANT EXECUTE ON FUNCTION public.get_llm_config_v2(uuid) TO anon;
    
    GRANT EXECUTE ON FUNCTION public.get_llm_config_v2(text) TO authenticated;
    GRANT EXECUTE ON FUNCTION public.get_llm_config_v2(text) TO service_role;
    GRANT EXECUTE ON FUNCTION public.get_llm_config_v2(text) TO anon;
    
    -- Add helpful comment
    COMMENT ON FUNCTION public.get_llm_config(uuid) IS 
    'Fixed version that correctly handles org_llm_settings.model and org_llm_credentials.default_model columns';
  