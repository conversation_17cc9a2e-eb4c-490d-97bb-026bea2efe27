-- Add critical indexes for performance optimization
-- Based on agent pipeline analysis recommendations

-- Thoughts table indexes
-- Index on run_id (equivalent to pipeline_run_id in the analysis)
CREATE INDEX IF NOT EXISTS idx_thoughts_run_id 
ON thoughts(run_id);

-- Index on thought_number for ordering and filtering
CREATE INDEX IF NOT EXISTS idx_thoughts_thought_number 
ON thoughts(thought_number);

-- Composite index for queries that filter by run_id and order by thought_number
CREATE INDEX IF NOT EXISTS idx_thoughts_run_id_thought_number 
ON thoughts(run_id, thought_number);

-- Additional index for agent_role as it's frequently used in queries
CREATE INDEX IF NOT EXISTS idx_thoughts_agent_role 
ON thoughts(agent_role);

-- Pipeline runs table indexes
-- Composite index on organisation_id and status for common query pattern
CREATE INDEX IF NOT EXISTS idx_pipeline_runs_org_status 
ON pipeline_runs(organisation_id, status);

-- Additional indexes based on query analysis
-- Index on created_at for time-based queries
CREATE INDEX IF NOT EXISTS idx_pipeline_runs_created_at 
ON pipeline_runs(created_at DESC);

-- Index on task_id for lookups
CREATE INDEX IF NOT EXISTS idx_pipeline_runs_task_id 
ON pipeline_runs(task_id);

-- Thoughts table additional performance indexes
-- Index on created_at for time-based queries
CREATE INDEX IF NOT EXISTS idx_thoughts_created_at 
ON thoughts(created_at DESC);

-- Composite index for organisation_id and run_id for filtered queries
CREATE INDEX IF NOT EXISTS idx_thoughts_org_run 
ON thoughts(organisation_id, run_id);

-- Bus events table indexes (if exists)
-- These are commonly queried tables in the event-driven architecture
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'bus_events'
  ) THEN
    -- Index on channel for filtering events by channel
    CREATE INDEX IF NOT EXISTS idx_bus_events_channel 
    ON bus_events(channel);
    
    -- Index on event_type for filtering by event type
    CREATE INDEX IF NOT EXISTS idx_bus_events_event_type 
    ON bus_events(event_type);
    
    -- Composite index for channel and created_at for time-based channel queries
    CREATE INDEX IF NOT EXISTS idx_bus_events_channel_created 
    ON bus_events(channel, created_at DESC);
    
    -- Index on processed flag if it exists
    IF EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'bus_events'
      AND column_name = 'processed'
    ) THEN
      CREATE INDEX IF NOT EXISTS idx_bus_events_processed 
      ON bus_events(processed) 
      WHERE processed = false;
    END IF;
  END IF;
END $$;

-- Agent registry table indexes (if exists)
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'agent_registry'
  ) THEN
    -- Index on status for finding available agents
    CREATE INDEX IF NOT EXISTS idx_agent_registry_status 
    ON agent_registry(status);
    
    -- Index on last_heartbeat for monitoring agent health
    IF EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'agent_registry'
      AND column_name = 'last_heartbeat'
    ) THEN
      CREATE INDEX IF NOT EXISTS idx_agent_registry_heartbeat 
      ON agent_registry(last_heartbeat DESC);
    END IF;
  END IF;
END $$;

-- Contracts table indexes (if exists)
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'contracts'
  ) THEN
    -- Composite index for organisation_id and status
    CREATE INDEX IF NOT EXISTS idx_contracts_org_status 
    ON contracts(organisation_id, status);
    
    -- Index on deadline for time-sensitive queries
    CREATE INDEX IF NOT EXISTS idx_contracts_deadline 
    ON contracts(deadline);
  END IF;
END $$;

-- Bids table indexes (if exists)
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'bids'
  ) THEN
    -- Index on status for filtering pending/accepted/rejected bids
    CREATE INDEX IF NOT EXISTS idx_bids_status 
    ON bids(status);
    
    -- Composite index for contract_id and status
    CREATE INDEX IF NOT EXISTS idx_bids_contract_status 
    ON bids(contract_id, status);
  END IF;
END $$;

-- Analyze tables to update statistics for query planner
ANALYZE thoughts;
ANALYZE pipeline_runs;

-- Analyze other tables if they exist
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'bus_events') THEN
    ANALYZE bus_events;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'agent_registry') THEN
    ANALYZE agent_registry;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'contracts') THEN
    ANALYZE contracts;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'bids') THEN
    ANALYZE bids;
  END IF;
END $$;