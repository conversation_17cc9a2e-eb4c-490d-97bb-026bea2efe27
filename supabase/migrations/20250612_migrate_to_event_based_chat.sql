/*
  # Migrate to Event-Based Chat Communication
  
  This migration documents the transition from RPC-based to event-based communication
  between the frontend and master agent.
  
  ## Changes:
  1. The frontend now publishes 'chat_posted' events directly to bus channels
  2. The master agent subscribes to these events via Supabase Realtime
  3. The post_chat RPC function is deprecated but kept for backward compatibility
  
  ## Architecture:
  - Frontend: Uses mcpPublish() to send chat_posted events
  - Bus Channel: bus-agent-{orgId} carries the events
  - Master Agent: Subscribes to channels and processes events
  
  ## Benefits:
  - Better scalability with event-driven architecture
  - Decoupled frontend and backend
  - Supports multiple master agent instances
  - More robust error handling and retry mechanisms
*/

-- Add a comment to the post_chat function indicating it's deprecated
COMMENT ON FUNCTION post_chat(uuid, uuid, text, jsonb) IS 
  'DEPRECATED: Posts a user chat message. Use event-based communication instead. 
   The frontend should publish chat_posted events directly to bus channels.
   This function is kept for backward compatibility only.';

-- Create an index on bus_events for better performance when querying chat events
CREATE INDEX IF NOT EXISTS idx_bus_events_event_chat 
  ON bus_events(event, created_at DESC) 
  WHERE event = 'chat_posted';

-- Add a comment explaining the new architecture
COMMENT ON TABLE bus_events IS 
  'Central event store for the event-driven architecture. 
   Chat messages are now published as chat_posted events and consumed by the master agent.
   This replaces the direct RPC approach for better scalability.';