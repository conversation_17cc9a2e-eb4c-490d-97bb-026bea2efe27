-- Consolidated Performance Fixes Migration
-- This migration ensures all required columns exist and creates performance indexes
-- Date: 2025-06-12

-- ============================================
-- PART 1: ADD MISSING COLUMNS
-- ============================================

-- Add missing columns to thoughts table
ALTER TABLE thoughts 
ADD COLUMN IF NOT EXISTS agent_id uuid;

ALTER TABLE thoughts 
ADD COLUMN IF NOT EXISTS thought_type text;

-- Add comments for documentation
COMMENT ON COLUMN thoughts.agent_id IS 'ID of the agent that generated this thought';
COMMENT ON COLUMN thoughts.thought_type IS 'Type/category of the thought for filtering and indexing purposes';

-- Add missing columns to bus_events table
ALTER TABLE bus_events 
ADD COLUMN IF NOT EXISTS event_type text;

ALTER TABLE bus_events 
ADD COLUMN IF NOT EXISTS processed boolean DEFAULT false;

-- Add comments for documentation
COMMENT ON COLUMN bus_events.event_type IS 'Type/category of the bus event for filtering and indexing';
COMMENT ON COLUMN bus_events.processed IS 'Indicates whether the event has been processed by consumers';

-- Update existing bus_events rows to have processed = true (assuming historical events were processed)
UPDATE bus_events 
SET processed = true 
WHERE processed IS NULL;

-- Add missing columns to agent_registry table
ALTER TABLE agent_registry 
ADD COLUMN IF NOT EXISTS current_load integer DEFAULT 0;

ALTER TABLE agent_registry 
ADD COLUMN IF NOT EXISTS last_heartbeat timestamptz DEFAULT now();

-- Add comments for documentation
COMMENT ON COLUMN agent_registry.current_load IS 'Current workload/task count for the agent, used for load balancing';
COMMENT ON COLUMN agent_registry.last_heartbeat IS 'Timestamp of the last heartbeat received from the agent';

-- Update existing agent_registry rows to have reasonable defaults
UPDATE agent_registry
SET last_heartbeat = COALESCE(updated_at, now())
WHERE last_heartbeat IS NULL;

-- Add missing column to org_llm_credentials table
ALTER TABLE org_llm_credentials
ADD COLUMN IF NOT EXISTS is_active boolean DEFAULT true;

-- Add comment for documentation
COMMENT ON COLUMN org_llm_credentials.is_active IS 'Indicates whether the LLM credential is active and available for use';

-- Update existing org_llm_credentials rows to have is_active = true
UPDATE org_llm_credentials
SET is_active = true
WHERE is_active IS NULL;

-- ============================================
-- PART 2: CREATE PERFORMANCE INDEXES
-- ============================================

-- P1: Performance Optimization - Database Indexes
-- Target: <30ms query response time for all critical operations

-- ============================================
-- WEBHOOK QUEUE INDEXES (for migration period)
-- ============================================

-- Index for pending webhook processing
CREATE INDEX IF NOT EXISTS idx_webhook_queue_pending_v2
ON webhook_queue(status, created_at) 
WHERE status = 'pending';

-- Index for webhook queue by organization
CREATE INDEX IF NOT EXISTS idx_webhook_queue_org_status_v2
ON webhook_queue(organisation_id, status, created_at);

-- Index for webhook retry logic
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'webhook_queue'
    AND column_name = 'retry_count'
  ) THEN
    CREATE INDEX IF NOT EXISTS idx_webhook_queue_retry_v2
    ON webhook_queue(retry_count, next_retry_at)
    WHERE status IN ('pending', 'failed') AND retry_count < max_retries;
  END IF;
END $$;

-- ============================================
-- CHAT LOG INDEXES (high-volume table)
-- ============================================

-- Composite index for chat queries
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'chat_log'
    AND column_name = 'thread_id'
  ) THEN
    CREATE INDEX IF NOT EXISTS idx_chat_log_thread_created_v2
    ON chat_log(thread_id, created_at DESC);
  END IF;
END $$;

-- Index for organization-based chat queries
-- NOTE: Commented out because chat_log doesn't have organisation_id column
-- Organization relationship is maintained through chat_log_organisations table
-- CREATE INDEX IF NOT EXISTS idx_chat_log_org_thread_v2
-- ON chat_log(organisation_id, thread_id, created_at DESC);

-- Index for user chat history
CREATE INDEX IF NOT EXISTS idx_chat_log_user_created_v2
ON chat_log(user_id, created_at DESC) 
WHERE user_id IS NOT NULL;

-- Index for agent responses
CREATE INDEX IF NOT EXISTS idx_chat_log_sender_v2
ON chat_log(sender, created_at DESC);

-- ============================================
-- PIPELINE RUNS INDEXES (high-volume table)
-- ============================================

-- Index for active pipelines
CREATE INDEX IF NOT EXISTS idx_pipeline_runs_active_v2
ON pipeline_runs(status, created_at DESC) 
WHERE status IN ('running', 'pending');

-- Index for organization pipeline queries
CREATE INDEX IF NOT EXISTS idx_pipeline_runs_org_status_v2
ON pipeline_runs(organisation_id, status, created_at DESC);

-- Index for contract-based pipeline lookups
-- COMMENTED OUT: pipeline_runs table does not have a contract_id column
-- The table uses task_id instead for identifying work items
-- CREATE INDEX IF NOT EXISTS idx_pipeline_runs_contract_v2
-- ON pipeline_runs(contract_id, created_at DESC);

-- ============================================
-- THOUGHTS TABLE INDEXES (high-volume writes)
-- ============================================

-- Index for pipeline thoughts retrieval
CREATE INDEX IF NOT EXISTS idx_thoughts_pipeline_v2
ON thoughts(pipeline_run_id, created_at);

-- Index for agent thoughts
CREATE INDEX IF NOT EXISTS idx_thoughts_agent_v2
ON thoughts(agent_id, created_at DESC);

-- Index for thought type filtering
CREATE INDEX IF NOT EXISTS idx_thoughts_type_v2
ON thoughts(thought_type, created_at DESC);

-- ============================================
-- BUS EVENTS INDEXES (real-time queries)
-- ============================================

-- Index for recent events by type
CREATE INDEX IF NOT EXISTS idx_bus_events_type_created_v2
ON bus_events(event_type, created_at DESC);

-- Index for organization event stream
CREATE INDEX IF NOT EXISTS idx_bus_events_org_created_v2
ON bus_events(organisation_id, created_at DESC);

-- Partial index for unprocessed events
CREATE INDEX IF NOT EXISTS idx_bus_events_unprocessed_v2
ON bus_events(created_at) 
WHERE processed = false;

-- ============================================
-- CONTRACTS INDEXES
-- ============================================

-- Index for active contracts by category
CREATE INDEX IF NOT EXISTS idx_contracts_category_status_v2
ON contracts(category, status, created_at DESC) 
WHERE status IN ('active', 'pending');

-- Index for organization contracts
CREATE INDEX IF NOT EXISTS idx_contracts_org_status_v2
ON contracts(organisation_id, status, created_at DESC);

-- Index for budget range queries
CREATE INDEX IF NOT EXISTS idx_contracts_budget_v2
ON contracts(budget, status) 
WHERE status = 'active';

-- ============================================
-- AGENT REGISTRY INDEXES
-- ============================================

-- Index for active agents by name
-- NOTE: Using 'agent_role' column as that's what exists in the table
CREATE INDEX IF NOT EXISTS idx_agent_registry_name_status_v2
ON agent_registry(name, status)
WHERE status = 'active';

-- Index for agent load balancing
CREATE INDEX IF NOT EXISTS idx_agent_registry_load_v2
ON agent_registry(current_load, status) 
WHERE status = 'active';

-- Index for heartbeat monitoring
CREATE INDEX IF NOT EXISTS idx_agent_registry_heartbeat_v2
ON agent_registry(last_heartbeat DESC) 
WHERE status != 'offline';

-- ============================================
-- LLM CREDENTIALS INDEXES
-- ============================================

-- Index for credential lookups
CREATE INDEX IF NOT EXISTS idx_llm_credentials_org_provider_v2
ON org_llm_credentials(organisation_id, provider, is_active)
WHERE is_active = true;

-- ============================================
-- FUNCTION INDEXES FOR COMPLEX QUERIES
-- ============================================

-- Function index for JSON data extraction (if needed)
CREATE INDEX IF NOT EXISTS idx_chat_log_metadata_user_v2
ON chat_log((metadata->>'user_type')) 
WHERE metadata IS NOT NULL;

-- Function index for pipeline metadata
-- COMMENTED OUT: pipeline_runs table does not have a metadata column
-- CREATE INDEX IF NOT EXISTS idx_pipeline_runs_metadata_priority_v2
-- ON pipeline_runs((metadata->>'priority'))
-- WHERE metadata IS NOT NULL;

-- ============================================
-- STATISTICS UPDATE
-- ============================================

-- Update table statistics for query planner
ANALYZE webhook_queue;
ANALYZE chat_log;
ANALYZE pipeline_runs;
ANALYZE thoughts;
ANALYZE bus_events;
ANALYZE contracts;
ANALYZE agent_registry;
ANALYZE org_llm_credentials;

-- ============================================
-- PERFORMANCE MONITORING FUNCTIONS
-- ============================================

-- Function to check index usage
CREATE OR REPLACE FUNCTION check_index_usage()
RETURNS TABLE(
  schemaname text,
  tablename text,
  indexname text,
  idx_scan bigint,
  idx_tup_read bigint,
  idx_tup_fetch bigint,
  size text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.schemaname,
    s.tablename,
    s.indexname,
    s.idx_scan,
    s.idx_tup_read,
    s.idx_tup_fetch,
    pg_size_pretty(pg_relation_size(s.indexrelid)) as size
  FROM pg_stat_user_indexes s
  JOIN pg_index i ON s.indexrelid = i.indexrelid
  WHERE s.schemaname = 'public'
  ORDER BY s.idx_scan DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to identify slow queries
CREATE OR REPLACE FUNCTION get_slow_queries(duration_ms int DEFAULT 100)
RETURNS TABLE(
  query text,
  calls bigint,
  total_time double precision,
  mean_time double precision,
  max_time double precision
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    substr(query, 1, 100) as query,
    calls,
    total_exec_time as total_time,
    mean_exec_time as mean_time,
    max_exec_time as max_time
  FROM pg_stat_statements
  WHERE mean_exec_time > duration_ms
  ORDER BY mean_exec_time DESC
  LIMIT 20;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================
-- CONFIGURATION FOR PERFORMANCE
-- ============================================

-- Ensure autovacuum is properly configured for high-volume tables
ALTER TABLE chat_log SET (
  autovacuum_vacuum_scale_factor = 0.1,
  autovacuum_analyze_scale_factor = 0.05
);

ALTER TABLE pipeline_runs SET (
  autovacuum_vacuum_scale_factor = 0.1,
  autovacuum_analyze_scale_factor = 0.05
);

ALTER TABLE thoughts SET (
  autovacuum_vacuum_scale_factor = 0.1,
  autovacuum_analyze_scale_factor = 0.05
);

ALTER TABLE bus_events SET (
  autovacuum_vacuum_scale_factor = 0.1,
  autovacuum_analyze_scale_factor = 0.05
);

-- Add comment for documentation
COMMENT ON FUNCTION check_index_usage() IS 'P1: Monitor index usage to identify unused or inefficient indexes';
COMMENT ON FUNCTION get_slow_queries(int) IS 'P1: Identify queries exceeding target response time (default 100ms)';