-- Create Library Storage Bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'library-assets',
  'library-assets', 
  false, -- private bucket
  52428800, -- 50MB limit
  ARRAY[
    'audio/mpeg',
    'audio/wav',
    'audio/ogg',
    'audio/mp3',
    'audio/flac',
    'audio/aac',
    'audio/webm',
    'audio/x-m4a',
    'application/octet-stream'
  ]::text[]
)
ON CONFLICT (id) DO UPDATE
SET 
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create RLS policies for the bucket
-- Policy: Users can upload their own files
CREATE POLICY "Users can upload their own files to library" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (
  bucket_id = 'library-assets' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Users can view their own files  
CREATE POLICY "Users can view their own files in library" ON storage.objects
FOR SELECT TO authenticated
USING (
  bucket_id = 'library-assets' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Users can update their own files
CREATE POLICY "Users can update their own files in library" ON storage.objects
FOR UPDATE TO authenticated
USING (
  bucket_id = 'library-assets' AND
  auth.uid()::text = (storage.foldername(name))[1]
)
WITH CHECK (
  bucket_id = 'library-assets' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Users can delete their own files
CREATE POLICY "Users can delete their own files from library" ON storage.objects
FOR DELETE TO authenticated
USING (
  bucket_id = 'library-assets' AND
  auth.uid()::text = (storage.foldername(name))[1]
);