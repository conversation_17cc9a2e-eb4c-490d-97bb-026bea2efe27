-- Migration: Add Bus Channels Support
-- Description: Implements enhanced bus channels for pipeline agent communication

-- 1. Create bus_events table if it doesn't exist
CREATE TABLE IF NOT EXISTS bus_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_name TEXT NOT NULL,
    payload JSONB NOT NULL,
    organisation_id UUID NOT NULL REFERENCES organisations(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Add channel support to bus_events table
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'bus_events'
  ) THEN
    ALTER TABLE bus_events
    ADD COLUMN IF NOT EXISTS channel TEXT DEFAULT 'bus-agent-pipeline',
    ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 5 CHECK (priority >= 1 AND priority <= 10),
    ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS request_id UUID,
    ADD COLUMN IF NOT EXISTS correlation_id UUID,
    ADD COLUMN IF NOT EXISTS source_agent TEXT,
    ADD COLUMN IF NOT EXISTS target_agent TEXT,
    ADD COLUMN IF NOT EXISTS pipeline_run_id UUID;
  END IF;
END $$;

-- 3. Create dead_letter_queue table
CREATE TABLE IF NOT EXISTS dead_letter_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    original_event_id UUID REFERENCES bus_events(id),
    channel TEXT NOT NULL,
    event_name TEXT NOT NULL,
    payload JSONB NOT NULL,
    error_message TEXT,
    error_details JSONB DEFAULT '{}',
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    organisation_id UUID NOT NULL REFERENCES organisations(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_retry_at TIMESTAMPTZ,
    next_retry_at TIMESTAMPTZ,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'retrying', 'failed', 'resolved'))
);

-- 4. Add indexes for channel-based queries
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'bus_events'
  ) THEN
    CREATE INDEX IF NOT EXISTS idx_bus_events_channel ON bus_events(channel);
    CREATE INDEX IF NOT EXISTS idx_bus_events_priority ON bus_events(priority);
    CREATE INDEX IF NOT EXISTS idx_bus_events_pipeline_run_id ON bus_events(pipeline_run_id);
  END IF;
END $$;