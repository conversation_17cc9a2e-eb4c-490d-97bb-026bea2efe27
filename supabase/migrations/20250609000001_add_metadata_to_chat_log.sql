-- Add metadata column to chat_log table for storing additional information
-- This is needed by the master agent to store processing details

ALTER TABLE public.chat_log
ADD COLUMN IF NOT EXISTS metadata jsonb DEFAULT '{}'::jsonb;

-- Create an index on metadata for better query performance
CREATE INDEX IF NOT EXISTS idx_chat_log_metadata ON public.chat_log USING gin(metadata);

-- Update RLS policies to include metadata access
-- The existing policies should already cover this, but let's ensure service role can write metadata

-- Add policy for service role to insert/update chat_log with metadata
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'chat_log'
    AND policyname = 'Service role can manage chat_log'
  ) THEN
    CREATE POLICY "Service role can manage chat_log"
      ON public.chat_log
      FOR ALL
      USING (auth.role() = 'service_role')
      WITH CHECK (auth.role() = 'service_role');
  END IF;
END $$;

-- Also ensure the post_agent_reply function can handle metadata
CREATE OR REPLACE FUNCTION post_agent_reply(
  p_org uuid,
  p_chat_id uuid,
  p_message text,
  p_metadata jsonb DEFAULT NULL
) RETURNS void AS $$
DECLARE
  new_id uuid := gen_random_uuid();
BEGIN
  -- Insert the reply into chat_log with metadata
  INSERT INTO chat_log(id, chat_id, sender, message, metadata, created_at)
  VALUES (new_id, p_chat_id, 'master_agent', p_message, COALESCE(p_metadata, '{}'::jsonb), now());

  -- Notify the event bus
  PERFORM pg_notify(
    'bus',
    json_build_object(
      'event', 'agent_reply',
      'organisation_id', p_org,
      'timestamp', now(),
      'payload', json_build_object(
        'chat_id', p_chat_id,
        'message', p_message,
        'metadata', p_metadata
      )
    )::text
  );
END;
$$ LANGUAGE plpgsql;

-- Also update the post_chat function to support metadata
CREATE OR REPLACE FUNCTION post_chat(
  p_org uuid,
  p_user_id uuid,
  p_message text,
  p_metadata jsonb DEFAULT NULL
) RETURNS uuid AS $$
DECLARE
  new_id uuid := gen_random_uuid();
  chat_id uuid := gen_random_uuid();
BEGIN
  -- Insert the message into chat_log with metadata
  INSERT INTO chat_log(id, chat_id, sender, message, metadata, created_at)
  VALUES (new_id, chat_id, 'user', p_message, COALESCE(p_metadata, '{}'::jsonb), now());
  
  -- Record the organisation ownership
  INSERT INTO chat_log_organisations(chat_id, organisations_id)
  VALUES (chat_id, p_org);

  -- Notify the event bus
  PERFORM pg_notify(
    'bus',
    json_build_object(
      'event', 'chat_posted',
      'organisation_id', p_org,
      'timestamp', now(),
      'payload', json_build_object(
        'chat_id', chat_id,
        'user_id', p_user_id,
        'message', p_message,
        'metadata', p_metadata
      )
    )::text
  );
  
  RETURN chat_id;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT ALL ON public.chat_log TO service_role;
GRANT ALL ON public.chat_log TO authenticated;