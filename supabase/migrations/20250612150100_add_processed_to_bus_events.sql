-- Add processed column to bus_events table
-- This column is required for the performance index in 20250610_p1_performance_indexes.sql

ALTER TABLE bus_events 
ADD COLUMN IF NOT EXISTS processed boolean DEFAULT false;

-- Add comment for documentation
COMMENT ON COLUMN bus_events.processed IS 'Indicates whether the event has been processed by consumers';

-- Update existing rows to have processed = true (assuming historical events were processed)
-- This prevents a large backlog of unprocessed events on first deployment
UPDATE bus_events 
SET processed = true 
WHERE processed IS NULL;

-- Create index for efficient querying of unprocessed events
-- Note: This index is also created in 20250610_p1_performance_indexes.sql
-- but we create it here too in case migrations run out of order
CREATE INDEX IF NOT EXISTS idx_bus_events_unprocessed_v2
ON bus_events(created_at) 
WHERE processed = false;