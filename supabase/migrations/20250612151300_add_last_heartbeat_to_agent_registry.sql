-- Add missing last_heartbeat column to agent_registry table
-- This column is required by the performance index idx_agent_registry_heartbeat_v2

ALTER TABLE agent_registry 
ADD COLUMN IF NOT EXISTS last_heartbeat timestamptz DEFAULT now();

-- Add comment for documentation
COMMENT ON COLUMN agent_registry.last_heartbeat IS 'Timestamp of the last heartbeat received from the agent';

-- Update existing rows to have a reasonable default value
UPDATE agent_registry
SET last_heartbeat = COALESCE(updated_at, now())
WHERE last_heartbeat IS NULL;