-- Add trace_id column to pipeline_runs table for event correlation
ALTER TABLE pipeline_runs 
ADD COLUMN IF NOT EXISTS trace_id UUID DEFAULT gen_random_uuid();

-- Add index for efficient querying by trace_id
CREATE INDEX IF NOT EXISTS idx_pipeline_runs_trace_id ON pipeline_runs(trace_id);

-- Update existing rows to have a trace_id if they don't already
UPDATE pipeline_runs 
SET trace_id = gen_random_uuid() 
WHERE trace_id IS NULL;

-- Make trace_id NOT NULL after populating existing rows
ALTER TABLE pipeline_runs 
ALTER COLUMN trace_id SET NOT NULL;