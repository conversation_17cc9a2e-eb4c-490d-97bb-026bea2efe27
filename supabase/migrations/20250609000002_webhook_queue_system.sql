-- Create webhook queue table for reliable webhook delivery
CREATE TABLE IF NOT EXISTS webhook_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_name TEXT NOT NULL,
  organisation_id TEXT NOT NULL,
  payload JSONB NOT NULL,
  webhook_url TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'delivered', 'failed')),
  attempts INT DEFAULT 0,
  max_attempts INT DEFAULT 3,
  last_error TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  processed_at TIMESTAMPTZ,
  delivered_at TIMESTAMPTZ,
  next_retry_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient polling
CREATE INDEX idx_webhook_queue_status_retry ON webhook_queue(status, next_retry_at) 
WHERE status IN ('pending', 'failed');

CREATE INDEX idx_webhook_queue_created_at ON webhook_queue(created_at);

-- Create bus events table to capture all bus events
CREATE TABLE IF NOT EXISTS bus_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_name TEXT NOT NULL,
  organisation_id TEXT NOT NULL,
  payload JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for bus events
CREATE INDEX idx_bus_events_created_at ON bus_events(created_at);

-- Function to calculate next retry time with exponential backoff
CREATE OR REPLACE FUNCTION calculate_next_retry(attempts INT)
RETURNS TIMESTAMPTZ AS $$
BEGIN
  -- Exponential backoff: 1s, 2s, 4s, 8s, etc.
  RETURN NOW() + (POWER(2, attempts) || ' seconds')::INTERVAL;
END;
$$ LANGUAGE plpgsql;

-- Function to insert webhook into queue
CREATE OR REPLACE FUNCTION insert_webhook_to_queue()
RETURNS TRIGGER AS $$
DECLARE
  webhook_config JSONB;
  webhook_url TEXT;
  events_list TEXT[];
BEGIN
  -- Define webhook configurations
  webhook_config := jsonb_build_object(
    'railway_pipeline', jsonb_build_object(
      'url', COALESCE(
        current_setting('app.railway_pipeline_webhook_url', true),
        'https://pipeline-agents.railway.app/webhook'
      ),
      'events', ARRAY[
        'mcp:invoke',
        'mcp:response',
        'mcp:storeThought',
        'mcp:recordTaskContract',
        'mcp:getQualifiedAgents',
        'mcp:getAgentReputation',
        'mcp:updateTaskStatus',
        'TASK_REQUIREMENT',
        'TASK_CONTRACT',
        'BID_GENERATED',
        'BID_ACCEPTED',
        'TASK_COMPLETED',
        'chat_posted',
        'agent_reply',
        'invoke_sequential_thinking',
        'thought_appended',
        'test_webhook_event'
      ]
    )
  );

  -- Check each webhook configuration
  FOR webhook_url, events_list IN 
    SELECT 
      config->>'url' as url,
      ARRAY(SELECT jsonb_array_elements_text(config->'events')) as events
    FROM jsonb_each(webhook_config) AS configs(name, config)
  LOOP
    -- Check if this event should trigger this webhook
    IF NEW.event_name = ANY(events_list) THEN
      INSERT INTO webhook_queue (
        event_name,
        organisation_id,
        payload,
        webhook_url,
        status,
        attempts,
        created_at,
        next_retry_at
      ) VALUES (
        NEW.event_name,
        NEW.organisation_id,
        NEW.payload,
        webhook_url,
        'pending',
        0,
        NOW(),
        NOW()
      );
    END IF;
  END LOOP;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to capture bus events and queue webhooks
CREATE TRIGGER bus_events_to_webhook_queue
AFTER INSERT ON bus_events
FOR EACH ROW
EXECUTE FUNCTION insert_webhook_to_queue();

-- Function to capture realtime broadcast events (called via RPC)
CREATE OR REPLACE FUNCTION capture_bus_event(
  p_event_name TEXT,
  p_organisation_id TEXT,
  p_payload JSONB
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO bus_events (event_name, organisation_id, payload)
  VALUES (p_event_name, p_organisation_id, p_payload);
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT ALL ON webhook_queue TO service_role;
GRANT ALL ON bus_events TO service_role;
GRANT EXECUTE ON FUNCTION capture_bus_event TO service_role;
GRANT EXECUTE ON FUNCTION calculate_next_retry TO service_role;

-- Add RLS policies
ALTER TABLE webhook_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE bus_events ENABLE ROW LEVEL SECURITY;

-- Service role can do everything
CREATE POLICY "Service role has full access to webhook_queue" ON webhook_queue
  FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Service role has full access to bus_events" ON bus_events
  FOR ALL TO service_role USING (true) WITH CHECK (true);

-- Add comment explaining the system
COMMENT ON TABLE webhook_queue IS 'Queue for reliable webhook delivery with retry logic';
COMMENT ON TABLE bus_events IS 'Captures all bus events for webhook processing';
COMMENT ON FUNCTION capture_bus_event IS 'RPC function to capture bus events from edge functions or client code';