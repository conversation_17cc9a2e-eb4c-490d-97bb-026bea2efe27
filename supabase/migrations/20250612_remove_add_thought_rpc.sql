-- Remove the add_thought RPC function as we're using direct database operations
-- to avoid event duplication issues

-- Drop the function and its permissions
DROP FUNCTION IF EXISTS add_thought(<PERSON>UI<PERSON>, <PERSON>UID, TEXT, JSONB);

-- Add comment explaining the change
COMMENT ON TABLE thoughts IS 'Stores sequential thinking thoughts. Direct inserts are used instead of RPC to avoid event duplication.';