-- Migration: Fix critical event system issues
-- This migration addresses:
-- 1. Database schema error with c.default_model
-- 2. Ensures get_llm_config works properly

BEGIN;

-- First, let's check and fix the org_llm_credentials table structure
-- The table should not have a default_model column (that's in org_llm_settings)
DO $$
BEGIN
  -- Check if default_model column exists in org_llm_credentials and drop it
  IF EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'org_llm_credentials' 
    AND column_name = 'default_model'
  ) THEN
    ALTER TABLE org_llm_credentials DROP COLUMN default_model;
    RAISE NOTICE 'Dropped default_model column from org_llm_credentials';
  END IF;

  -- Check if temperature column exists in org_llm_credentials and drop it
  IF EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'org_llm_credentials' 
    AND column_name = 'temperature'
  ) THEN
    ALTER TABLE org_llm_credentials DROP COLUMN temperature;
    RAISE NOTICE 'Dropped temperature column from org_llm_credentials';
  END IF;
END $$;

-- Drop the problematic get_llm_config function that references c.default_model
DROP FUNCTION IF EXISTS public.get_llm_config(uuid);
DROP FUNCTION IF EXISTS public.get_llm_config(text);

-- Create a corrected get_llm_config function
CREATE OR REPLACE FUNCTION public.get_llm_config(p_org_id uuid)
RETURNS TABLE(provider text, api_key text, default_model text, openai_org_id text) 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  encrypted_key text;
  decrypted_key text;
  result_row RECORD;
BEGIN
  -- Get the data with proper joins
  SELECT 
    c.provider,
    c.api_key as encrypted_api_key,
    COALESCE(s.default_model, 'gpt-4') as default_model,
    c.openai_org_id
  INTO result_row
  FROM org_llm_credentials c
  LEFT JOIN org_llm_settings s 
    ON s.organisation_id = c.organisation_id 
    AND s.preferred_provider = c.provider
  WHERE c.organisation_id = p_org_id
  ORDER BY 
    CASE WHEN s.preferred_provider IS NOT NULL THEN 0 ELSE 1 END,
    c.created_at DESC
  LIMIT 1;
  
  -- If no result found, return NULL row
  IF NOT FOUND THEN
    RETURN QUERY SELECT NULL::text, NULL::text, NULL::text, NULL::text;
    RETURN;
  END IF;
  
  -- Try to decrypt the API key if it's encrypted
  BEGIN
    -- Check if the key looks encrypted (base64 and long)
    IF length(result_row.encrypted_api_key) > 100 AND result_row.encrypted_api_key ~ '^[A-Za-z0-9+/]+=*$' THEN
      -- Try to decrypt
      decrypted_key := decrypt_api_key(result_row.encrypted_api_key);
    ELSE
      -- Use as-is if not encrypted
      decrypted_key := result_row.encrypted_api_key;
    END IF;
  EXCEPTION WHEN OTHERS THEN
    -- If decryption fails, use the key as-is (might be plaintext)
    decrypted_key := result_row.encrypted_api_key;
  END;
  
  -- Return the result
  RETURN QUERY SELECT 
    result_row.provider,
    decrypted_key,
    result_row.default_model,
    result_row.openai_org_id;
END;
$$;

-- Create text parameter variant
CREATE OR REPLACE FUNCTION public.get_llm_config(p_org text)
RETURNS TABLE(provider text, api_key text, default_model text, openai_org_id text) 
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT * FROM get_llm_config(p_org::uuid);
$$;

-- Also create get_llm_config_v2 for backward compatibility
CREATE OR REPLACE FUNCTION public.get_llm_config_v2(p_org_id uuid)
RETURNS TABLE(provider text, api_key text, default_model text, openai_org_id text) 
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT * FROM get_llm_config(p_org_id);
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_llm_config(uuid) TO authenticated, service_role, anon;
GRANT EXECUTE ON FUNCTION public.get_llm_config(text) TO authenticated, service_role, anon;
GRANT EXECUTE ON FUNCTION public.get_llm_config_v2(uuid) TO authenticated, service_role, anon;

-- Add comments
COMMENT ON FUNCTION public.get_llm_config(uuid) IS 
'Retrieves LLM configuration with decrypted API key. Handles both encrypted and plaintext keys.';

COMMENT ON FUNCTION public.get_llm_config_v2(uuid) IS 
'Alias for get_llm_config for backward compatibility.';

-- Ensure the webhook queue trigger is properly set up for event-based communication
CREATE OR REPLACE FUNCTION public.post_chat()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_webhook_url text;
  v_org_id uuid;
  v_chat_id uuid;
  v_message text;
  v_user_id uuid;
BEGIN
  -- Extract values from NEW record
  v_org_id := NEW.organisation_id;
  v_chat_id := NEW.chat_id;
  v_message := NEW.message;
  v_user_id := NEW.user_id;

  -- Log the trigger execution
  RAISE LOG 'post_chat trigger: org_id=%, chat_id=%, message=%, user_id=%', 
    v_org_id, v_chat_id, v_message, v_user_id;

  -- Insert into bus_events for event-based processing
  INSERT INTO public.bus_events (
    event_type,
    channel_id,
    organisation_id,
    user_id,
    payload,
    created_at
  ) VALUES (
    'chat_posted',
    v_chat_id::text,
    v_org_id,
    v_user_id,
    jsonb_build_object(
      'chat_id', v_chat_id,
      'message', v_message,
      'organisation_id', v_org_id,
      'user_id', v_user_id,
      'timestamp', NEW.created_at
    ),
    NOW()
  );

  -- Also insert into webhook_queue for backward compatibility
  -- Get the webhook URL from agent_registry
  SELECT webhook_url INTO v_webhook_url
  FROM public.agent_registry
  WHERE organisation_id = v_org_id
    AND agent_type = 'master'
    AND is_active = true
  ORDER BY last_heartbeat DESC
  LIMIT 1;

  -- If we have a webhook URL, queue it
  IF v_webhook_url IS NOT NULL THEN
    INSERT INTO public.webhook_queue (
      event_name,
      organisation_id,
      payload,
      webhook_url,
      status,
      attempts,
      max_attempts,
      created_at,
      next_retry_at
    ) VALUES (
      'chat_posted',
      v_org_id,
      jsonb_build_object(
        'chat_id', v_chat_id,
        'message', v_message,
        'user_id', v_user_id,
        'timestamp', NEW.created_at
      ),
      v_webhook_url,
      'pending',
      0,
      3,
      NOW(),
      NOW()
    );
  END IF;

  RETURN NEW;
END;
$$;

-- Ensure the trigger exists
DROP TRIGGER IF EXISTS post_chat_trigger ON public.chat_log;
CREATE TRIGGER post_chat_trigger
  AFTER INSERT ON public.chat_log
  FOR EACH ROW
  EXECUTE FUNCTION public.post_chat();

-- Log completion
DO $$
BEGIN
  RAISE NOTICE 'Event system critical fixes applied successfully';
  RAISE NOTICE '1. Fixed get_llm_config to not reference non-existent c.default_model';
  RAISE NOTICE '2. Updated post_chat to publish events to bus_events table';
  RAISE NOTICE '3. Webhook queue still supported for backward compatibility';
END $$;

COMMIT;