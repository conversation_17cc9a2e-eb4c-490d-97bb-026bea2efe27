Risk ID,Risk Description,Risk Category (Technical/Market/Operational/Economic),Applies to Version(s),Probability (1-5),Impact (1-5),Risk Exposure (Prob * Impact),Mitigation Strategy,Owner,Status (Open/Mitigated/Closed)
R-01,Example: Complex database migration for V3 could lead to data loss or extended downtime.,Technical,V3,3,5,15,"Develop and test migration scripts in a staging environment; schedule migration during off-peak hours; prepare rollback plan.",Lead Dev,Open
R-02,,,,,,,,,
R-03,,,,,,,,,
R-04,,,,,,,,,
R-05,,,,,,,,,
