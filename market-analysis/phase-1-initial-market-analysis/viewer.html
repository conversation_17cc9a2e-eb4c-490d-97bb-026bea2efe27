<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VibeLaunch Market Analysis - Markdown Viewer</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
            overflow: hidden;
        }
        
        #sidebar {
            width: 300px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            overflow-y: auto;
            flex-shrink: 0;
        }
        
        #sidebar h2 {
            margin-top: 0;
            border-bottom: 2px solid #34495e;
            padding-bottom: 10px;
        }
        
        #sidebar ul {
            list-style: none;
            padding: 0;
        }
        
        #sidebar li {
            margin-bottom: 10px;
        }
        
        #sidebar a {
            color: #ecf0f1;
            text-decoration: none;
            display: block;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        #sidebar a:hover,
        #sidebar a.active {
            background: #34495e;
        }
        
        #content {
            flex: 1;
            padding: 40px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        #content-inner {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* Markdown content styling */
        #content-inner h1 {
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }
        
        #content-inner h2 {
            color: #34495e;
            margin-top: 30px;
        }
        
        #content-inner h3 {
            color: #34495e;
        }
        
        #content-inner code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', Courier, monospace;
        }
        
        #content-inner pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        
        #content-inner pre code {
            background: none;
            padding: 0;
        }
        
        #content-inner blockquote {
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-left: 0;
            color: #666;
        }
        
        #content-inner table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        
        #content-inner th,
        #content-inner td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        #content-inner th {
            background: #f4f4f4;
            font-weight: bold;
        }
        
        #content-inner tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        #error {
            color: #c53030;
            padding: 20px;
            background: #fff5f5;
            border: 1px solid #feb2b2;
            border-radius: 5px;
        }
        
        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }
            
            #sidebar {
                width: 100%;
                height: auto;
                max-height: 200px;
            }
            
            #content {
                padding: 20px;
            }
            
            #content-inner {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div id="sidebar">
        <h2>VibeLaunch Market Analysis</h2>
        <p style="margin-bottom: 20px; opacity: 0.9;">Economic Documentation</p>
        <ul id="file-list">
            <li><a href="#" onclick="loadMarkdown('PROJECT_OVERVIEW.md'); return false;" class="active">1. Project Overview</a></li>
            <li><a href="#" onclick="loadMarkdown('MARKET_COMPONENTS.md'); return false;">2. Market Components</a></li>
            <li><a href="#" onclick="loadMarkdown('DATA_FLOWS.md'); return false;">3. Data Flows</a></li>
            <li><a href="#" onclick="loadMarkdown('API_ENDPOINTS.md'); return false;">4. API Endpoints</a></li>
            <li><a href="#" onclick="loadMarkdown('CONFIGURATION_GUIDE.md'); return false;">5. Configuration Guide</a></li>
            <li><a href="#" onclick="loadMarkdown('DATABASE_SCHEMA.md'); return false;">6. Database Schema</a></li>
            <li><a href="#" onclick="loadMarkdown('EXTERNAL_INTEGRATIONS.md'); return false;">7. External Integrations</a></li>
            <li><a href="#" onclick="loadMarkdown('ECONOMIC_MODELS.md'); return false;">8. Economic Models</a></li>
            <li><a href="#" onclick="loadMarkdown('USER_FLOWS.md'); return false;">9. User Flows</a></li>
            <li><a href="#" onclick="loadMarkdown('GLOSSARY.md'); return false;">10. Glossary</a></li>
        </ul>
    </div>
    
    <div id="content">
        <div id="content-inner">
            <div id="error" style="display: none;"></div>
            <div id="markdown-content">
                <h1>Loading...</h1>
                <p>Please select a document from the sidebar.</p>
            </div>
        </div>
    </div>
    
    <script>
        let currentFile = 'PROJECT_OVERVIEW.md';
        
        function loadMarkdown(filename) {
            // Update active state
            document.querySelectorAll('#sidebar a').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update current file
            currentFile = filename;
            
            // Show loading state
            document.getElementById('markdown-content').innerHTML = '<h1>Loading...</h1>';
            document.getElementById('error').style.display = 'none';
            
            // Fetch and render the markdown file
            fetch(filename)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('File not found');
                    }
                    return response.text();
                })
                .then(text => {
                    // Parse and render markdown
                    const html = marked.parse(text);
                    document.getElementById('markdown-content').innerHTML = html;
                    
                    // Scroll to top
                    document.getElementById('content').scrollTop = 0;
                })
                .catch(error => {
                    document.getElementById('error').style.display = 'block';
                    document.getElementById('error').innerHTML = `
                        <h3>Error Loading File</h3>
                        <p>Could not load ${filename}</p>
                        <p>Make sure you're viewing this file through a web server or use a browser that allows local file access.</p>
                        <p>Alternatively, you can:</p>
                        <ul>
                            <li>Use VS Code's Live Server extension</li>
                            <li>Run <code>python -m http.server</code> in this directory</li>
                            <li>Use any local web server</li>
                        </ul>
                    `;
                    document.getElementById('markdown-content').innerHTML = '';
                });
        }
        
        // Load the first document on page load
        window.onload = function() {
            loadMarkdown('PROJECT_OVERVIEW.md');
        };
    </script>
</body>
</html>