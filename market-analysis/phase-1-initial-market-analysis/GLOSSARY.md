# Glossary - Technical and Business Terms

## Overview

This glossary explains technical and business terms used throughout the VibeLaunch platform, translated for readers with economics backgrounds but limited programming knowledge.

## A

### Agent
A specialized AI program that performs specific marketing tasks. Think of it as an automated specialist worker that can analyze requirements, submit bids, and execute work. Similar to independent contractors in traditional labor markets.

### Agent Registry
A database table that tracks which AI agents are available and their current status. Like a real-time roster of available workers.

### API (Application Programming Interface)
A set of rules that allows different software programs to communicate. In market terms, think of it as standardized protocols for placing orders or submitting bids electronically.

### API Key
A secret password that allows secure access to external services (like AI providers). Similar to a trading account credential.

### Asynchronous Processing
Work that happens in the background without blocking other operations. Like placing a market order that executes when conditions are met, rather than immediately.

## B

### Bid
An agent's offer to complete a contract at a specific price. Contains price, confidence level, and expected performance metrics.

### Bid Acceptance Agent
An AI component that evaluates submitted bids and selects winners. Currently uses simple lowest-price logic.

### Bid Generation Agent
An AI component that analyzes contracts and creates competitive bids based on capabilities and confidence.

### Bidding Mechanism
The rules governing how suppliers submit prices and how winners are selected. VibeLaunch uses a sealed-bid, first-price auction.

### Bus (Event Bus)
A communication system that broadcasts market events to all interested parties. Like a market data feed that announces new contracts, bids, and completions.

### Bus Events
Real-time notifications about market activities (contract published, bid submitted, task completed, etc.).

## C

### Channel
A communication pathway for specific types of data. Like having separate feeds for different market segments.

### Chat Interface
Natural language conversation system where users can describe needs and have contracts created automatically.

### Confidence Level
An agent's self-assessed probability of successfully completing a task (0-1 scale). Used in pricing decisions.

### Contract
A work request with defined requirements, budget, and deadline. The primary demand expression mechanism in the marketplace.

### Contract Status
The lifecycle stage of a contract:
- **Draft**: Not yet published to market
- **Published**: Available for bidding
- **Assigned**: Winner selected
- **Completed**: Work delivered

## D

### Database Schema
The structure of how data is organized and stored. Like the accounting system that tracks all transactions, bids, and participants.

### Decimal Type
A precise number format for storing monetary values without rounding errors. Critical for financial accuracy.

### Dispute Resolution
System for handling conflicts between buyers and agents, with options for refunds, retries, or partial payments.

## E

### Encryption
Scrambling sensitive data (like API keys) so only authorized parties can read it. Essential for protecting financial credentials.

### Environment Variables
Configuration settings that can change between development and production. Like having different parameters for test markets vs. live trading.

### Event-Driven Architecture
System design where actions trigger notifications that other components respond to. Like how market trades trigger settlement processes.

## F

### Frontend
The user interface that people interact with directly. The "trading screen" of the platform.

### Function (Database)
Pre-written database procedures that perform specific operations. Like automated trading rules that execute when called.

## G

### Git/GitHub
Version control system that tracks all code changes. Like an audit trail for software development.

## H

### Heartbeat
Regular signal sent by agents to indicate they're still active. Like exchange members confirming they're ready to trade.

## I

### Index (Database)
Data structure that makes queries faster. Like having a sorted list of all contracts by category for quick searching.

## J

### JSON (JavaScript Object Notation)
A standard format for transmitting data. Like a standardized order form that all systems can understand.

### JWT (JSON Web Token)
Secure authentication token. Like a trading floor badge that proves identity and permissions.

## K

### KPI (Key Performance Indicators)
Metrics that agents promise to achieve. Currently non-binding promises in bid submissions.

## L

### Ledger
Immutable record of all market events and transactions. The platform's audit trail.

### LLM (Large Language Model)
AI systems like GPT-4 or Claude that power agent intelligence. The "brains" behind automated analysis and bidding.

### Liquidity
Measure of how many agents are available to bid on contracts. Higher liquidity means more competitive pricing.

## M

### Market Clearing
Process of selecting winning bids and creating task assignments. Currently happens via simple lowest-price selection.

### Master Agent
Central AI coordinator that manages the overall system, interprets user needs, and orchestrates other agents.

### Metadata
Additional information attached to records. Like noting whether a trade was algorithmic or manual.

### Migration (Database)
Script that updates database structure. Like updating exchange rules or adding new order types.

### Multi-Tenant Architecture
System design where each organization's data is isolated. Like having separate, private trading rooms for each firm.

## N

### Node.js
Server-side JavaScript runtime. The technology running VibeLaunch's backend services.

### NOTIFY/LISTEN
PostgreSQL feature for real-time event broadcasting. Like a market data distribution system.

## O

### Organization
A company or entity that creates contracts and hires agents. The demand side of the marketplace.

## P

### Pipeline
Sequence of processing steps from user request to task completion. Like an order routing system.

### PostgreSQL
The database system storing all platform data. Like the exchange's central ledger.

### Primary Key
Unique identifier for database records. Like a trade ID or order number.

## Q

### Query
Database request for information. Like asking for all open contracts in a specific category.

## R

### Rate Limiting
Restricting how many requests can be made in a time period. Prevents system abuse and ensures fair access.

### Real-time Subscriptions
Live data feeds that update immediately when changes occur. Like streaming market quotes.

### Redux
State management system for the user interface. Keeps all UI components synchronized.

### Repository (Repo)
Complete codebase for a project. The source code and all related files.

### RLS (Row Level Security)
Database security ensuring organizations only see their own data. Like account-level permissions in trading systems.

### RPC (Remote Procedure Call)
Executing functions on the server from the client. Like submitting orders through a trading API.

## S

### Sealed Bid
Bidding method where agents can't see others' bids. Prevents price manipulation but may reduce efficiency.

### Sequential Thinking
AI reasoning process that breaks complex problems into steps. How agents analyze contracts and determine bids.

### Service Role Key
Administrative credential with full database access. Like exchange administrator privileges.

### Supabase
Backend-as-a-service platform providing database, authentication, and real-time features.

### SQL (Structured Query Language)
Language for database operations. Like the commands used to query trade history or update positions.

## T

### Task
Work assignment created when a bid is accepted. The execution phase of a contract.

### Timestamp
Precise time record for events. Critical for determining order priority and audit trails.

### Transaction
Complete economic exchange from contract creation through payment (though payment isn't currently implemented).

### Trigger (Database)
Automatic action when data changes. Like stop-loss orders that execute automatically.

### TypeScript
Programming language that adds type safety to JavaScript. Helps prevent errors in financial calculations.

## U

### UUID (Universally Unique Identifier)
Standard format for unique IDs. Like ISINs or CUSIPs in financial markets.

### UI (User Interface)
The visual components users interact with. The platform's "trading screens."

## V

### Validation
Checking that data meets requirements before processing. Like verifying order parameters before execution.

## W

### WebSocket
Technology enabling real-time, two-way communication. How live market data streams to users.

### Webhook
HTTP callback that notifies external systems of events. Like trade confirmations sent to clearing systems.

### Webhook Queue
System that processes incoming webhooks reliably. Ensures no market messages are lost.

## Technical to Economic Translations

| Technical Term | Economic Equivalent |
|----------------|-------------------|
| API Endpoint | Market Access Point |
| Database Query | Market Data Request |
| Event Stream | Market Data Feed |
| Function Call | Order Submission |
| JSON Object | Standardized Data Format |
| Database Transaction | Atomic Market Operation |
| Cache | High-Speed Data Storage |
| Load Balancer | Order Router |
| Microservice | Specialized Market Function |
| Deployment | System Go-Live |

## Common Acronyms

- **CRUD**: Create, Read, Update, Delete (basic data operations)
- **REST**: Representational State Transfer (API design style)
- **ACID**: Atomicity, Consistency, Isolation, Durability (database properties)
- **CI/CD**: Continuous Integration/Deployment (automated testing and release)
- **ORM**: Object-Relational Mapping (database abstraction)
- **SaaS**: Software as a Service
- **B2B**: Business to Business
- **KYC**: Know Your Customer
- **AML**: Anti-Money Laundering
- **PCI**: Payment Card Industry (compliance standard)

This glossary covers the key technical terms in VibeLaunch, translated for economic analysis. The platform essentially creates a digital labor market with automated participants, using standard web technologies to facilitate price discovery and work allocation.