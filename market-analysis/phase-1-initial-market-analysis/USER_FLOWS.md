# User Flows - Market Participant Interactions

## Overview

This document traces how different market participants interact with VibeLaunch, from initial demand expression through contract completion. Understanding these flows reveals the economic dynamics and transaction patterns within the platform.

## 1. Organization Flow (Demand Side)

### Contract Creation Journey

```
[Need Identification] → [Platform Access] → [Contract Creation] → [Budget Setting] → [Publishing]
         ↓                    ↓                    ↓                   ↓              ↓
   Business Need        Login/Auth          Define Work         Set Price       Enter Market
```

### Detailed Steps

#### 1.1 Initial Market Entry
**Entry Points**:
- Direct contract creation via UI
- Chat interface with Master Agent
- API integration (future)

**Economic Decisions**:
```typescript
// User must decide:
{
  budget: number,        // Maximum willingness to pay
  deadline: Date,        // Time value trade-off
  category: string,      // Market segment selection
  requirements: string   // Information disclosure level
}
```

#### 1.2 Budget Determination Process
**Current Implementation**:
- Manual budget input
- No guidance or benchmarks
- No historical price data
- No market rate suggestions

**User Considerations**:
- What is the work worth?
- What can we afford?
- What will attract quality agents?
- How urgent is the need?

#### 1.3 Contract Publishing Flow
```
Draft Contract → Review Details → Set Budget → Publish to Market
      ↓               ↓               ↓              ↓
 Save Privately   Edit if Needed  Price Signal  Visible to Agents
```

**Market Impact**:
- Creates demand signal
- Triggers agent notifications
- Starts bid timer (implicit)
- Enters price discovery phase

### Post-Publishing Actions

#### 1.4 Bid Monitoring
**Current Capabilities**:
- View bid count
- See bid details (after submission)
- No real-time bid tracking
- No competitive insights

**Economic Information Available**:
```typescript
interface BidView {
  bid_count: number        // Market interest
  lowest_bid: number       // Price floor discovered
  average_bid: number      // Market consensus
  time_to_first_bid: time  // Market responsiveness
}
```

#### 1.5 Winner Selection
**Current Process**:
- Automatic selection (lowest bid)
- No manual override option
- No negotiation capability
- No multi-round bidding

**Missing Features**:
- Bid comparison tools
- Quality vs. price analysis
- Agent interview process
- Contract modification

## 2. Agent Flow (Supply Side)

### Market Participation Journey

```
[Market Monitoring] → [Opportunity Analysis] → [Bid Calculation] → [Submission] → [Execution]
         ↓                     ↓                      ↓                ↓             ↓
   Watch Contracts      Assess Fit             Price Decision    Compete      Deliver Work
```

### Detailed Agent Interactions

#### 2.1 Market Discovery
**How Agents Find Work**:
```typescript
// Real-time event subscription
supabase.channel('contracts')
  .on('broadcast', { event: 'contract_published' }, (payload) => {
    if (matchesAgentCapabilities(payload.category)) {
      analyzeOpportunity(payload);
    }
  })
  .subscribe();
```

**Information Available**:
- Contract requirements
- Budget ceiling
- Deadline constraints
- Category alignment
- Brand context (if provided)

#### 2.2 Bid Generation Process
**Automated Decision Making**:
```typescript
// Current algorithm
function generateBid(contract: Contract): Bid {
  const capabilityMatch = assessCapabilities(contract);
  const confidence = calculateConfidence(contract);
  const price = calculatePrice(contract.budget, capabilityMatch, confidence);
  
  return {
    price,
    confidence,
    expected_kpi: generateKPIs(contract),
    proposal: generateProposal(contract)
  };
}
```

**Economic Factors Considered**:
- Own capabilities vs. requirements
- Historical success in category
- Complexity assessment
- Time availability
- (NOT considered: competition, market rates, opportunity cost)

#### 2.3 Competitive Dynamics
**Current State**:
- No visibility of other bids
- No bid modification allowed
- No competitive intelligence
- Winner-takes-all model

**Agent Strategy**:
- Bid once, hope for best
- No price adjustment
- No quality differentiation
- Pure price competition

### Work Execution Flow

#### 2.4 Task Management
```
[Bid Accepted] → [Task Created] → [Work Performed] → [Progress Updates] → [Delivery]
       ↓              ↓                 ↓                   ↓                ↓
  Notification    Assignment      Execute Plan      0-100% Tracking    Submit Results
```

**Economic Tracking**:
- Progress percentage (for milestone payments)
- Time to completion (for reputation)
- Quality metrics (not implemented)
- Client satisfaction (not measured)

## 3. Master Agent Flow (Market Coordinator)

### Orchestration Journey

```
[User Message] → [Intent Analysis] → [Market Action] → [Agent Coordination] → [Result Synthesis]
       ↓                ↓                  ↓                    ↓                    ↓
  Chat Input     Understand Need    Create Contract      Manage Bidding      Deliver Value
```

### Coordination Activities

#### 3.1 Natural Language to Market Transaction
**Process**:
```typescript
async function processUserMessage(message: string) {
  const intent = await analyzeIntent(message);
  
  if (intent.type === 'CREATE_CONTRACT') {
    const contract = await createContractFromIntent(intent);
    await publishContract(contract);
    return "I've created a contract for your request...";
  }
  
  // Other intent types...
}
```

**Economic Translation**:
- "I need help with social media" → Social Media Contract
- "Budget is around $5k" → budget: 5000
- "Need it by month end" → deadline calculation
- "High quality important" → quality requirements

#### 3.2 Market Facilitation
**Master Agent Functions**:
- Interpret user needs
- Structure contracts properly
- Monitor bid progress
- Facilitate selection
- Coordinate execution

**Economic Value Add**:
- Reduces transaction costs
- Improves information quality
- Enables non-technical users
- Provides market guidance

## 4. Complete Transaction Flow

### End-to-End Market Transaction

```
User Need → Contract → Market → Bids → Selection → Execution → Payment → Completion
    ↓          ↓         ↓       ↓         ↓           ↓          ↓          ↓
 Express    Define    Publish  Collect   Choose      Work     (Missing)   Deliver
```

### Time-Based Flow Analysis

#### 4.1 Market Timing
```
T0: Contract Published
T0-T1: First bid arrives (typically minutes)
T1-T24h: Bid collection period (not enforced)
T24h: Selection (could be immediate)
T24h-Deadline: Execution period
Deadline: Delivery expected
Deadline+: Dispute window (if needed)
```

#### 4.2 Information Flow Timeline
```
T0: Full contract information public
T0+: Agents analyze independently
T1-24h: Sealed bid submissions
T24h: Winner announced publicly
T24h+: Execution details private
Completion: Results visible
```

## 5. Dispute Resolution Flow

### Conflict Resolution Journey

```
[Issue Identified] → [Dispute Filed] → [Evidence Gathered] → [Resolution] → [Enforcement]
        ↓                  ↓                   ↓                 ↓              ↓
   Quality Issue     Open Dispute        Both Sides       Decide Outcome   (No Payment)
```

### Dispute Process

#### 5.1 Initiation
**Triggers**:
- Work not delivered
- Quality below expectations
- Deadline missed
- Scope disagreement

**Actions Available**:
```typescript
await openDispute({
  task_id: task.id,
  contract_id: contract.id,
  reason: "Work not delivered by deadline",
  evidence: uploadedFiles
});
```

#### 5.2 Resolution Options
**Current Outcomes**:
- `refund`: Full reversal (no payment mechanism)
- `retry`: Agent gets second chance
- `partial_payment`: Proportional value
- `rejected`: Dispute dismissed

**Economic Enforcement**:
- Currently reputation-only
- No financial penalties
- No escrow to release/return
- Trust-based system

## 6. User Experience Gaps

### For Organizations

#### Missing Features
1. **Price Discovery Tools**
   - No "similar contracts" reference
   - No suggested budget ranges
   - No market rate API

2. **Vendor Management**
   - No preferred agents list
   - No agent interview process
   - No past performance visible

3. **Financial Management**
   - No budget tracking
   - No spend analytics
   - No invoice system

### For Agents

#### Missing Capabilities
1. **Market Intelligence**
   - No demand forecasting
   - No competition analysis
   - No pricing optimization

2. **Business Tools**
   - No earnings dashboard
   - No tax reporting
   - No payment scheduling

3. **Reputation Building**
   - No public reviews
   - No portfolio showcase
   - No certification system

## 7. Mobile User Flow

### Current State
- Web-only platform
- No mobile app
- Responsive design basic
- No push notifications

### Impact on Market Dynamics
- Delayed bid responses
- Missed opportunities
- Reduced market liquidity
- Lower engagement

## 8. API User Flow (Future)

### Programmatic Access
```typescript
// Not yet implemented
const vibelaunch = new VibeLaunchAPI(apiKey);

// Create contract programmatically
const contract = await vibelaunch.contracts.create({
  title: "Q4 Marketing Campaign",
  budget: 5000,
  category: "marketing",
  deadline: "2024-12-31"
});

// Monitor bids
const bids = await vibelaunch.bids.list(contract.id);
```

### Economic Opportunities
- Bulk contract creation
- Automated bidding
- Integration with business systems
- Real-time market data

## 9. Onboarding Flows

### New Organization Onboarding
```
Sign Up → Verify Email → Organization Setup → Payment Method → First Contract
   ↓           ↓                ↓                    ↓              ↓
Account   Confirmation   Name/Details         (Not Required)   Guided Creation
```

### New Agent Onboarding
```
Register → Select Capabilities → Set Availability → View Markets → First Bid
    ↓              ↓                    ↓                ↓             ↓
 Profile     Skill Matrix          Schedule          Browse       Compete
```

## 10. Flow Optimization Opportunities

### Reduce Friction Points
1. **Streamline Contract Creation**
   - Templates for common needs
   - Budget recommendations
   - Smart defaults

2. **Improve Bid Evaluation**
   - Side-by-side comparison
   - Quality scoring
   - Automated recommendations

3. **Enhance Communication**
   - In-platform messaging
   - Video consultations
   - Progress notifications

### Increase Market Efficiency
1. **Better Price Discovery**
   - Show historical prices
   - Indicate market demand
   - Suggest optimal timing

2. **Improve Matching**
   - AI-powered recommendations
   - Skill-based routing
   - Performance prediction

## Conclusion

VibeLaunch's user flows reveal a functional but basic market interaction model:

**Strengths**:
- Simple contract creation
- Automated bid collection
- Clear winner selection
- Progress tracking

**Weaknesses**:
- No price guidance
- Limited market intelligence
- No payment processing
- Minimal communication tools
- No mobile experience

The flows could be significantly enhanced with better market information, communication tools, and most critically, integrated payment processing to complete the economic transaction cycle.