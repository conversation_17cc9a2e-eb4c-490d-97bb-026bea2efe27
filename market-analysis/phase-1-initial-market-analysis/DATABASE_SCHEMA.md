# Database Schema - Market Data Storage Analysis

## Overview

The database schema reveals how VibeLaunch stores and manages economic data. This analysis examines tables related to market operations, pricing, and financial tracking.

## 1. Core Market Tables

### contracts
**Purpose**: Stores demand-side market entries

```sql
CREATE TABLE contracts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id UUID NOT NULL,
  category TEXT NOT NULL,           -- Market segment
  title TEXT NOT NULL,
  brief TEXT NOT NULL,              -- Requirement details
  spec JSONB,                       -- Detailed specifications
  deadline TIMESTAMPTZ NOT NULL,    -- Time constraint
  budget DECIMAL(10, 2) NOT NULL,   -- Maximum price (ceiling)
  created_by UUID,                  -- Demand originator
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  status TEXT DEFAULT 'draft',      -- Market lifecycle stage
  brand_context_url TEXT            -- Additional context
);
```

**Economic Fields**:
- `budget`: Reservation price/willingness to pay
- `category`: Market segmentation variable
- `deadline`: Affects urgency and pricing
- `status`: Market state (draft → published → assigned → completed)

**Market States**:
- `draft`: Not yet in market
- `published`: Active, accepting bids
- `assigned`: Bid accepted, market cleared
- `completed`: Transaction finished

### bids
**Purpose**: Stores supply-side price offers

```sql
CREATE TABLE bids (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  contract_id UUID REFERENCES contracts(id),
  agent_role TEXT NOT NULL,         -- Supplier identifier
  price DECIMAL(10, 2) NOT NULL,    -- Offered price
  status bid_status DEFAULT 'pending',
  confidence DECIMAL(5, 4) NOT NULL, -- Self-assessed success probability
  expected_kpi JSONB,               -- Performance promises
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  task_id UUID REFERENCES tasks(id) -- Link to execution
);
```

**Pricing Mechanics**:
- `price`: Must be ≤ contract.budget
- `confidence`: 0.0000 to 1.0000 precision
- `expected_kpi`: Non-binding quality promises
- `status`: pending → accepted/rejected

**Market Dynamics**:
- Sealed bid auction (no visibility of others)
- First-price mechanism (pay what you bid)
- No bid modifications after submission

### tasks
**Purpose**: Tracks work execution post-market clearing

```sql
CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  status task_status DEFAULT 'draft',
  progress INTEGER DEFAULT 0,        -- 0-100 completion
  progress_message TEXT,
  result JSONB,                      -- Delivery details
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  completed_at TIMESTAMPTZ,
  artifact_url TEXT                  -- Deliverable location
);
```

**Economic Tracking**:
- `progress`: Enables milestone-based payments
- `completed_at`: Transaction completion time
- `result`: Quality/performance data

## 2. Market Performance Tables

### agent_scores
**Purpose**: Reputation and performance tracking

```sql
CREATE TABLE agent_scores (
  agent_role TEXT,
  organisation_id UUID,
  success_rate NUMERIC,             -- Historical performance
  avg_confidence NUMERIC,           -- Prediction accuracy
  task_count INTEGER,               -- Experience level
  total_revenue NUMERIC,            -- Lifetime earnings
  PRIMARY KEY (agent_role, organisation_id)
);
```

**Reputation Economics**:
- `success_rate`: Quality signal for buyers
- `total_revenue`: Market success indicator
- `task_count`: Experience proxy
- Per-organization tracking (no global reputation)

### agent_registry
**Purpose**: Supply-side capacity tracking

```sql
CREATE TABLE agent_registry (
  agent_role TEXT PRIMARY KEY,
  status agent_status DEFAULT 'idle',  -- idle/running/error
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

**Market Liquidity**:
- Tracks available suppliers
- Could enable capacity pricing
- Currently binary (available/busy)

## 3. Transaction Audit Tables

### ledger
**Purpose**: Immutable transaction history

```sql
CREATE TABLE ledger (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id UUID NOT NULL,
  event_type TEXT NOT NULL,         -- Economic event type
  entity_id UUID,                   -- Reference to contract/bid/task
  entity_type TEXT,                 -- 'contract'/'bid'/'task'
  data JSONB NOT NULL,              -- Complete event data
  user_id UUID,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

**Audit Trail**:
- `event_type`: 'contract_published', 'bid_submitted', etc.
- `data`: Preserves historical prices
- Enables market analysis
- Supports dispute resolution

### bus_events
**Purpose**: Real-time market event stream

```sql
CREATE TABLE bus_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  channel TEXT NOT NULL,            -- Distribution channel
  event TEXT NOT NULL,              -- Event type
  organisation_id UUID,
  payload JSONB,                    -- Event data
  created_at TIMESTAMPTZ DEFAULT now()
);
```

**Market Transparency**:
- Real-time price discovery
- Competitive dynamics
- Performance feedback

## 4. Financial Tables (Missing)

### Not Implemented
```sql
-- These tables would be needed for real economy:

CREATE TABLE payments (
  id UUID PRIMARY KEY,
  contract_id UUID REFERENCES contracts(id),
  amount DECIMAL(10, 2),
  currency TEXT DEFAULT 'USD',
  status TEXT,  -- pending/completed/failed/refunded
  processor TEXT,  -- stripe/paypal
  processor_id TEXT,
  created_at TIMESTAMPTZ
);

CREATE TABLE platform_fees (
  id UUID PRIMARY KEY,
  payment_id UUID REFERENCES payments(id),
  fee_amount DECIMAL(10, 2),
  fee_percentage DECIMAL(5, 4),
  collected_at TIMESTAMPTZ
);

CREATE TABLE agent_earnings (
  id UUID PRIMARY KEY,
  agent_role TEXT,
  payment_id UUID REFERENCES payments(id),
  gross_amount DECIMAL(10, 2),
  platform_fee DECIMAL(10, 2),
  net_amount DECIMAL(10, 2),
  status TEXT  -- pending/available/withdrawn
);
```

## 5. Dispute Resolution Tables

### disputes (Implied from code)
**Purpose**: Quality assurance and conflict resolution

```sql
-- Inferred structure from dispute-resolution.ts
CREATE TABLE disputes (
  id UUID PRIMARY KEY,
  organisation_id UUID,
  task_id UUID REFERENCES tasks(id),
  contract_id UUID REFERENCES contracts(id),
  status TEXT,  -- open/in_progress/resolved/rejected
  reason TEXT,
  opened_by UUID,
  opened_at TIMESTAMPTZ,
  assigned_to UUID,
  resolution TEXT,
  resolution_type TEXT,  -- refund/retry/partial_payment/rejected
  resolved_at TIMESTAMPTZ
);
```

**Economic Enforcement**:
- `resolution_type`: Determines financial outcome
- Currently no payment reversal mechanism
- Reputation impact only

## 6. Multi-Tenant Architecture

### organizations
**Purpose**: Market segmentation

```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  settings JSONB,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

**Market Isolation**:
- Each organization is a separate market
- No cross-organization price discovery
- Limits network effects

### Row-Level Security
```sql
-- Example RLS policy
CREATE POLICY contracts_isolation ON contracts
  USING (organisation_id IN (
    SELECT org_id FROM user_organisations 
    WHERE user_id = auth.uid()
  ));
```

**Economic Impact**:
- Prevents market manipulation
- Ensures data privacy
- Limits market efficiency

## 7. Performance Optimization

### Indexes for Market Queries
```sql
CREATE INDEX idx_contracts_organisation_id ON contracts(organisation_id);
CREATE INDEX idx_contracts_status ON contracts(status);
CREATE INDEX idx_contracts_category ON contracts(category);
CREATE INDEX idx_bids_contract_id ON bids(contract_id);
CREATE INDEX idx_bids_status ON bids(status);
CREATE INDEX idx_tasks_status ON tasks(status);
```

**Query Performance**:
- Fast market searches
- Efficient bid aggregation
- Quick status filtering

## 8. Data Integrity Constraints

### Business Rules in Database
```sql
-- Bid must not exceed budget (should exist)
ALTER TABLE bids 
  ADD CONSTRAINT bid_price_check 
  CHECK (price > 0);

-- Confidence must be valid probability
ALTER TABLE bids 
  ADD CONSTRAINT confidence_range 
  CHECK (confidence >= 0 AND confidence <= 1);

-- Task progress constraint
ALTER TABLE tasks 
  ADD CONSTRAINT progress_range 
  CHECK (progress >= 0 AND progress <= 100);
```

## 9. Missing Economic Features

### Price History
```sql
-- Not implemented but needed:
CREATE TABLE price_history (
  id UUID PRIMARY KEY,
  category TEXT,
  date DATE,
  avg_budget DECIMAL(10, 2),
  avg_winning_bid DECIMAL(10, 2),
  total_contracts INTEGER,
  total_value DECIMAL(12, 2)
);
```

### Market Depth
```sql
-- Would show supply/demand:
CREATE TABLE market_depth (
  category TEXT,
  timestamp TIMESTAMPTZ,
  active_contracts INTEGER,
  pending_bids INTEGER,
  available_agents INTEGER,
  avg_response_time INTERVAL
);
```

## 10. Data Retention and Archival

### Current State
- No data retention policies
- All data kept indefinitely
- No archival strategy

### Economic Considerations
- Historical data valuable for pricing
- Compliance may require retention
- Storage costs will grow

## Schema Evolution Recommendations

### For Payment Processing
1. Add `payments` table with processor integration
2. Add `platform_fees` for revenue tracking
3. Add `agent_earnings` for supplier accounting
4. Add `withdrawals` for cash-out tracking

### For Market Intelligence
1. Add `price_indices` for category benchmarks
2. Add `market_metrics` for real-time stats
3. Add `agent_rankings` for public reputation
4. Add `forecast_data` for predictive pricing

### For Scalability
1. Partition large tables by date/organization
2. Add materialized views for analytics
3. Implement data archival policies
4. Add caching tables for hot data

## Conclusion

The database schema supports a sophisticated task allocation system with market-like features:

**Strengths**:
- Comprehensive audit trail
- Flexible JSON storage
- Multi-tenant isolation
- Performance indexing

**Weaknesses**:
- No payment tables
- No financial tracking
- Limited market analytics
- No cross-org data sharing

The schema could evolve into a full economic platform with additions focused on financial data, market intelligence, and performance optimization.