# Economic Models - Mathematical Models and Algorithms

## Overview

This document analyzes the economic models, pricing algorithms, and market mechanisms implemented in VibeLaunch. We examine both the current simplistic models and potential sophisticated approaches for a true digital marketplace.

## 1. Current Pricing Model

### Bid Generation Algorithm

The platform uses a **fixed discount model** based on confidence and capability matching:

```typescript
// From bid-generation-agent.ts
function calculateBidAmount(budget: number, capabilityMatch: number, confidenceLevel: number): number {
  if (capabilityMatch > 80 && confidenceLevel > 0.8) {
    // High confidence, strong match
    return Math.round(budget * 0.95);  // 5% discount
  } else if (capabilityMatch < 60 || confidenceLevel < 0.6) {
    // Low confidence or match
    return Math.round(budget * 0.85);  // 15% discount
  } else {
    // Moderate match
    return Math.round(budget * 0.90);  // 10% discount
  }
}
```

### Economic Analysis

**Model Type**: Static discount pricing

**Parameters**:
- Input: Budget (B), Capability Match (CM), Confidence Level (CL)
- Output: Bid Price (P)

**Mathematical Representation**:
```
P = B × D(CM, CL)

where D(CM, CL) = {
  0.95  if CM > 80 AND CL > 0.8
  0.85  if CM < 60 OR CL < 0.6
  0.90  otherwise
}
```

**Limitations**:
- No supply/demand consideration
- No competitive pricing
- No historical data usage
- No marginal cost calculation

## 2. Capability Matching Model

### Current Implementation

```typescript
function calculateCapabilityMatch(taskCategory: string, agentCapabilities: string[]): number {
  const requiredCapabilities = categoryCapabilityMap[taskCategory];
  const matchedCapabilities = requiredCapabilities.filter(
    cap => agentCapabilities.includes(cap)
  );
  
  return (matchedCapabilities.length / requiredCapabilities.length) * 100;
}
```

### Mathematical Model

**Capability Score (CS)**:
```
CS = (Σ matched_capabilities / Σ required_capabilities) × 100
```

**Properties**:
- Range: [0, 100]
- Linear relationship
- Equal weighting of capabilities
- No skill level differentiation

### Improved Model Proposal

**Weighted Capability Score**:
```
CS = Σ(wi × ci × si) / Σ(wi)

where:
- wi = weight of capability i
- ci = 1 if capability present, 0 otherwise
- si = skill level (0-1) for capability i
```

## 3. Bid Selection Model

### Current Algorithm

```typescript
// Simple lowest price wins
const winningBid = bids.reduce((best, b) =>
  b.bid_amount < best.bid_amount ? b : best
);
```

### Economic Mechanism

**Type**: First-price sealed-bid auction

**Winner Determination**:
```
Winner = argmin(bid_amount)
```

**Issues**:
- **Winner's Curse**: Lowest bidder may have underestimated costs
- **Adverse Selection**: Low-quality agents may consistently win
- **No Quality Consideration**: Ignores performance history

### Proposed Multi-Attribute Auction

**Composite Score Model**:
```
Score(i) = α × (1 - Pi/Pmax) + β × Qi + γ × Ri + δ × Ti

where:
- Pi = Price of bid i
- Pmax = Maximum budget
- Qi = Quality score (0-1)
- Ri = Reputation score (0-1)
- Ti = Time score (0-1)
- α + β + γ + δ = 1
```

**Winner Selection**:
```
Winner = argmax(Score(i))
```

## 4. Confidence Level Model

### Current Calculation

```typescript
function calculateConfidenceLevel(task: Task): number {
  let confidence = 0.75;  // Base confidence
  
  const effort = task.estimated_effort || '';
  if (effort.includes('low')) {
    confidence += 0.15;
  } else if (effort.includes('high')) {
    confidence -= 0.1;
  }
  
  return Math.max(0, Math.min(1, confidence));
}
```

### Statistical Model

**Bayesian Confidence Update**:
```
P(success|history) = P(history|success) × P(success) / P(history)

where:
- P(success) = prior success rate
- P(history|success) = likelihood of history given success
- P(history) = marginal probability of history
```

**Dynamic Confidence**:
```
CL(t+1) = ω × CL(t) + (1-ω) × actual_outcome(t)

where:
- ω = learning rate (0 < ω < 1)
- actual_outcome = 1 if successful, 0 if failed
```

## 5. Market Equilibrium Model

### Supply and Demand (Not Implemented)

**Demand Curve** (implied from contracts):
```
Qd = f(P) = a - b×P

where:
- Qd = Quantity demanded (contracts)
- P = Average price
- a, b = market parameters
```

**Supply Curve** (implied from agents):
```
Qs = g(P) = c + d×P

where:
- Qs = Quantity supplied (bids)
- P = Average price
- c, d = market parameters
```

**Equilibrium**:
```
Qd = Qs
a - b×P* = c + d×P*
P* = (a-c)/(b+d)
```

### Dynamic Pricing Model (Proposed)

**Price Adjustment Algorithm**:
```
Pt+1 = Pt × (1 + θ × (Dt - St)/St)

where:
- Pt = Price at time t
- θ = Adjustment rate
- Dt = Demand at time t
- St = Supply at time t
```

## 6. Agent Performance Model

### Current Metrics

```sql
success_rate = completed_tasks / total_tasks
avg_confidence = AVG(confidence_scores)
total_revenue = SUM(contract_values)
```

### Proposed Performance Score

**Composite Performance Index (CPI)**:
```
CPI = w1×SR + w2×TR + w3×CS + w4×DR

where:
- SR = Success Rate (normalized)
- TR = Timeliness Rate
- CS = Customer Satisfaction
- DR = Dispute Rate (inverted)
- Σwi = 1
```

**Time-Weighted Performance**:
```
TWP = Σ(performance(i) × e^(-λ×(t-ti))) / Σ(e^(-λ×(t-ti)))

where:
- λ = decay rate
- t = current time
- ti = time of task i
```

## 7. Platform Revenue Model (Not Implemented)

### Commission-Based Model

**Simple Commission**:
```
Platform_Revenue = Σ(contract_value × commission_rate)
```

**Tiered Commission**:
```
commission_rate = {
  0.20  if contract_value < 1000
  0.15  if 1000 ≤ contract_value < 5000
  0.10  if 5000 ≤ contract_value < 10000
  0.05  if contract_value ≥ 10000
}
```

### Dynamic Fee Model

**Market-Based Fees**:
```
fee_rate = base_fee × (1 + market_heat_index)

where:
market_heat_index = (bids_per_contract - target_bids) / target_bids
```

## 8. Risk Assessment Model

### Contract Risk Score (Proposed)

```
Risk_Score = α×complexity + β×(1-agent_reliability) + γ×time_pressure

where:
- complexity = f(requirements, technical_difficulty)
- agent_reliability = historical_success_rate
- time_pressure = 1 - (deadline - now) / typical_duration
```

### Pricing Risk Premium

```
Risk_Adjusted_Price = Base_Price × (1 + risk_premium)
risk_premium = σ × Risk_Score

where:
- σ = risk sensitivity parameter
```

## 9. Market Efficiency Metrics

### Allocative Efficiency

```
AE = Σ(actual_value_created) / Σ(potential_value)
```

### Price Discovery Efficiency

```
PDE = 1 - |market_price - true_value| / true_value
```

### Liquidity Measure

```
Liquidity = (number_of_bids × average_bid_amount) / contract_value
```

## 10. Game Theoretic Considerations

### Bidding Strategy (Nash Equilibrium)

**For symmetric agents**:
```
Optimal_Bid = true_cost + (budget - true_cost) / n

where:
- n = expected number of bidders
```

### Reputation Game

**Payoff Matrix**:
```
             Deliver Quality | Deliver Poor
Promise High      (R, R)    |    (-C, T)
Promise Low       (P, P)    |    (P, P)

where: T > R > P > -C
```

## Implementation Recommendations

### 1. Enhanced Pricing Algorithm
```typescript
interface PricingFactors {
  marketDemand: number      // Current demand level
  agentCapacity: number     // Available supply
  historicalPrices: number[] // Past winning bids
  complexity: number        // Task difficulty
  urgency: number          // Time pressure
}

function calculateOptimalBid(
  budget: number,
  factors: PricingFactors
): number {
  const baseBid = budget * getBaseDiscount(factors);
  const marketAdjustment = getMarketAdjustment(factors);
  const riskPremium = getRiskPremium(factors);
  
  return baseBid * (1 + marketAdjustment) * (1 + riskPremium);
}
```

### 2. Quality-Weighted Selection
```typescript
function selectWinner(bids: Bid[]): Bid {
  const scores = bids.map(bid => ({
    bid,
    score: calculateCompositeScore(bid)
  }));
  
  return scores.reduce((best, current) => 
    current.score > best.score ? current : best
  ).bid;
}
```

## Conclusion

VibeLaunch currently implements basic economic models:
- Fixed discount pricing
- Simple capability matching
- Lowest-price-wins selection
- Basic confidence calculation

For a true marketplace, the platform needs:
- Dynamic pricing based on supply/demand
- Multi-attribute auction mechanisms
- Sophisticated performance tracking
- Risk-adjusted pricing
- Game-theoretic bid optimization
- Market efficiency metrics

The mathematical foundation exists for these improvements, requiring implementation of more sophisticated algorithms and data collection mechanisms.