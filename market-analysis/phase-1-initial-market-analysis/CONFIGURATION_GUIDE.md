# Configuration Guide - Market-Related Settings

## Overview

This guide details all configuration options that affect VibeLaunch's market behavior, pricing mechanisms, and economic operations. Understanding these settings is crucial for analyzing the platform's economic model.

## 1. Environment Variables

### Core System Configuration

#### Backend Services (.env)
```bash
# Supabase Configuration
SUPABASE_URL=https://[project-id].supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...  # Full database access
SUPABASE_ANON_KEY=eyJ...          # Public access

# Server Configuration
PORT=8090                          # Master Agent API port
NODE_ENV=development|production
LOG_LEVEL=debug|info|warn|error

# Security
ENCRYPTION_KEY=your-32-character-key  # For API key encryption

# Organization Settings
DEFAULT_ORG_ID=00000000-0000-0000-0000-000000000000
```

**Economic Impact**:
- `DEFAULT_ORG_ID`: Affects market segmentation
- `ENCRYPTION_KEY`: Secures payment credentials (when implemented)
- No payment processor keys configured

#### Frontend Configuration (public/config.js)
```javascript
window.ENV = {
  SUPABASE_URL: 'https://[project-id].supabase.co',
  SUPABASE_ANON_KEY: 'eyJ...',
  NODE_ENV: 'development'
};
```

**Market Visibility**:
- Public access to market data
- Real-time price updates
- No payment UI configuration

### Missing Economic Configurations

**Not Found**:
```bash
# Payment Processing (Not Implemented)
STRIPE_SECRET_KEY=sk_...
STRIPE_WEBHOOK_SECRET=whsec_...
PAYPAL_CLIENT_ID=...
PAYPAL_SECRET=...

# Platform Economics (Not Configured)
PLATFORM_COMMISSION_RATE=0.15  # 15% fee
MINIMUM_BID_AMOUNT=10.00
MAXIMUM_CONTRACT_VALUE=100000.00

# Market Parameters (Absent)
BID_TIMEOUT_HOURS=24
AUTO_ACCEPT_THRESHOLD=0.90
PRICE_HISTORY_RETENTION_DAYS=365
```

## 2. LLM Provider Configuration

### Supported Providers
```typescript
// From packages/ui/src/config/providers.ts
export const providers = {
  openai: {
    models: ['gpt-4o', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    pricing: undefined  // No cost tracking
  },
  anthropic: {
    models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
    pricing: undefined  // No cost tracking
  },
  google: {
    models: ['gemini-1.5-pro', 'gemini-1.5-flash'],
    pricing: undefined  // No cost tracking
  }
};
```

**Economic Considerations**:
- No LLM cost pass-through to contracts
- No margin calculation on AI operations
- Agents don't consider LLM costs in bidding

### API Key Storage
```sql
-- Stored encrypted in database per organization
llm_config {
  organisation_id: UUID,
  provider: TEXT,
  model_id: TEXT,
  api_key_encrypted: TEXT,  -- AES-256-GCM encrypted
  settings: JSONB
}
```

## 3. Database Configuration

### Economic Tables Configuration

#### Contract Constraints
```sql
-- From migrations
contracts (
  budget DECIMAL(10, 2) NOT NULL,  -- Max 99,999,999.99
  deadline TIMESTAMPTZ NOT NULL,
  status TEXT DEFAULT 'draft'
);
```

**Market Limits**:
- Maximum contract value: ~$100 million
- Precision: 2 decimal places (cents)
- No minimum budget enforced

#### Bid Validation
```sql
bids (
  price DECIMAL(10, 2) NOT NULL,
  confidence DECIMAL(5, 4) NOT NULL,  -- 0.0000 to 1.0000
  CHECK (price > 0),
  CHECK (confidence >= 0 AND confidence <= 1)
);
```

**Bidding Rules**:
- Bids must be positive
- 4 decimal precision for confidence
- No maximum bid limit
- No bid increment rules

### Row-Level Security
```sql
-- Organization isolation
CREATE POLICY contracts_policy ON contracts
  USING (organisation_id IN (
    SELECT org_id FROM user_organisations 
    WHERE user_id = auth.uid()
  ));
```

**Market Isolation**:
- Each organization is a separate market
- No cross-organization price discovery
- Limits market efficiency

## 4. Platform Business Rules

### Bidding Algorithm Parameters
```typescript
// From bid-generation-agent.ts
const PRICING_RULES = {
  HIGH_CONFIDENCE_DISCOUNT: 0.05,    // 5% off budget
  LOW_CONFIDENCE_DISCOUNT: 0.15,     // 15% off budget
  STANDARD_DISCOUNT: 0.10,           // 10% off budget
  
  HIGH_CONFIDENCE_THRESHOLD: 0.8,
  HIGH_CAPABILITY_THRESHOLD: 80,
  LOW_CONFIDENCE_THRESHOLD: 0.6,
  LOW_CAPABILITY_THRESHOLD: 60
};
```

**Price Formation**:
- Fixed discount tiers
- No dynamic pricing
- No supply/demand adjustment
- No competitor awareness

### Task Categories
```typescript
const MARKET_CATEGORIES = [
  'content_creation',
  'social_media',
  'seo_optimization',
  'email_marketing',
  'advertising',
  'web_design',
  'branding',
  'market_research',
  'analytics',
  'video_production'
];
```

**Market Segmentation**:
- 10 defined market segments
- No custom categories
- No category pricing differences
- No seasonal adjustments

## 5. Performance and Scaling

### Current Limitations
```javascript
// Basic rate limiting
RATE_LIMIT_WINDOW_MS: 900000,     // 15 minutes
RATE_LIMIT_MAX_REQUESTS: 100,

// No configured limits for:
MAX_CONTRACTS_PER_ORG: undefined,
MAX_BIDS_PER_CONTRACT: undefined,
MAX_AGENTS_PER_CATEGORY: undefined,
MAX_CONCURRENT_TASKS: undefined
```

**Economic Capacity**:
- No market size limits
- No liquidity requirements
- Potential for market congestion
- No queue management

### Redis Configuration (Planned)
```bash
# From P1 performance features (not implemented)
REDIS_URL=redis://localhost:6379
REDIS_STREAMS_KEY_PREFIX=vibelaunch:
REDIS_CONSUMER_GROUP=vibelaunch-consumers
REDIS_STREAM_MAX_LENGTH=10000
```

**Future Performance**:
- Would enable high-frequency bidding
- Real-time market data streaming
- Improved market matching speed

## 6. Security Configuration

### Encryption Settings
```typescript
// For LLM credentials
ENCRYPTION_ALGORITHM: 'aes-256-gcm'
ENCRYPTION_KEY_LENGTH: 32
IV_LENGTH: 16
AUTH_TAG_LENGTH: 16
```

**Financial Security** (Not Implemented):
- No PCI compliance configuration
- No payment encryption setup
- No financial audit logging
- No fraud detection parameters

## 7. Monitoring Configuration

### Metrics Collection
```yaml
# Prometheus metrics (configured but not deployed)
metrics:
  - api_response_time
  - error_rate
  - database_performance
  - redis_streams_lag  # Not applicable yet
```

**Missing Market Metrics**:
- Contract publication rate
- Bid submission frequency
- Market clearing time
- Average discount rates
- Category liquidity

## 8. Development vs Production

### Development Settings
```bash
# Relaxed for testing
LOG_LEVEL=debug
DISABLE_RATE_LIMITING=true
MOCK_PAYMENTS=true  # If payments existed
AUTO_ACCEPT_BIDS=false
```

### Production Requirements
```bash
# Strict for live market
LOG_LEVEL=info
ENABLE_RATE_LIMITING=true
REQUIRE_PAYMENT_VERIFICATION=true
MIN_BID_RESPONSE_TIME=300000  # 5 minutes
```

## 9. Multi-Tenant Configuration

### Organization Defaults
```json
{
  "default_budget_limit": null,      // No spending caps
  "allowed_categories": "all",       // No restrictions
  "auto_approve_threshold": null,    // Manual approval
  "preferred_agents": [],            // No preferences
  "commission_rate": null            // No platform fee
}
```

**Economic Freedom**:
- Organizations set own budgets
- No platform-imposed limits
- No credit checks
- No escrow requirements

## 10. Recommended Market Configurations

### For Economic Efficiency
```bash
# Implement these for true marketplace
PLATFORM_COMMISSION_RATE=0.15
ESCROW_ENABLED=true
MINIMUM_AGENT_RATING=3.0
BID_VISIBILITY_DELAY_SECONDS=300
DYNAMIC_PRICING_ENABLED=true
CROSS_ORG_ANALYTICS=true
```

### For Market Integrity
```bash
# Prevent manipulation
MAX_BIDS_PER_AGENT_PER_CONTRACT=1
BID_MODIFICATION_ALLOWED=false
MINIMUM_TIME_BETWEEN_BIDS=60
REQUIRE_AGENT_VERIFICATION=true
ENABLE_FRAUD_DETECTION=true
```

### For Scalability
```bash
# Handle growth
USE_REDIS_STREAMS=true
ENABLE_CACHING=true
DATABASE_POOL_SIZE=20
WEBHOOK_RETRY_ATTEMPTS=3
ASYNC_BID_PROCESSING=true
```

## Conclusion

VibeLaunch's current configuration reveals:

**Implemented**:
- Basic multi-tenant isolation
- LLM provider flexibility
- Security fundamentals
- Real-time event streaming

**Missing for Economic Operations**:
- Payment processor configuration
- Platform commission settings
- Market parameter tuning
- Economic monitoring metrics
- Dynamic pricing rules
- Cross-organization market features

The configuration structure could support a full marketplace with minimal changes, primarily adding payment settings and market parameters.