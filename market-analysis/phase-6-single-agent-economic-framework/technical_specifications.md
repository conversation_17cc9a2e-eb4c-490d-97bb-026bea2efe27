# Technical Specifications: Progressive Trust VCG Implementation

## 1. System Architecture Overview

### 1.1 Component Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React UI]
        WS[WebSocket Client]
    end
    
    subgraph "API Layer"
        API[REST API]
        GQL[GraphQL API]
        WSS[WebSocket Server]
    end
    
    subgraph "Core Services"
        AS[Auction Service]
        SS[Scoring Service]
        RS[Reputation Service]
        VS[Verification Service]
        MS[Monitoring Service]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL)]
        RD[(Redis)]
        ES[(Event Store)]
    end
    
    UI --> API
    UI --> WS
    WS --> WSS
    API --> AS
    API --> RS
    AS --> SS
    AS --> VS
    AS --> MS
    AS --> PG
    RS --> PG
    MS --> RD
    AS --> ES
```

### 1.2 Service Specifications

#### Auction Service
- **Language**: TypeScript/Node.js
- **Framework**: Express.js with TypeORM
- **Responsibilities**:
  - Bid collection and validation
  - Winner determination
  - Payment calculation
  - Event publishing

#### Scoring Service
- **Language**: Python with NumPy
- **Framework**: FastAPI
- **Responsibilities**:
  - Multi-attribute score calculation
  - Weight optimization
  - Anomaly detection
  - Performance caching

#### Reputation Service
- **Language**: TypeScript/Node.js
- **Framework**: NestJS
- **Responsibilities**:
  - Reputation score calculation
  - Bayesian updating
  - Trust level assignment
  - Historical tracking

## 2. Database Schema

### 2.1 Core Tables

```sql
-- Enhanced contracts table
ALTER TABLE contracts ADD COLUMN (
    quality_requirement INTEGER DEFAULT 70,
    max_delivery_hours INTEGER DEFAULT 48,
    required_specialization VARCHAR(50),
    scoring_weights JSONB DEFAULT '{"quality": 0.4, "speed": 0.2, "specialization": 0.2, "price": 0.2}'
);

-- Multi-attribute bids
CREATE TABLE bid_attributes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bid_id UUID REFERENCES bids(id) ON DELETE CASCADE,
    quality_score INTEGER CHECK (quality_score BETWEEN 0 AND 100),
    delivery_hours INTEGER CHECK (delivery_hours > 0),
    specialization_score DECIMAL(3,2) CHECK (specialization_score BETWEEN 0 AND 1),
    confidence_level DECIMAL(3,2) CHECK (confidence_level BETWEEN 0 AND 1),
    verification_status VARCHAR(20) DEFAULT 'pending',
    verification_timestamp TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(bid_id)
);

-- Agent reputation and performance
CREATE TABLE agent_reputation (
    agent_id UUID PRIMARY KEY REFERENCES agent_registry(id),
    organization_id UUID REFERENCES organizations(id),
    total_tasks INTEGER DEFAULT 0,
    completed_tasks INTEGER DEFAULT 0,
    quality_scores DECIMAL[] DEFAULT '{}',
    average_quality DECIMAL(3,1) GENERATED ALWAYS AS (
        CASE 
            WHEN array_length(quality_scores, 1) > 0 
            THEN (SELECT AVG(score) FROM unnest(quality_scores) AS score)
            ELSE 0
        END
    ) STORED,
    delivery_performance JSONB DEFAULT '{}',
    trust_level VARCHAR(20) DEFAULT 'new',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, organization_id)
);

-- Auction results for analysis
CREATE TABLE auction_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id UUID REFERENCES contracts(id),
    auction_type VARCHAR(50) NOT NULL,
    winner_bid_id UUID REFERENCES bids(id),
    winner_score DECIMAL(10,4),
    second_best_score DECIMAL(10,4),
    vcg_payment DECIMAL(10,2),
    actual_payment DECIMAL(10,2),
    efficiency_estimate DECIMAL(3,2),
    participation_count INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Collusion detection patterns
CREATE TABLE collusion_indicators (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id),
    agent_ids UUID[],
    pattern_type VARCHAR(50),
    confidence_score DECIMAL(3,2),
    evidence JSONB,
    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed BOOLEAN DEFAULT FALSE
);
```

### 2.2 Indexes for Performance

```sql
-- Scoring performance
CREATE INDEX idx_bid_attributes_bid_id ON bid_attributes(bid_id);
CREATE INDEX idx_bid_attributes_verification ON bid_attributes(verification_status) 
    WHERE verification_status = 'pending';

-- Reputation queries
CREATE INDEX idx_agent_reputation_org_trust ON agent_reputation(organization_id, trust_level);
CREATE INDEX idx_agent_reputation_quality ON agent_reputation(average_quality DESC);

-- Auction analysis
CREATE INDEX idx_auction_results_contract ON auction_results(contract_id);
CREATE INDEX idx_auction_results_efficiency ON auction_results(efficiency_estimate);

-- Pattern detection
CREATE INDEX idx_collusion_indicators_agents ON collusion_indicators USING GIN(agent_ids);
CREATE INDEX idx_collusion_indicators_unreviewed ON collusion_indicators(reviewed) 
    WHERE reviewed = FALSE;
```

## 3. Core Algorithms

### 3.1 Multi-Attribute Scoring Algorithm

```typescript
interface BidAttributes {
  price: number;
  qualityScore: number;
  deliveryHours: number;
  specializationScore: number;
}

interface ScoringWeights {
  quality: number;
  speed: number;
  specialization: number;
  price: number;
}

class MultiAttributeScoring {
  private reputationService: ReputationService;
  
  constructor(reputationService: ReputationService) {
    this.reputationService = reputationService;
  }
  
  calculateScore(
    bid: BidAttributes,
    weights: ScoringWeights,
    budget: number,
    agentId: string
  ): number {
    // Normalize components to [0, 1]
    const priceNorm = 1 - (bid.price / budget);
    const qualityNorm = bid.qualityScore / 100;
    const speedNorm = Math.min(1, 24 / bid.deliveryHours); // Normalize to 24h baseline
    
    // Get reputation modifier
    const reputation = await this.reputationService.getReputationScore(agentId);
    const reputationModifier = this.calculateReputationModifier(reputation);
    
    // Calculate weighted score
    const rawScore = 
      weights.quality * qualityNorm * reputationModifier +
      weights.speed * speedNorm +
      weights.specialization * bid.specializationScore +
      weights.price * priceNorm;
    
    // Apply confidence adjustment
    const confidenceAdjustment = this.getConfidenceAdjustment(reputation.trustLevel);
    
    return rawScore * confidenceAdjustment;
  }
  
  private calculateReputationModifier(reputation: AgentReputation): number {
    // Progressive trust building
    const trustModifiers = {
      'new': 0.8,        // 20% penalty for new agents
      'basic': 0.9,      // 10% penalty
      'verified': 1.0,   // No penalty
      'trusted': 1.05,   // 5% bonus
      'premier': 1.1     // 10% bonus
    };
    
    return trustModifiers[reputation.trustLevel] || 0.8;
  }
}
```

### 3.2 VCG Payment Calculation

```typescript
class VCGPaymentCalculator {
  /**
   * Calculate VCG payment for the winning bid
   * Payment = Social welfare without winner - Social welfare of others with winner
   */
  calculatePayment(
    winningBid: ScoredBid,
    allBids: ScoredBid[],
    buyerUtility: (bid: ScoredBid) => number
  ): number {
    // Remove winner from bids
    const bidsWithoutWinner = allBids.filter(b => b.id !== winningBid.id);
    
    // Find best alternative without winner
    const bestAlternative = this.findBestBid(bidsWithoutWinner, buyerUtility);
    
    if (!bestAlternative) {
      // No alternative exists, winner gets full budget
      return winningBid.price;
    }
    
    // Calculate utilities
    const utilityWithWinner = buyerUtility(winningBid);
    const utilityWithoutWinner = buyerUtility(bestAlternative);
    
    // VCG payment ensures winner's presence improves welfare
    // Payment makes buyer indifferent between winner and alternative
    const vcgPayment = bestAlternative.price + 
      (utilityWithoutWinner - utilityWithWinner);
    
    // Ensure payment is at least the bid price (individual rationality)
    return Math.max(vcgPayment, winningBid.price);
  }
  
  private findBestBid(
    bids: ScoredBid[],
    utilityFunction: (bid: ScoredBid) => number
  ): ScoredBid | null {
    if (bids.length === 0) return null;
    
    return bids.reduce((best, current) => 
      utilityFunction(current) > utilityFunction(best) ? current : best
    );
  }
}
```

### 3.3 Dynamic Weight Optimization

```python
import numpy as np
from scipy.optimize import minimize
from typing import List, Dict, Tuple

class WeightOptimizer:
    def __init__(self, learning_rate: float = 0.1, window_size: int = 100):
        self.learning_rate = learning_rate
        self.window_size = window_size
        self.history = []
    
    def optimize_weights(
        self,
        outcomes: List[Dict],
        current_weights: Dict[str, float]
    ) -> Dict[str, float]:
        """
        Optimize scoring weights based on historical outcomes
        using gradient descent on buyer satisfaction
        """
        # Extract recent outcomes
        recent_outcomes = outcomes[-self.window_size:]
        
        # Convert to numpy arrays
        X = self._extract_features(recent_outcomes)
        y = self._extract_satisfaction(recent_outcomes)
        
        # Define objective function (negative satisfaction for minimization)
        def objective(weights):
            scores = X @ weights
            # Predict satisfaction based on scores
            predicted_satisfaction = 1 / (1 + np.exp(-scores))
            return -np.mean(y * np.log(predicted_satisfaction) + 
                           (1 - y) * np.log(1 - predicted_satisfaction))
        
        # Current weights as starting point
        w0 = np.array([
            current_weights['quality'],
            current_weights['speed'],
            current_weights['specialization'],
            current_weights['price']
        ])
        
        # Constraints: weights sum to 1, all non-negative
        constraints = [
            {'type': 'eq', 'fun': lambda w: np.sum(w) - 1},
            {'type': 'ineq', 'fun': lambda w: w}
        ]
        
        # Optimize
        result = minimize(objective, w0, method='SLSQP', constraints=constraints)
        
        if result.success:
            new_weights = result.x
            # Apply learning rate for stability
            updated_weights = (1 - self.learning_rate) * w0 + self.learning_rate * new_weights
            
            return {
                'quality': float(updated_weights[0]),
                'speed': float(updated_weights[1]),
                'specialization': float(updated_weights[2]),
                'price': float(updated_weights[3])
            }
        
        return current_weights  # Return unchanged if optimization fails
```

### 3.4 Collusion Detection

```typescript
interface BidPattern {
  agentId: string;
  timestamp: Date;
  price: number;
  score: number;
}

class CollusionDetector {
  private readonly ROTATION_THRESHOLD = 0.85;
  private readonly PRICE_CORRELATION_THRESHOLD = 0.9;
  private readonly WINDOW_SIZE = 20;
  
  async detectCollusion(
    recentBids: BidPattern[],
    organizationId: string
  ): Promise<CollusionIndicator[]> {
    const indicators: CollusionIndicator[] = [];
    
    // Group bids by contract
    const bidsByContract = this.groupByContract(recentBids);
    
    // Check for bid rotation patterns
    const rotationPattern = this.detectBidRotation(bidsByContract);
    if (rotationPattern.confidence > this.ROTATION_THRESHOLD) {
      indicators.push({
        type: 'BID_ROTATION',
        agentIds: rotationPattern.agents,
        confidence: rotationPattern.confidence,
        evidence: rotationPattern.evidence
      });
    }
    
    // Check for price correlation
    const priceCorrelation = this.detectPriceCorrelation(recentBids);
    if (priceCorrelation.coefficient > this.PRICE_CORRELATION_THRESHOLD) {
      indicators.push({
        type: 'PRICE_CORRELATION',
        agentIds: priceCorrelation.agents,
        confidence: priceCorrelation.coefficient,
        evidence: priceCorrelation.evidence
      });
    }
    
    // Check for simultaneous bid updates
    const simultaneousBids = this.detectSimultaneousBidding(recentBids);
    if (simultaneousBids.frequency > 0.5) {
      indicators.push({
        type: 'SIMULTANEOUS_BIDDING',
        agentIds: simultaneousBids.agents,
        confidence: simultaneousBids.frequency,
        evidence: simultaneousBids.evidence
      });
    }
    
    // Store detected patterns
    for (const indicator of indicators) {
      await this.storeCollusionIndicator(indicator, organizationId);
    }
    
    return indicators;
  }
  
  private detectBidRotation(bidsByContract: Map<string, BidPattern[]>): RotationResult {
    // Analyze if agents take turns winning contracts
    const winnerSequence: string[] = [];
    const agentWins = new Map<string, number>();
    
    for (const [contractId, bids] of bidsByContract) {
      const winner = bids.reduce((best, current) => 
        current.score > best.score ? current : best
      );
      winnerSequence.push(winner.agentId);
      agentWins.set(winner.agentId, (agentWins.get(winner.agentId) || 0) + 1);
    }
    
    // Calculate rotation metric
    const uniqueAgents = Array.from(agentWins.keys());
    const rotationScore = this.calculateRotationScore(winnerSequence, uniqueAgents);
    
    return {
      confidence: rotationScore,
      agents: uniqueAgents,
      evidence: { winnerSequence, distribution: Object.fromEntries(agentWins) }
    };
  }
}
```

## 4. API Specifications

### 4.1 REST API Endpoints

```yaml
openapi: 3.0.0
info:
  title: VibeLaunch Market API v2
  version: 2.0.0

paths:
  /api/v2/contracts/{contractId}/bid:
    post:
      summary: Submit multi-attribute bid
      parameters:
        - name: contractId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - price
                - qualityScore
                - deliveryHours
                - specializationMatch
              properties:
                price:
                  type: number
                  minimum: 0
                qualityScore:
                  type: integer
                  minimum: 0
                  maximum: 100
                deliveryHours:
                  type: integer
                  minimum: 1
                specializationMatch:
                  type: number
                  minimum: 0
                  maximum: 1
                confidence:
                  type: number
                  minimum: 0
                  maximum: 1
      responses:
        201:
          description: Bid submitted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  bidId:
                    type: string
                    format: uuid
                  score:
                    type: number
                  estimatedRank:
                    type: integer

  /api/v2/agents/{agentId}/reputation:
    get:
      summary: Get agent reputation details
      parameters:
        - name: agentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: organizationId
          in: query
          schema:
            type: string
            format: uuid
      responses:
        200:
          description: Agent reputation data
          content:
            application/json:
              schema:
                type: object
                properties:
                  trustLevel:
                    type: string
                    enum: [new, basic, verified, trusted, premier]
                  averageQuality:
                    type: number
                  completionRate:
                    type: number
                  totalTasks:
                    type: integer
                  recentPerformance:
                    type: array
                    items:
                      type: object
                      properties:
                        contractId:
                          type: string
                        qualityScore:
                          type: number
                        deliveredOn:
                          type: string
                          format: date-time

  /api/v2/market/simulate:
    post:
      summary: Simulate market mechanism
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - mechanism
                - participants
                - parameters
              properties:
                mechanism:
                  type: string
                  enum: [first_price, second_score, vcg, combinatorial]
                participants:
                  type: array
                  items:
                    type: object
                    properties:
                      agentId:
                        type: string
                      capabilities:
                        type: object
                parameters:
                  type: object
                  properties:
                    rounds:
                      type: integer
                    budget:
                      type: number
      responses:
        200:
          description: Simulation results
          content:
            application/json:
              schema:
                type: object
                properties:
                  efficiency:
                    type: number
                  averagePrice:
                    type: number
                  participation:
                    type: number
                  winnerDistribution:
                    type: object
```

### 4.2 WebSocket Events

```typescript
// Client -> Server Events
interface BidUpdateEvent {
  type: 'BID_UPDATE';
  contractId: string;
  bidId: string;
  updates: Partial<BidAttributes>;
}

interface SubscribeEvent {
  type: 'SUBSCRIBE';
  channel: 'CONTRACT' | 'MARKET' | 'REPUTATION';
  filters?: {
    contractId?: string;
    agentId?: string;
    organizationId?: string;
  };
}

// Server -> Client Events
interface AuctionResultEvent {
  type: 'AUCTION_RESULT';
  contractId: string;
  winner: {
    agentId: string;
    bidId: string;
    score: number;
    payment: number;
  };
  efficiency: number;
  participants: number;
}

interface ReputationUpdateEvent {
  type: 'REPUTATION_UPDATE';
  agentId: string;
  changes: {
    trustLevel?: string;
    averageQuality?: number;
    completionRate?: number;
  };
}

interface MarketAlertEvent {
  type: 'MARKET_ALERT';
  severity: 'INFO' | 'WARNING' | 'CRITICAL';
  message: string;
  details?: {
    collusionDetected?: boolean;
    affectedAgents?: string[];
    recommendedAction?: string;
  };
}
```

## 5. Monitoring and Analytics

### 5.1 Key Performance Indicators

```sql
-- Real-time efficiency tracking
CREATE OR REPLACE VIEW market_efficiency AS
SELECT
    DATE_TRUNC('hour', ar.created_at) as hour,
    AVG(ar.efficiency_estimate) as avg_efficiency,
    COUNT(DISTINCT ar.contract_id) as contracts_cleared,
    AVG(ar.participation_count) as avg_participation,
    AVG(ar.actual_payment / c.budget) as avg_price_ratio
FROM auction_results ar
JOIN contracts c ON ar.contract_id = c.id
WHERE ar.created_at > NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', ar.created_at)
ORDER BY hour DESC;

-- Agent performance distribution
CREATE OR REPLACE VIEW agent_performance_distribution AS
SELECT
    trust_level,
    COUNT(DISTINCT agent_id) as agent_count,
    AVG(average_quality) as avg_quality_score,
    AVG(CAST(completed_tasks AS FLOAT) / NULLIF(total_tasks, 0)) as avg_completion_rate,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY average_quality) as median_quality
FROM agent_reputation
WHERE total_tasks > 0
GROUP BY trust_level
ORDER BY 
    CASE trust_level
        WHEN 'premier' THEN 1
        WHEN 'trusted' THEN 2
        WHEN 'verified' THEN 3
        WHEN 'basic' THEN 4
        WHEN 'new' THEN 5
    END;
```

### 5.2 Monitoring Dashboard Configuration

```yaml
# Grafana Dashboard Configuration
dashboard:
  title: VibeLaunch Market Monitoring
  
  panels:
    - title: Market Efficiency
      type: graph
      datasource: PostgreSQL
      targets:
        - query: |
            SELECT 
              hour as time,
              avg_efficiency * 100 as "Efficiency %"
            FROM market_efficiency
            WHERE hour > $__timeFrom()
      
    - title: Auction Participation
      type: stat
      datasource: PostgreSQL
      targets:
        - query: |
            SELECT AVG(participation_count)
            FROM auction_results
            WHERE created_at > NOW() - INTERVAL '1 hour'
      
    - title: Trust Level Distribution
      type: piechart
      datasource: PostgreSQL
      targets:
        - query: |
            SELECT trust_level, agent_count
            FROM agent_performance_distribution
      
    - title: Collusion Alerts
      type: table
      datasource: PostgreSQL
      targets:
        - query: |
            SELECT 
              detected_at,
              pattern_type,
              array_length(agent_ids, 1) as agents_involved,
              confidence_score
            FROM collusion_indicators
            WHERE reviewed = false
            ORDER BY detected_at DESC
            LIMIT 10
      
    - title: Payment vs Budget Ratio
      type: gauge
      datasource: PostgreSQL
      targets:
        - query: |
            SELECT AVG(actual_payment / budget) * 100
            FROM auction_results ar
            JOIN contracts c ON ar.contract_id = c.id
            WHERE ar.created_at > NOW() - INTERVAL '1 hour'
```

## 6. Security Specifications

### 6.1 Verification Pipeline

```typescript
class BidVerificationPipeline {
  private verificationSteps: VerificationStep[] = [
    new RateLimitCheck(),
    new CapabilityVerification(),
    new ConsistencyCheck(),
    new AnomalyDetection(),
    new HistoricalValidation()
  ];
  
  async verify(bid: BidSubmission, agent: Agent): Promise<VerificationResult> {
    const context: VerificationContext = {
      bid,
      agent,
      history: await this.loadAgentHistory(agent.id),
      marketState: await this.getCurrentMarketState()
    };
    
    for (const step of this.verificationSteps) {
      const result = await step.verify(context);
      
      if (!result.passed) {
        return {
          status: 'REJECTED',
          reason: result.reason,
          step: step.name
        };
      }
      
      // Update context with step results
      context.previousResults.push(result);
    }
    
    return {
      status: 'VERIFIED',
      confidence: this.calculateConfidence(context),
      timestamp: new Date()
    };
  }
}
```

### 6.2 Audit Trail Specification

```sql
-- Comprehensive audit logging
CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id UUID NOT NULL,
    actor_id UUID,
    actor_type VARCHAR(20),
    action VARCHAR(100) NOT NULL,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Audit trail indexes
CREATE INDEX idx_audit_log_entity ON audit_log(entity_type, entity_id);
CREATE INDEX idx_audit_log_actor ON audit_log(actor_type, actor_id);
CREATE INDEX idx_audit_log_time ON audit_log(created_at DESC);

-- Audit trigger for sensitive operations
CREATE OR REPLACE FUNCTION audit_sensitive_operation()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_log (
        event_type,
        entity_type,
        entity_id,
        actor_id,
        actor_type,
        action,
        details
    ) VALUES (
        TG_ARGV[0],
        TG_TABLE_NAME,
        NEW.id,
        current_setting('app.current_user_id')::UUID,
        current_setting('app.current_user_type'),
        TG_OP,
        row_to_json(NEW)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply audit triggers
CREATE TRIGGER audit_bid_changes
    AFTER INSERT OR UPDATE OR DELETE ON bids
    FOR EACH ROW
    EXECUTE FUNCTION audit_sensitive_operation('BID_CHANGE');

CREATE TRIGGER audit_reputation_changes
    AFTER UPDATE ON agent_reputation
    FOR EACH ROW
    WHEN (OLD.trust_level IS DISTINCT FROM NEW.trust_level)
    EXECUTE FUNCTION audit_sensitive_operation('TRUST_LEVEL_CHANGE');
```

## 7. Performance Optimization

### 7.1 Caching Strategy

```typescript
class ScoringCache {
  private redis: Redis;
  private ttl: number = 300; // 5 minutes
  
  async getCachedScore(
    bidId: string,
    weights: ScoringWeights
  ): Promise<number | null> {
    const key = this.generateKey(bidId, weights);
    const cached = await this.redis.get(key);
    
    if (cached) {
      // Update access pattern for cache optimization
      await this.redis.zincrby('cache:access:frequency', 1, key);
      return parseFloat(cached);
    }
    
    return null;
  }
  
  async setCachedScore(
    bidId: string,
    weights: ScoringWeights,
    score: number
  ): Promise<void> {
    const key = this.generateKey(bidId, weights);
    
    await this.redis.multi()
      .set(key, score.toString(), 'EX', this.ttl)
      .zadd('cache:scores:by:time', Date.now(), key)
      .exec();
  }
  
  private generateKey(bidId: string, weights: ScoringWeights): string {
    // Create deterministic cache key
    const weightsHash = crypto
      .createHash('md5')
      .update(JSON.stringify(weights))
      .digest('hex')
      .substring(0, 8);
    
    return `score:${bidId}:${weightsHash}`;
  }
}
```

### 7.2 Database Query Optimization

```sql
-- Materialized view for fast reputation lookups
CREATE MATERIALIZED VIEW mv_agent_reputation_summary AS
SELECT
    ar.agent_id,
    ar.organization_id,
    ar.trust_level,
    ar.average_quality,
    ar.completed_tasks,
    ar.total_tasks,
    COALESCE(
        (SELECT array_agg(contract_id ORDER BY created_at DESC)
         FROM auction_results
         WHERE winner_bid_id IN (
             SELECT id FROM bids WHERE agent_id = ar.agent_id
         )
         LIMIT 5),
        '{}'::UUID[]
    ) as recent_wins
FROM agent_reputation ar
WHERE ar.total_tasks > 0;

-- Refresh strategy
CREATE OR REPLACE FUNCTION refresh_reputation_summary()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_agent_reputation_summary;
END;
$$ LANGUAGE plpgsql;

-- Schedule refresh every 5 minutes
SELECT cron.schedule(
    'refresh-reputation-summary',
    '*/5 * * * *',
    'SELECT refresh_reputation_summary()'
);
```

This technical specification provides the detailed implementation guidance for the Progressive Trust VCG mechanism, ensuring efficient, secure, and scalable deployment within VibeLaunch's infrastructure.