#!/usr/bin/env python3
"""
Generate visual assets illustrating the final Progressive Trust VCG economic system
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, Rectangle, Circle, FancyArrowPatch
from matplotlib.patches import ConnectionPatch
import numpy as np
import seaborn as sns

# Set style
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

def create_progressive_trust_vcg_overview():
    """Create comprehensive overview of the Progressive Trust VCG system"""
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # Title
    ax.text(0.5, 0.95, 'Progressive Trust VCG Economic System', 
            ha='center', va='center', fontsize=20, fontweight='bold',
            transform=ax.transAxes)
    
    # Phase progression boxes
    phases = [
        (0.125, 0.7, 'Phase 1\nSecurity Foundation', '#ff9999', '42% → 65%'),
        (0.375, 0.7, 'Phase 2\nEnhanced Mechanisms', '#99ccff', '65% → 78%'),
        (0.625, 0.7, 'Phase 3\nTheoretical Optimality', '#99ff99', '78% → 85%'),
        (0.875, 0.7, 'Phase 4\nAdvanced Features', '#ffcc99', '85% → 90%')
    ]
    
    for x, y, title, color, efficiency in phases:
        # Phase box
        box = FancyBboxPatch((x-0.1, y-0.1), 0.2, 0.15,
                           boxstyle="round,pad=0.01",
                           facecolor=color, edgecolor='black', linewidth=2,
                           transform=ax.transAxes)
        ax.add_patch(box)
        ax.text(x, y+0.025, title, ha='center', va='center', 
                fontsize=11, fontweight='bold', transform=ax.transAxes)
        ax.text(x, y-0.05, efficiency, ha='center', va='center', 
                fontsize=10, style='italic', transform=ax.transAxes)
    
    # Draw arrows between phases
    for i in range(3):
        arrow = FancyArrowPatch((phases[i][0]+0.1, phases[i][1]), 
                              (phases[i+1][0]-0.1, phases[i+1][1]),
                              connectionstyle="arc3,rad=0", 
                              arrowstyle='->', lw=2, color='black',
                              transform=ax.transAxes)
        ax.add_patch(arrow)
    
    # Core mechanism components
    components = [
        (0.2, 0.35, 'Multi-Attribute\nScoring', '#e6f3ff'),
        (0.4, 0.35, 'Trust-Based\nVerification', '#ffe6e6'),
        (0.6, 0.35, 'VCG Payment\nMechanism', '#e6ffe6'),
        (0.8, 0.35, 'Dynamic\nAdaptation', '#ffffe6')
    ]
    
    for x, y, label, color in components:
        circle = Circle((x, y), 0.08, facecolor=color, edgecolor='black', 
                       linewidth=2, transform=ax.transAxes)
        ax.add_patch(circle)
        ax.text(x, y, label, ha='center', va='center', fontsize=9,
                transform=ax.transAxes)
    
    # Economic outcomes
    outcomes_y = 0.1
    ax.text(0.5, outcomes_y + 0.05, 'Economic Outcomes', ha='center', va='center',
            fontsize=14, fontweight='bold', transform=ax.transAxes)
    
    outcomes = [
        ('Efficiency: 90%', 0.2),
        ('Revenue: $232K/mo', 0.35),
        ('Network Value: $2.88M/yr', 0.5),
        ('ROI: 158%', 0.65),
        ('Agents: 200+', 0.8)
    ]
    
    for outcome, x in outcomes:
        box = FancyBboxPatch((x-0.06, outcomes_y-0.03), 0.12, 0.04,
                           boxstyle="round,pad=0.005",
                           facecolor='lightblue', edgecolor='navy', linewidth=1,
                           transform=ax.transAxes)
        ax.add_patch(box)
        ax.text(x, outcomes_y, outcome, ha='center', va='center', 
                fontsize=9, transform=ax.transAxes)
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('progressive_trust_vcg_overview.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_economic_flow_diagram():
    """Create detailed economic flow diagram showing value creation and distribution"""
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # Title
    ax.text(7, 9.5, 'VibeLaunch Economic Value Flow', 
            ha='center', fontsize=18, fontweight='bold')
    
    # Define entities
    entities = {
        'orgs': (2, 7, 'Organizations\n(Buyers)', '#ff9999'),
        'platform': (7, 7, 'VibeLaunch\nPlatform', '#99ff99'),
        'agents': (12, 7, 'AI Agents\n(Sellers)', '#99ccff'),
        'market': (7, 4, 'Progressive Trust\nVCG Market', '#ffcc99')
    }
    
    # Draw entities
    for key, (x, y, label, color) in entities.items():
        if key == 'platform':
            box = FancyBboxPatch((x-1.5, y-0.75), 3, 1.5,
                               boxstyle="round,pad=0.1",
                               facecolor=color, edgecolor='black', linewidth=3)
        else:
            box = FancyBboxPatch((x-1, y-0.5), 2, 1,
                               boxstyle="round,pad=0.05",
                               facecolor=color, edgecolor='black', linewidth=2)
        ax.add_patch(box)
        ax.text(x, y, label, ha='center', va='center', 
                fontsize=12, fontweight='bold')
    
    # Value flows with labels
    flows = [
        # (from_x, from_y, to_x, to_y, label, value, color)
        (3, 7, 5.5, 7, 'Contracts\n$500 avg', '$500K/mo', 'green'),
        (8.5, 7, 11, 7, 'Tasks', '2000/mo', 'blue'),
        (12, 6.5, 8.5, 6.5, 'Bids', '14K/mo', 'orange'),
        (5.5, 6.5, 3, 6.5, 'Results', '95% quality', 'purple'),
        (7, 6.25, 7, 5.5, 'Auction\nClearance', '90% eff.', 'red'),
        (7, 4.5, 3, 7.5, 'Commission\n18%', '$90K/mo', 'darkgreen'),
        (7, 4.5, 11, 7.5, 'Payment\n82%', '$410K/mo', 'darkblue'),
    ]
    
    for x1, y1, x2, y2, label, value, color in flows:
        arrow = FancyArrowPatch((x1, y1), (x2, y2),
                              connectionstyle="arc3,rad=0.2",
                              arrowstyle='->', lw=3, color=color, alpha=0.7)
        ax.add_patch(arrow)
        # Add labels
        mid_x, mid_y = (x1 + x2) / 2, (y1 + y2) / 2
        ax.text(mid_x, mid_y + 0.2, label, ha='center', va='bottom', 
                fontsize=9, fontweight='bold')
        ax.text(mid_x, mid_y - 0.2, value, ha='center', va='top', 
                fontsize=8, style='italic', color=color)
    
    # Network effects indicators
    ax.text(2, 5, 'Network Value:\n$450K/mo', ha='center', va='center',
            bbox=dict(boxstyle='round', facecolor='lightyellow'),
            fontsize=9)
    ax.text(12, 5, 'Agent Pool:\n200+ active', ha='center', va='center',
            bbox=dict(boxstyle='round', facecolor='lightcyan'),
            fontsize=9)
    
    # Key metrics box
    metrics_text = ('Key Metrics:\n'
                   '• Allocative Efficiency: 90%\n'
                   '• Transaction Volume: $500K/mo\n'
                   '• Platform Revenue: $90K/mo\n'
                   '• Average Quality Score: 87\n'
                   '• Market Liquidity: 6.3')
    ax.text(1, 2, metrics_text, ha='left', va='top',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray'),
            fontsize=10)
    
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('economic_value_flow.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_trust_mechanism_diagram():
    """Create diagram showing the trust-based progression mechanism"""
    fig, ax = plt.subplots(figsize=(12, 10))
    
    # Title
    ax.text(0.5, 0.95, 'Trust-Based Agent Progression System', 
            ha='center', va='center', fontsize=18, fontweight='bold',
            transform=ax.transAxes)
    
    # Trust levels with characteristics
    levels = [
        ('New', 0.5, 0.8, '#ffcccc', 
         ['Max bid: $100', 'Min interval: 5min', 'Unverified'], 0.8),
        ('Basic', 0.5, 0.65, '#ffddcc', 
         ['Max bid: $500', 'Min interval: 2min', 'Email verified'], 0.9),
        ('Verified', 0.5, 0.5, '#ffffcc', 
         ['Max bid: $2000', 'Min interval: 1min', 'Identity verified'], 1.0),
        ('Trusted', 0.5, 0.35, '#ccffcc', 
         ['Max bid: $5000', 'Min interval: 30s', '30+ tasks, 85% quality'], 1.05),
        ('Premier', 0.5, 0.2, '#ccffff', 
         ['Unlimited bidding', 'No restrictions', '100+ tasks, 90% quality'], 1.1)
    ]
    
    # Draw trust levels
    for i, (level, x, y, color, features, modifier) in enumerate(levels):
        # Main box
        box = FancyBboxPatch((x-0.15, y-0.06), 0.3, 0.1,
                           boxstyle="round,pad=0.01",
                           facecolor=color, edgecolor='black', linewidth=2,
                           transform=ax.transAxes)
        ax.add_patch(box)
        
        # Level name
        ax.text(x, y+0.02, level, ha='center', va='center', 
                fontsize=14, fontweight='bold', transform=ax.transAxes)
        
        # Reputation modifier
        ax.text(x+0.2, y, f'×{modifier}', ha='center', va='center', 
                fontsize=11, color='blue', fontweight='bold', transform=ax.transAxes)
        
        # Features
        feature_text = '\n'.join(features)
        ax.text(x-0.25, y, feature_text, ha='right', va='center', 
                fontsize=9, transform=ax.transAxes)
        
        # Requirements for progression
        if i < len(levels) - 1:
            req_y = y - 0.075
            requirements = {
                0: 'Email verify + Test task',
                1: 'KYC + 10 tasks + 80% quality',
                2: '30 tasks + 85% quality + No violations',
                3: '100 tasks + 90% quality + Security audit'
            }
            ax.text(x, req_y, f'▼ {requirements[i]} ▼', ha='center', va='center',
                   fontsize=8, style='italic', color='gray', transform=ax.transAxes)
    
    # Side panels showing benefits
    # Left panel - Economic benefits
    ax.text(0.15, 0.9, 'Economic Benefits', ha='center', fontsize=12, 
            fontweight='bold', transform=ax.transAxes)
    
    benefits = [
        'Higher bid limits',
        'Better scoring multiplier',
        'Priority in auctions',
        'Reduced fees',
        'Access to premium contracts'
    ]
    
    for i, benefit in enumerate(benefits):
        ax.text(0.15, 0.85 - i*0.05, f'• {benefit}', ha='center', va='top',
               fontsize=9, transform=ax.transAxes)
    
    # Right panel - Platform benefits
    ax.text(0.85, 0.9, 'Platform Benefits', ha='center', fontsize=12, 
            fontweight='bold', transform=ax.transAxes)
    
    platform_benefits = [
        'Quality assurance',
        'Reduced fraud risk',
        'Better matching',
        'Higher efficiency',
        'Trust building'
    ]
    
    for i, benefit in enumerate(platform_benefits):
        ax.text(0.85, 0.85 - i*0.05, f'• {benefit}', ha='center', va='top',
               fontsize=9, transform=ax.transAxes)
    
    # Distribution chart at bottom
    ax.text(0.5, 0.08, 'Current Agent Distribution', ha='center', fontsize=11,
            fontweight='bold', transform=ax.transAxes)
    
    distribution = [30, 25, 20, 15, 10]  # Percentages
    colors_dist = ['#ffcccc', '#ffddcc', '#ffffcc', '#ccffcc', '#ccffff']
    level_names = ['New', 'Basic', 'Verified', 'Trusted', 'Premier']
    
    # Create horizontal bar
    start_x = 0.2
    for i, (pct, color, name) in enumerate(zip(distribution, colors_dist, level_names)):
        width = pct / 100 * 0.6  # Scale to fit
        rect = Rectangle((start_x, 0.02), width, 0.03,
                        facecolor=color, edgecolor='black', linewidth=1,
                        transform=ax.transAxes)
        ax.add_patch(rect)
        ax.text(start_x + width/2, 0.035, f'{name}\n{pct}%', 
               ha='center', va='center', fontsize=8, transform=ax.transAxes)
        start_x += width
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('trust_mechanism_diagram.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_vcg_payment_illustration():
    """Create illustration of VCG payment mechanism"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # Left: First-price vs VCG comparison
    ax1.set_title('Payment Mechanism Comparison', fontsize=14, fontweight='bold')
    
    # Scenario data
    scenarios = ['Current\n(First-Price)', 'Progressive Trust VCG']
    agents = ['Agent A', 'Agent B', 'Agent C']
    
    # First-price data
    first_price_bids = [200, 220, 250]
    first_price_scores = [0.85, 0.82, 0.78]
    
    # VCG data
    vcg_bids = [280, 285, 290]  # Truthful bids
    vcg_scores = [0.92, 0.89, 0.86]
    vcg_payment = 285  # Second-best price
    
    # Plot first-price
    y_pos = np.arange(len(agents))
    ax1.barh(y_pos + 0.2, first_price_bids, 0.35, 
             label='First-Price Bids', color='#ff9999', alpha=0.7)
    ax1.barh(y_pos - 0.2, vcg_bids, 0.35,
             label='VCG Truthful Bids', color='#99ff99', alpha=0.7)
    
    # Add scores as text
    for i, (fp_score, vcg_score) in enumerate(zip(first_price_scores, vcg_scores)):
        ax1.text(first_price_bids[i] + 5, i + 0.2, f'Score: {fp_score}', 
                va='center', fontsize=9)
        ax1.text(vcg_bids[i] + 5, i - 0.2, f'Score: {vcg_score}', 
                va='center', fontsize=9)
    
    # Winner indicators
    ax1.plot(first_price_bids[0], 0.2, 'r*', markersize=15, label='Winner')
    ax1.plot(vcg_bids[0], -0.2, 'g*', markersize=15)
    
    # Payment lines
    ax1.axvline(x=200, color='red', linestyle='--', alpha=0.5, 
                label=f'First-Price Payment: ${first_price_bids[0]}')
    ax1.axvline(x=285, color='green', linestyle='--', alpha=0.5,
                label=f'VCG Payment: ${vcg_payment}')
    
    ax1.set_yticks(y_pos)
    ax1.set_yticklabels(agents)
    ax1.set_xlabel('Bid/Payment ($)')
    ax1.legend(loc='lower right', fontsize=9)
    ax1.grid(True, alpha=0.3)
    
    # Right: Multi-attribute scoring visualization
    ax2.set_title('Multi-Attribute Scoring Function', fontsize=14, fontweight='bold')
    
    # Scoring components
    attributes = ['Quality\n(40%)', 'Speed\n(20%)', 'Specialization\n(20%)', 'Price\n(20%)']
    
    # Agent scores for each attribute
    agent_data = {
        'Agent A': [0.36, 0.18, 0.19, 0.19],  # Total: 0.92
        'Agent B': [0.34, 0.17, 0.18, 0.20],  # Total: 0.89
        'Agent C': [0.32, 0.16, 0.17, 0.21],  # Total: 0.86
    }
    
    # Stacked bar chart
    bottom = np.zeros(len(agent_data))
    colors = ['#ff9999', '#99ccff', '#99ff99', '#ffcc99']
    
    agents_list = list(agent_data.keys())
    x_pos = np.arange(len(agents_list))
    
    for i, attr in enumerate(attributes):
        values = [agent_data[agent][i] for agent in agents_list]
        ax2.bar(x_pos, values, 0.6, bottom=bottom, label=attr, color=colors[i])
        
        # Add value labels
        for j, (val, b) in enumerate(zip(values, bottom)):
            if val > 0.05:  # Only show if large enough
                ax2.text(j, b + val/2, f'{val:.2f}', ha='center', va='center',
                        fontsize=9, fontweight='bold')
        
        bottom += values
    
    # Total scores on top
    for i, agent in enumerate(agents_list):
        total = sum(agent_data[agent])
        ax2.text(i, total + 0.02, f'Total: {total:.2f}', ha='center', va='bottom',
                fontsize=11, fontweight='bold')
    
    # Winner indicator
    ax2.plot(0, sum(agent_data['Agent A']) + 0.05, 'g*', markersize=15)
    
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(agents_list)
    ax2.set_ylabel('Score Components')
    ax2.set_ylim(0, 1.1)
    ax2.legend(loc='upper right', fontsize=9)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # Add formula
    formula = r'$Score_i = 0.4 \cdot Q_i + 0.2 \cdot S_i + 0.2 \cdot Z_i + 0.2 \cdot (1 - \frac{P_i}{Budget})$'
    ax2.text(0.5, -0.15, formula, ha='center', transform=ax2.transAxes, fontsize=10)
    
    plt.tight_layout()
    plt.savefig('vcg_payment_illustration.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_market_dynamics_visualization():
    """Create visualization of market dynamics and equilibrium"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
    
    # Top left: Efficiency progression with phases
    months = np.arange(0, 13)
    efficiency = np.array([42, 45, 55, 65, 70, 74, 78, 81, 83, 85, 87, 89, 90])
    revenue = np.array([50, 52, 65, 93, 110, 128, 145, 165, 182, 201, 215, 225, 232])
    
    ax1.set_title('Efficiency & Revenue Progression', fontsize=12, fontweight='bold')
    
    # Efficiency line
    ax1_twin = ax1.twinx()
    line1 = ax1.plot(months, efficiency, 'b-', linewidth=3, marker='o', label='Efficiency')
    line2 = ax1_twin.plot(months, revenue, 'g-', linewidth=3, marker='s', label='Revenue')
    
    # Phase backgrounds
    phases = [(1, 3, 'Phase 1', '#ffcccc'), (4, 6, 'Phase 2', '#ccffcc'), 
              (7, 9, 'Phase 3', '#ccccff'), (10, 12, 'Phase 4', '#ffffcc')]
    
    for start, end, name, color in phases:
        ax1.axvspan(start, end, alpha=0.2, color=color)
        ax1.text((start + end) / 2, 95, name, ha='center', fontsize=9)
    
    ax1.set_xlabel('Month')
    ax1.set_ylabel('Efficiency (%)', color='b')
    ax1_twin.set_ylabel('Revenue ($K/mo)', color='g')
    ax1.tick_params(axis='y', labelcolor='b')
    ax1_twin.tick_params(axis='y', labelcolor='g')
    ax1.grid(True, alpha=0.3)
    
    # Combine legends
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='lower right')
    
    # Top right: Agent participation and quality
    ax2.set_title('Agent Ecosystem Growth', fontsize=12, fontweight='bold')
    
    agent_counts = np.array([30, 35, 50, 80, 100, 120, 140, 160, 170, 180, 190, 195, 200])
    quality_scores = np.array([70, 71, 73, 75, 77, 79, 81, 83, 84, 85, 86, 87, 87.5])
    
    ax2_twin = ax2.twinx()
    ax2.bar(months, agent_counts, alpha=0.7, color='orange', label='Active Agents')
    ax2_twin.plot(months, quality_scores, 'r-', linewidth=3, marker='d', label='Avg Quality')
    
    ax2.set_xlabel('Month')
    ax2.set_ylabel('Active Agents', color='orange')
    ax2_twin.set_ylabel('Average Quality Score', color='r')
    ax2.tick_params(axis='y', labelcolor='orange')
    ax2_twin.tick_params(axis='y', labelcolor='r')
    
    # Add trust level distribution for final month
    trust_dist = [40, 50, 60, 35, 15]  # New, Basic, Verified, Trusted, Premier
    trust_colors = ['#ffcccc', '#ffddcc', '#ffffcc', '#ccffcc', '#ccffff']
    
    # Stacked bar for month 12
    bottom = 0
    for i, (count, color) in enumerate(zip(trust_dist, trust_colors)):
        ax2.bar(12, count, bottom=bottom, width=0.8, color=color, alpha=0.8)
        bottom += count
    
    ax2.legend(loc='upper left')
    ax2_twin.legend(loc='lower right')
    ax2.grid(True, alpha=0.3)
    
    # Bottom left: Market liquidity and competition
    ax3.set_title('Market Liquidity Evolution', fontsize=12, fontweight='bold')
    
    liquidity = np.array([0.48, 0.55, 0.75, 0.96, 1.5, 1.8, 2.08, 2.8, 3.1, 3.4, 4.5, 5.2, 6.3])
    avg_bids = np.array([3.2, 3.5, 4.0, 4.1, 4.5, 4.9, 5.2, 5.6, 5.8, 6.0, 6.5, 6.8, 7.0])
    
    ax3_twin = ax3.twinx()
    ax3.plot(months, liquidity, 'purple', linewidth=3, marker='o', label='Liquidity Score')
    ax3_twin.plot(months, avg_bids, 'brown', linewidth=3, marker='^', label='Avg Bids/Contract')
    
    # Add critical thresholds
    ax3.axhline(y=1.0, color='purple', linestyle='--', alpha=0.5)
    ax3.text(6, 1.1, 'Minimum Viable Liquidity', fontsize=9, color='purple')
    
    ax3.set_xlabel('Month')
    ax3.set_ylabel('Liquidity Score', color='purple')
    ax3_twin.set_ylabel('Average Bids per Contract', color='brown')
    ax3.tick_params(axis='y', labelcolor='purple')
    ax3_twin.tick_params(axis='y', labelcolor='brown')
    ax3.legend(loc='upper left')
    ax3_twin.legend(loc='lower right')
    ax3.grid(True, alpha=0.3)
    
    # Bottom right: Equilibrium analysis
    ax4.set_title('Market Equilibrium Dynamics', fontsize=12, fontweight='bold')
    
    # Create phase space diagram
    n_agents = np.linspace(20, 250, 50)
    n_orgs = np.linspace(10, 100, 50)
    
    # Equilibrium surface (simplified)
    X, Y = np.meshgrid(n_agents, n_orgs)
    Z = np.log(X * Y) * 10  # Simplified equilibrium metric
    
    contour = ax4.contourf(X, Y, Z, levels=15, cmap='viridis', alpha=0.7)
    
    # Add trajectory
    trajectory_agents = agent_counts[::2] * 1.2  # Scale for visualization
    trajectory_orgs = months[::2] * 7 + 10
    ax4.plot(trajectory_agents, trajectory_orgs, 'r-', linewidth=3, marker='o', 
             markersize=8, label='Growth Trajectory')
    
    # Mark equilibrium points
    ax4.plot(50, 20, 'r*', markersize=20, label='Critical Mass')
    ax4.plot(200, 80, 'g*', markersize=20, label='Target Equilibrium')
    
    ax4.set_xlabel('Number of Active Agents')
    ax4.set_ylabel('Number of Organizations')
    ax4.legend()
    
    # Colorbar
    cbar = plt.colorbar(contour, ax=ax4)
    cbar.set_label('Market Value Index', rotation=270, labelpad=20)
    
    plt.tight_layout()
    plt.savefig('market_dynamics_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_commission_structure_diagram():
    """Create diagram showing the commission structure and incentives"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # Left: Commission rate optimization
    ax1.set_title('Optimal Commission Structure', fontsize=14, fontweight='bold')
    
    commission_rates = np.linspace(0, 0.4, 100)
    volume_factor = (1 - commission_rates)**1.5  # Elasticity effect
    revenue = commission_rates * 1000 * volume_factor
    
    ax1.plot(commission_rates * 100, revenue, 'b-', linewidth=3)
    
    # Mark optimal and recommended ranges
    optimal_idx = np.argmax(revenue)
    optimal_rate = commission_rates[optimal_idx]
    ax1.plot(optimal_rate * 100, revenue[optimal_idx], 'ro', markersize=10)
    ax1.axvspan(15, 20, alpha=0.2, color='green', label='Recommended Range')
    
    # Annotations
    ax1.annotate(f'Theoretical\nOptimal: {optimal_rate*100:.1f}%',
                xy=(optimal_rate * 100, revenue[optimal_idx]),
                xytext=(optimal_rate * 100 + 5, revenue[optimal_idx] - 50),
                arrowprops=dict(arrowstyle='->', color='red'))
    
    ax1.set_xlabel('Commission Rate (%)')
    ax1.set_ylabel('Platform Revenue')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Right: Incentive alignment matrix
    ax2.set_title('Incentive Alignment Matrix', fontsize=14, fontweight='bold')
    
    stakeholders = ['Organizations', 'AI Agents', 'Platform', 'End Users']
    incentives = ['Quality', 'Efficiency', 'Trust', 'Innovation', 'Growth']
    
    # Alignment scores (0-1)
    alignment_matrix = np.array([
        [0.9, 0.8, 0.7, 0.8, 0.9],  # Organizations
        [0.8, 0.9, 0.9, 0.7, 0.8],  # AI Agents
        [0.9, 0.9, 0.8, 0.8, 0.9],  # Platform
        [0.9, 0.7, 0.8, 0.7, 0.8],  # End Users
    ])
    
    im = ax2.imshow(alignment_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    
    # Set ticks
    ax2.set_xticks(np.arange(len(incentives)))
    ax2.set_yticks(np.arange(len(stakeholders)))
    ax2.set_xticklabels(incentives)
    ax2.set_yticklabels(stakeholders)
    
    # Rotate the tick labels
    plt.setp(ax2.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")
    
    # Add text annotations
    for i in range(len(stakeholders)):
        for j in range(len(incentives)):
            text = ax2.text(j, i, f'{alignment_matrix[i, j]:.1f}',
                           ha="center", va="center", color="black", fontweight='bold')
    
    # Colorbar
    cbar = plt.colorbar(im, ax=ax2)
    cbar.set_label('Alignment Score', rotation=270, labelpad=20)
    
    # Add summary text
    ax2.text(0.5, -0.25, 'Higher scores indicate better incentive alignment',
            ha='center', transform=ax2.transAxes, fontsize=10, style='italic')
    
    plt.tight_layout()
    plt.savefig('commission_structure_diagram.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Generate all economic system visualizations"""
    print("Generating VibeLaunch Progressive Trust VCG economic system visualizations...")
    
    create_progressive_trust_vcg_overview()
    print("✓ Progressive Trust VCG overview created")
    
    create_economic_flow_diagram()
    print("✓ Economic value flow diagram created")
    
    create_trust_mechanism_diagram()
    print("✓ Trust mechanism diagram created")
    
    create_vcg_payment_illustration()
    print("✓ VCG payment illustration created")
    
    create_market_dynamics_visualization()
    print("✓ Market dynamics visualization created")
    
    create_commission_structure_diagram()
    print("✓ Commission structure diagram created")
    
    print("\nAll economic system visualizations generated successfully!")

if __name__ == "__main__":
    main()