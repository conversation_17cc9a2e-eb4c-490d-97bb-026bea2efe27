#!/usr/bin/env python3
"""
Generate comprehensive system architecture diagram for Progressive Trust VCG
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, Rectangle, Circle, FancyArrowPatch, Polygon
import numpy as np

def create_system_architecture():
    """Create comprehensive system architecture diagram"""
    fig, ax = plt.subplots(figsize=(18, 12))
    
    # Title
    ax.text(9, 11.5, 'Progressive Trust VCG System Architecture', 
            ha='center', fontsize=20, fontweight='bold')
    ax.text(9, 11, 'Integrated Economic, Technical, and Security Components', 
            ha='center', fontsize=14, style='italic')
    
    # Define layers with colors
    layers = [
        (1, 8.5, 17, 2, 'User Interface Layer', '#e6f3ff'),
        (1, 6, 17, 2, 'API & Service Layer', '#ffe6e6'),
        (1, 3.5, 17, 2, 'Core Economic Engine', '#e6ffe6'),
        (1, 1, 17, 2, 'Data & Infrastructure Layer', '#ffffe6')
    ]
    
    # Draw layers
    for x, y, w, h, label, color in layers:
        rect = Rectangle((x, y), w, h, facecolor=color, edgecolor='black', 
                        linewidth=2, alpha=0.3)
        ax.add_patch(rect)
        ax.text(x + 0.5, y + h - 0.3, label, fontsize=12, fontweight='bold')
    
    # UI Layer Components
    ui_components = [
        (2, 9.2, 'Organization\nDashboard', '#4169E1'),
        (5, 9.2, 'Agent\nInterface', '#4169E1'),
        (8, 9.2, 'Master Agent\nChat', '#4169E1'),
        (11, 9.2, 'Marketplace\nView', '#4169E1'),
        (14, 9.2, 'Analytics\nDashboard', '#4169E1'),
        (17, 9.2, 'Admin\nPanel', '#4169E1')
    ]
    
    for x, y, label, color in ui_components:
        box = FancyBboxPatch((x-0.8, y-0.3), 1.6, 0.6,
                           boxstyle="round,pad=0.05",
                           facecolor=color, edgecolor='black', linewidth=1,
                           alpha=0.7)
        ax.add_patch(box)
        ax.text(x, y, label, ha='center', va='center', fontsize=9, 
                color='white', fontweight='bold')
    
    # API Layer Components
    api_components = [
        (3, 6.7, 'REST API\nv2', '#DC143C'),
        (6, 6.7, 'GraphQL\nAPI', '#DC143C'),
        (9, 6.7, 'WebSocket\nServer', '#DC143C'),
        (12, 6.7, 'Webhook\nProcessor', '#DC143C'),
        (15, 6.7, 'Event\nBus', '#DC143C')
    ]
    
    for x, y, label, color in api_components:
        box = FancyBboxPatch((x-0.8, y-0.3), 1.6, 0.6,
                           boxstyle="round,pad=0.05",
                           facecolor=color, edgecolor='black', linewidth=1,
                           alpha=0.7)
        ax.add_patch(box)
        ax.text(x, y, label, ha='center', va='center', fontsize=9, 
                color='white', fontweight='bold')
    
    # Core Economic Engine - Main Components
    # Central VCG Engine
    vcg_box = FancyBboxPatch((7, 4), 4, 1,
                           boxstyle="round,pad=0.1",
                           facecolor='#2E8B57', edgecolor='black', linewidth=3)
    ax.add_patch(vcg_box)
    ax.text(9, 4.5, 'Progressive Trust VCG Engine', ha='center', va='center', 
            fontsize=12, color='white', fontweight='bold')
    
    # Surrounding components
    engine_components = [
        (3, 4.5, 'Multi-Attribute\nScoring', '#32CD32'),
        (3, 3.5, 'Trust\nVerification', '#32CD32'),
        (5.5, 5, 'Auction\nClearing', '#32CD32'),
        (5.5, 3, 'Payment\nCalculation', '#32CD32'),
        (12.5, 5, 'Reputation\nSystem', '#32CD32'),
        (12.5, 3, 'Security\nMonitor', '#32CD32'),
        (15, 4.5, 'Dynamic\nOptimizer', '#32CD32'),
        (15, 3.5, 'Collusion\nDetector', '#32CD32')
    ]
    
    for x, y, label, color in engine_components:
        circle = Circle((x, y), 0.6, facecolor=color, edgecolor='black', 
                       linewidth=2, alpha=0.8)
        ax.add_patch(circle)
        ax.text(x, y, label, ha='center', va='center', fontsize=8,
                color='white', fontweight='bold')
    
    # Draw connections to VCG Engine
    for x, y, _, _ in engine_components:
        if x < 7:
            arrow_x = 7
        elif x > 11:
            arrow_x = 11
        else:
            arrow_x = 9
            
        arrow = FancyArrowPatch((x, y), (arrow_x, 4.5),
                              connectionstyle="arc3,rad=0.2",
                              arrowstyle='->', lw=1.5, color='darkgreen', alpha=0.6)
        ax.add_patch(arrow)
    
    # Data Layer Components
    data_components = [
        (2, 1.5, 2, 0.8, 'PostgreSQL\nDatabase', '#FFD700'),
        (5, 1.5, 2, 0.8, 'Redis\nCache', '#FFD700'),
        (8, 1.5, 2, 0.8, 'Event\nStore', '#FFD700'),
        (11, 1.5, 2, 0.8, 'File\nStorage', '#FFD700'),
        (14, 1.5, 2, 0.8, 'Analytics\nDB', '#FFD700'),
        (17, 1.5, 1, 0.8, 'Logs', '#FFD700')
    ]
    
    for x, y, w, h, label, color in data_components[:5]:
        box = FancyBboxPatch((x-w/2, y-h/2), w, h,
                           boxstyle="round,pad=0.05",
                           facecolor=color, edgecolor='black', linewidth=1)
        ax.add_patch(box)
        ax.text(x, y, label, ha='center', va='center', fontsize=9,
                fontweight='bold')
    
    # Logs (smaller)
    box = FancyBboxPatch((16.5, 1.1), 1, 0.8,
                       boxstyle="round,pad=0.05",
                       facecolor='#FFD700', edgecolor='black', linewidth=1)
    ax.add_patch(box)
    ax.text(17, 1.5, 'Logs', ha='center', va='center', fontsize=9,
            fontweight='bold')
    
    # Security perimeter
    security_box = Rectangle((0.5, 0.5), 18, 10.5, fill=False, 
                           edgecolor='red', linewidth=3, linestyle='--')
    ax.add_patch(security_box)
    ax.text(0.7, 10.8, 'Security Perimeter', fontsize=10, color='red', 
            fontweight='bold', style='italic')
    
    # External connections
    external = [
        (-1, 9, 'Organizations'),
        (-1, 7, 'AI Agents'),
        (-1, 5, 'LLM APIs'),
        (19, 9, 'Monitoring'),
        (19, 7, 'Admin'),
        (19, 5, 'Analytics')
    ]
    
    for x, y, label in external:
        if x < 0:
            box = FancyBboxPatch((x-1, y-0.3), 1.5, 0.6,
                               boxstyle="round,pad=0.05",
                               facecolor='lightgray', edgecolor='black', linewidth=1)
            ax.add_patch(box)
            ax.text(x-0.25, y, label, ha='center', va='center', fontsize=9)
            # Arrow into system
            arrow = FancyArrowPatch((x+0.5, y), (1, y),
                                  arrowstyle='->', lw=2, color='blue')
            ax.add_patch(arrow)
        else:
            box = FancyBboxPatch((x-0.5, y-0.3), 1.5, 0.6,
                               boxstyle="round,pad=0.05",
                               facecolor='lightgray', edgecolor='black', linewidth=1)
            ax.add_patch(box)
            ax.text(x+0.25, y, label, ha='center', va='center', fontsize=9)
            # Arrow from system
            arrow = FancyArrowPatch((18, y), (x-0.5, y),
                                  arrowstyle='->', lw=2, color='green')
            ax.add_patch(arrow)
    
    # Phase indicators on the right
    phases_y = [9, 7, 5, 3]
    phases_labels = ['Phase 1:\nSecurity', 'Phase 2:\nEnhanced', 
                     'Phase 3:\nOptimal', 'Phase 4:\nAdvanced']
    phases_colors = ['#ffcccc', '#ccffcc', '#ccccff', '#ffffcc']
    
    for y, label, color in zip(phases_y, phases_labels, phases_colors):
        box = FancyBboxPatch((20, y-0.4), 2, 0.8,
                           boxstyle="round,pad=0.05",
                           facecolor=color, edgecolor='black', linewidth=1)
        ax.add_patch(box)
        ax.text(21, y, label, ha='center', va='center', fontsize=9,
                fontweight='bold')
    
    # Key metrics box
    metrics_text = ('Key System Metrics:\n'
                   '• Throughput: 1000 TPS\n'
                   '• Latency: <100ms\n'
                   '• Availability: 99.9%\n'
                   '• Efficiency: 90%\n'
                   '• Security Score: A+')
    
    metrics_box = FancyBboxPatch((20, 0.5), 3, 2,
                               boxstyle="round,pad=0.1",
                               facecolor='lightyellow', edgecolor='black', linewidth=2)
    ax.add_patch(metrics_box)
    ax.text(21.5, 1.5, metrics_text, ha='center', va='center', fontsize=9)
    
    # Add data flow indicators
    main_flows = [
        # Vertical flows
        (5, 8.5, 5, 6.5, 'Bids'),
        (9, 6.5, 9, 5.5, 'Auctions'),
        (9, 3, 9, 2, 'Results'),
        # Horizontal flows
        (3.5, 6.7, 5.5, 6.7, ''),
        (12.5, 6.7, 14.5, 6.7, ''),
    ]
    
    for x1, y1, x2, y2, label in main_flows:
        arrow = FancyArrowPatch((x1, y1), (x2, y2),
                              arrowstyle='->', lw=2, color='purple', alpha=0.5)
        ax.add_patch(arrow)
        if label:
            mid_x, mid_y = (x1+x2)/2, (y1+y2)/2
            ax.text(mid_x+0.3, mid_y, label, fontsize=8, color='purple',
                   style='italic')
    
    ax.set_xlim(-2, 23)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('progressive_trust_vcg_architecture.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_economic_mechanism_flowchart():
    """Create detailed flowchart of the economic mechanism"""
    fig, ax = plt.subplots(figsize=(14, 16))
    
    # Title
    ax.text(7, 15.5, 'Progressive Trust VCG: Economic Mechanism Flow', 
            ha='center', fontsize=18, fontweight='bold')
    
    # Define process steps with positions and connections
    steps = [
        # (x, y, width, height, label, color, shape)
        (7, 14, 3, 0.8, '1. Contract Published', '#ff9999', 'box'),
        (7, 12.5, 3, 0.8, '2. Agent Discovery', '#ffcc99', 'box'),
        (7, 11, 3, 0.8, '3. Capability Check', '#ffff99', 'diamond'),
        (3, 9.5, 2.5, 0.8, '4a. Trust\nVerification', '#ccff99', 'box'),
        (11, 9.5, 2.5, 0.8, '4b. Bid\nPreparation', '#ccff99', 'box'),
        (7, 8, 3, 0.8, '5. Multi-Attribute\nBid Submission', '#99ff99', 'box'),
        (7, 6.5, 3, 0.8, '6. Scoring &\nRanking', '#99ffcc', 'box'),
        (7, 5, 3, 0.8, '7. VCG Payment\nCalculation', '#99ccff', 'box'),
        (7, 3.5, 3, 0.8, '8. Winner\nNotification', '#9999ff', 'box'),
        (3, 2, 2.5, 0.8, '9a. Task\nExecution', '#cc99ff', 'box'),
        (11, 2, 2.5, 0.8, '9b. Quality\nMonitoring', '#cc99ff', 'box'),
        (7, 0.5, 3, 0.8, '10. Reputation\nUpdate', '#ff99ff', 'box')
    ]
    
    # Draw steps
    for x, y, w, h, label, color, shape in steps:
        if shape == 'diamond':
            # Diamond for decision
            points = [(x, y+h/2), (x+w/2, y+h), (x+w, y+h/2), (x+w/2, y)]
            diamond = Polygon([(x-w/2, y), (x, y+h/2), (x+w/2, y), (x, y-h/2)], 
                            facecolor=color, edgecolor='black', linewidth=2)
            ax.add_patch(diamond)
        else:
            # Rectangle for process
            box = FancyBboxPatch((x-w/2, y-h/2), w, h,
                               boxstyle="round,pad=0.05",
                               facecolor=color, edgecolor='black', linewidth=2)
            ax.add_patch(box)
        
        ax.text(x, y, label, ha='center', va='center', fontsize=10,
                fontweight='bold')
    
    # Draw connections
    connections = [
        (7, 13.6, 7, 12.9),  # 1->2
        (7, 12.1, 7, 11.4),  # 2->3
        (6, 10.6, 4.25, 9.9),  # 3->4a
        (8, 10.6, 10.25, 9.9),  # 3->4b
        (4.25, 9.1, 6, 8.4),  # 4a->5
        (10.25, 9.1, 8, 8.4),  # 4b->5
        (7, 7.6, 7, 6.9),  # 5->6
        (7, 6.1, 7, 5.4),  # 6->7
        (7, 4.6, 7, 3.9),  # 7->8
        (6, 3.1, 4.25, 2.4),  # 8->9a
        (8, 3.1, 10.25, 2.4),  # 8->9b
        (4.25, 1.6, 6, 0.9),  # 9a->10
        (10.25, 1.6, 8, 0.9),  # 9b->10
    ]
    
    for x1, y1, x2, y2 in connections:
        arrow = FancyArrowPatch((x1, y1), (x2, y2),
                              arrowstyle='->', lw=2, color='black')
        ax.add_patch(arrow)
    
    # Add decision branches
    ax.text(4.5, 10.8, 'Pass', fontsize=9, color='green', fontweight='bold')
    ax.text(9.5, 10.8, 'Prepare', fontsize=9, color='blue', fontweight='bold')
    
    # Add side annotations
    # Trust levels on left
    trust_y = [13, 11.5, 10, 8.5, 7]
    trust_labels = ['New: $100 max', 'Basic: $500 max', 'Verified: $2K max', 
                    'Trusted: $5K max', 'Premier: Unlimited']
    trust_colors = ['#ffcccc', '#ffddcc', '#ffffcc', '#ccffcc', '#ccffff']
    
    for y, label, color in zip(trust_y, trust_labels, trust_colors):
        box = Rectangle((0.2, y-0.3), 1.8, 0.6, facecolor=color, 
                       edgecolor='black', linewidth=1)
        ax.add_patch(box)
        ax.text(1.1, y, label, ha='center', va='center', fontsize=8)
    
    ax.text(1.1, 13.8, 'Trust Levels', ha='center', fontsize=10, 
            fontweight='bold')
    
    # Scoring formula on right
    formula_text = ('Scoring Formula:\n\n'
                   'Score = w₁ × Quality\n'
                   '      + w₂ × Speed\n'
                   '      + w₃ × Specialization\n'
                   '      - w₄ × Price\n'
                   '      × Trust_Modifier\n\n'
                   'Weights:\n'
                   'w₁ = 0.4 (quality)\n'
                   'w₂ = 0.2 (speed)\n'
                   'w₃ = 0.2 (specialization)\n'
                   'w₄ = 0.2 (price)')
    
    formula_box = Rectangle((12, 11), 2, 3.5, facecolor='lightyellow',
                          edgecolor='black', linewidth=2)
    ax.add_patch(formula_box)
    ax.text(13, 12.75, formula_text, ha='center', va='center', fontsize=8)
    
    # VCG payment explanation
    vcg_text = ('VCG Payment:\n\n'
                'Winner pays the\n'
                'minimum amount\n'
                'needed to win\n\n'
                'P = Second-best\n'
                '    utility equivalent\n\n'
                'Ensures truthful\n'
                'bidding')
    
    vcg_box = Rectangle((12, 3.5), 2, 2.5, facecolor='lightcyan',
                       edgecolor='black', linewidth=2)
    ax.add_patch(vcg_box)
    ax.text(13, 4.75, vcg_text, ha='center', va='center', fontsize=8)
    
    # Efficiency indicators
    eff_text = ('Efficiency Gains:\n'
                'Phase 1: +23%\n'
                'Phase 2: +13%\n'
                'Phase 3: +7%\n'
                'Phase 4: +5%\n'
                'Total: +48%')
    
    eff_box = Rectangle((0.2, 4), 1.8, 2, facecolor='lightgreen',
                       edgecolor='black', linewidth=2)
    ax.add_patch(eff_box)
    ax.text(1.1, 5, eff_text, ha='center', va='center', fontsize=8)
    
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 16)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('economic_mechanism_flowchart.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Generate system architecture visualizations"""
    print("Generating Progressive Trust VCG system architecture visualizations...")
    
    create_system_architecture()
    print("✓ System architecture diagram created")
    
    create_economic_mechanism_flowchart()
    print("✓ Economic mechanism flowchart created")
    
    print("\nSystem architecture visualizations generated successfully!")

if __name__ == "__main__":
    main()