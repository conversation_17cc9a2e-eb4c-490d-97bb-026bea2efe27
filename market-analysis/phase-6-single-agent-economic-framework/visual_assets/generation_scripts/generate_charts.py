#!/usr/bin/env python3
"""
Generate visualization charts for VibeLaunch Progressive Trust VCG Framework
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.patches import Rectangle
import pandas as pd

# Set style
plt.style.use('seaborn-v0_8-darkgrid')
sns.set_palette("husl")

def create_efficiency_progression_chart():
    """Create efficiency progression chart showing improvement over 12 months"""
    months = np.arange(0, 13)
    efficiency = [42, 45, 55, 65, 70, 74, 78, 81, 83, 85, 87, 89, 90]
    
    phases = [
        ('Current', 0, 0, 42),
        ('Phase 1:\nSecurity Foundation', 1, 3, 65),
        ('Phase 2:\nEnhanced Mechanisms', 4, 6, 78),
        ('Phase 3:\nTheoretical Optimality', 7, 9, 85),
        ('Phase 4:\nAdvanced Features', 10, 12, 90)
    ]
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Plot main efficiency line
    ax.plot(months, efficiency, 'b-', linewidth=3, marker='o', markersize=8, label='Allocative Efficiency')
    
    # Add phase backgrounds
    colors = ['#ffcccc', '#ccffcc', '#ccccff', '#ffffcc', '#ffccff']
    for i, (phase_name, start, end, target) in enumerate(phases[1:], 0):
        ax.axvspan(start, end, alpha=0.2, color=colors[i])
        ax.text((start + end) / 2, 95, phase_name, ha='center', va='top', fontsize=10, fontweight='bold')
    
    # Add horizontal lines for targets
    ax.axhline(y=90, color='g', linestyle='--', alpha=0.5, label='Target (90%)')
    ax.axhline(y=42, color='r', linestyle='--', alpha=0.5, label='Baseline (42%)')
    
    # Annotations
    ax.annotate('58% improvement', xy=(12, 90), xytext=(10, 75),
                arrowprops=dict(arrowstyle='->', color='green', lw=2),
                fontsize=12, fontweight='bold', color='green')
    
    ax.set_xlabel('Month', fontsize=14)
    ax.set_ylabel('Allocative Efficiency (%)', fontsize=14)
    ax.set_title('VibeLaunch Market Efficiency Progression', fontsize=16, fontweight='bold')
    ax.set_xlim(-0.5, 12.5)
    ax.set_ylim(35, 100)
    ax.legend(loc='lower right')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('efficiency_progression.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_market_mechanism_flow():
    """Create flow diagram of Progressive Trust VCG mechanism"""
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # Define components
    components = [
        # (x, y, width, height, label, color)
        (1, 8, 2, 1.5, 'Contract\nPublished', '#ff9999'),
        (5, 8, 2, 1.5, 'Multi-Attribute\nBids', '#99ccff'),
        (9, 8, 2, 1.5, 'Verification\n& Scoring', '#99ff99'),
        (13, 8, 2, 1.5, 'VCG\nAuction', '#ffcc99'),
        
        (1, 5, 2, 1.5, 'Agent\nRegistry', '#cccccc'),
        (5, 5, 2, 1.5, 'Reputation\nSystem', '#ffccff'),
        (9, 5, 2, 1.5, 'Security\nMonitoring', '#ccffff'),
        (13, 5, 2, 1.5, 'Payment\nCalculation', '#ffffcc'),
        
        (7, 2, 2, 1.5, 'Winner\nSelection', '#ff99ff'),
        (7, -1, 2, 1.5, 'Task\nExecution', '#99ff99'),
    ]
    
    # Draw components
    for x, y, w, h, label, color in components:
        rect = Rectangle((x-w/2, y-h/2), w, h, facecolor=color, edgecolor='black', linewidth=2)
        ax.add_patch(rect)
        ax.text(x, y, label, ha='center', va='center', fontsize=11, fontweight='bold')
    
    # Draw arrows
    arrows = [
        # (x1, y1, x2, y2)
        (2, 8, 4, 8),      # Contract -> Bids
        (6, 8, 8, 8),      # Bids -> Verification
        (10, 8, 12, 8),    # Verification -> VCG
        (2, 6.5, 2, 6.5),  # Registry connection
        (6, 6.5, 6, 6.5),  # Reputation connection
        (10, 6.5, 10, 6.5),# Security connection
        (14, 6.5, 14, 5),  # VCG -> Payment
        (14, 5, 8, 3.5),   # Payment -> Winner
        (8, 0.5, 8, -1),   # Winner -> Execution
    ]
    
    for x1, y1, x2, y2 in arrows:
        ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                   arrowprops=dict(arrowstyle='->', lw=2, color='black'))
    
    # Add trust levels
    trust_levels = ['New', 'Basic', 'Verified', 'Trusted', 'Premier']
    colors_trust = ['#ffcccc', '#ffddcc', '#ffffcc', '#ccffcc', '#ccffff']
    
    for i, (level, color) in enumerate(zip(trust_levels, colors_trust)):
        rect = Rectangle((16, 7-i*1.2), 1.5, 1, facecolor=color, edgecolor='black', linewidth=1)
        ax.add_patch(rect)
        ax.text(16.75, 7.5-i*1.2, level, ha='center', va='center', fontsize=9)
    
    ax.text(16.75, 8.5, 'Trust Levels', ha='center', fontsize=11, fontweight='bold')
    
    # Add phase indicators
    ax.text(8, 10, 'Progressive Trust VCG Mechanism', ha='center', fontsize=16, fontweight='bold')
    
    ax.set_xlim(-1, 19)
    ax.set_ylim(-3, 11)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('market_mechanism_flow.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_network_effects_model():
    """Create network effects visualization"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # Left plot: Network value functions
    n_agents = np.linspace(0, 200, 100)
    n_orgs = np.linspace(0, 100, 100)
    
    # Organization value from agents
    v_org = 1000 * n_agents**0.7
    # Agent value from organizations  
    v_agent = 500 * n_orgs**0.4
    
    ax1.plot(n_agents, v_org/1000, 'b-', linewidth=3, label='Org Value from Agents')
    ax1.set_xlabel('Number of Agents', fontsize=12)
    ax1.set_ylabel('Value (thousands)', fontsize=12)
    ax1.set_title('Organization Network Value', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    ax1_twin = ax1.twiny()
    ax1_twin.plot(n_orgs, v_agent/1000, 'r-', linewidth=3, label='Agent Value from Orgs')
    ax1_twin.set_xlabel('Number of Organizations', fontsize=12, color='r')
    ax1_twin.tick_params(axis='x', labelcolor='r')
    
    # Add critical mass indicators
    ax1.axvline(x=50, color='g', linestyle='--', alpha=0.5)
    ax1.text(52, 20, 'Critical Mass:\n50 Agents', fontsize=10, color='g')
    
    # Right plot: Total network value heatmap
    n_agents_grid, n_orgs_grid = np.meshgrid(
        np.linspace(10, 200, 50),
        np.linspace(5, 100, 50)
    )
    
    quality_factor = 0.9  # With our mechanism
    total_value = 0.001 * n_agents_grid * n_orgs_grid * quality_factor
    
    im = ax2.contourf(n_agents_grid, n_orgs_grid, total_value, levels=20, cmap='viridis')
    ax2.set_xlabel('Number of Agents', fontsize=12)
    ax2.set_ylabel('Number of Organizations', fontsize=12)
    ax2.set_title('Total Network Value (Quality Factor = 0.9)', fontsize=14, fontweight='bold')
    
    # Add critical mass point
    ax2.plot(50, 20, 'r*', markersize=15, label='Critical Mass')
    ax2.legend()
    
    # Colorbar
    cbar = plt.colorbar(im, ax=ax2)
    cbar.set_label('Network Value (millions)', fontsize=12)
    
    plt.tight_layout()
    plt.savefig('network_effects_model.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_implementation_timeline():
    """Create Gantt chart style implementation timeline"""
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Define tasks
    tasks = [
        ('Security Infrastructure', 0, 1, '#ff9999'),
        ('Multi-Attribute Bidding', 1, 2, '#ff9999'),
        ('Second-Score Payment', 2, 3, '#ff9999'),
        ('Reputation System', 3, 4, '#99ccff'),
        ('Enhanced Profiles', 4, 5, '#99ccff'),
        ('Dynamic Scoring', 5, 6, '#99ccff'),
        ('VCG Mechanism Core', 6, 7, '#99ff99'),
        ('Equilibrium Analysis', 7, 8, '#99ff99'),
        ('Collusion Detection', 8, 9, '#99ff99'),
        ('Combinatorial Bidding', 9, 10, '#ffcc99'),
        ('Stable Matching', 10, 11, '#ffcc99'),
        ('Market Making', 11, 12, '#ffcc99'),
    ]
    
    # Plot tasks
    for i, (task, start, end, color) in enumerate(tasks):
        ax.barh(i, end - start, left=start, height=0.8, color=color, edgecolor='black', linewidth=1)
        ax.text(start + (end - start) / 2, i, task, ha='center', va='center', fontsize=10, fontweight='bold')
    
    # Add phase labels
    phases = [
        ('Phase 1: Foundation Security', 0, 3, 12.5),
        ('Phase 2: Enhanced Mechanisms', 3, 6, 11.5),
        ('Phase 3: Theoretical Optimality', 6, 9, 10.5),
        ('Phase 4: Advanced Features', 9, 12, 9.5),
    ]
    
    for phase, start, end, y in phases:
        ax.text((start + end) / 2, y, phase, ha='center', va='center', 
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8),
                fontsize=11, fontweight='bold')
    
    # Add efficiency milestones
    milestones = [(3, 65), (6, 78), (9, 85), (12, 90)]
    for month, eff in milestones:
        ax.axvline(x=month, color='green', linestyle='--', alpha=0.5)
        ax.text(month, -1, f'{eff}%', ha='center', fontsize=10, color='green', fontweight='bold')
    
    ax.set_xlabel('Month', fontsize=14)
    ax.set_ylabel('Implementation Tasks', fontsize=14)
    ax.set_title('VibeLaunch Progressive Trust VCG Implementation Timeline', fontsize=16, fontweight='bold')
    ax.set_xlim(-0.5, 12.5)
    ax.set_ylim(-2, 13)
    ax.grid(True, axis='x', alpha=0.3)
    ax.set_yticks([])
    
    plt.tight_layout()
    plt.savefig('implementation_timeline.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_revenue_optimization_curve():
    """Create revenue optimization curve showing optimal commission rate"""
    commission_rates = np.linspace(0, 0.5, 100)
    
    # Revenue function R(c) = c * V * (1-c)^1.5
    base_volume = 1000
    elasticity = 1.5
    revenue = commission_rates * base_volume * (1 - commission_rates)**elasticity
    
    # Find optimal rate
    optimal_idx = np.argmax(revenue)
    optimal_rate = commission_rates[optimal_idx]
    optimal_revenue = revenue[optimal_idx]
    
    fig, ax = plt.subplots(figsize=(10, 6))
    
    ax.plot(commission_rates * 100, revenue, 'b-', linewidth=3, label='Platform Revenue')
    ax.plot(optimal_rate * 100, optimal_revenue, 'ro', markersize=10, label=f'Optimal: {optimal_rate*100:.1f}%')
    
    # Add shaded regions
    ax.axvspan(15, 20, alpha=0.2, color='green', label='Recommended Range')
    ax.axvline(x=optimal_rate * 100, color='r', linestyle='--', alpha=0.5)
    
    # Annotations
    ax.annotate(f'Theoretical Optimal\n{optimal_rate*100:.1f}%', 
                xy=(optimal_rate * 100, optimal_revenue), 
                xytext=(optimal_rate * 100 + 10, optimal_revenue - 50),
                arrowprops=dict(arrowstyle='->', color='red', lw=2),
                fontsize=12, fontweight='bold')
    
    ax.annotate('Adjusted for\nCompetition\n15-20%', 
                xy=(17.5, revenue[35]), 
                xytext=(25, revenue[35] + 50),
                arrowprops=dict(arrowstyle='->', color='green', lw=2),
                fontsize=12, fontweight='bold', color='green')
    
    ax.set_xlabel('Commission Rate (%)', fontsize=14)
    ax.set_ylabel('Platform Revenue', fontsize=14)
    ax.set_title('Revenue Optimization: Commission Rate Analysis', fontsize=16, fontweight='bold')
    ax.legend(loc='upper right')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('revenue_optimization_curve.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Generate all charts"""
    print("Generating VibeLaunch Progressive Trust VCG visualizations...")
    
    create_efficiency_progression_chart()
    print("✓ Efficiency progression chart created")
    
    create_market_mechanism_flow()
    print("✓ Market mechanism flow diagram created")
    
    create_network_effects_model()
    print("✓ Network effects model created")
    
    create_implementation_timeline()
    print("✓ Implementation timeline created")
    
    create_revenue_optimization_curve()
    print("✓ Revenue optimization curve created")
    
    print("\nAll visualizations generated successfully!")

if __name__ == "__main__":
    main()