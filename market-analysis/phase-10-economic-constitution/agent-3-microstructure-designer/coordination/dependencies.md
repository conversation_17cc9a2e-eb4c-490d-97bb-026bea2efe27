# Agent 3 (Market Microstructure Designer) - Coordination Dependencies

## Your Dependencies

### From Agent 1 (Market Theorist):
1. **Mechanism Designs**
   - Team formation algorithms
   - Matching criteria and objective functions
   - Information aggregation protocols
   - Equilibrium conditions

2. **Market Dynamics Theory**
   - Price formation principles
   - Liquidity generation mechanisms
   - Efficiency bounds and proofs
   - Stability conditions

### From Agent 2 (Currency Architect):
1. **Currency Specifications**
   - All currency properties and behaviors
   - Transfer and settlement rules
   - Divisibility and minimum units
   - Special handling requirements

2. **Exchange Requirements**
   - Currency pair definitions
   - Initial pricing models
   - Liquidity targets
   - Market making obligations

## Your Outputs (What Others Need From You)

### For Agent 4 (Financial Ecosystem Engineer):
1. **Market Data Feeds**
   - Real-time price streams
   - Order book depth data
   - Trade history and volumes
   - Volatility calculations
   - Liquidity metrics

2. **Trading Infrastructure**
   - Order placement APIs
   - Execution algorithms
   - Settlement procedures
   - Position tracking
   - Netting services

### For Agent 5 (Governance Philosopher):
1. **Market Control Points**
   - Parameter adjustment interfaces
   - Emergency halt mechanisms
   - Rule modification hooks
   - Surveillance data streams
   - Compliance checkpoints

2. **Market Quality Metrics**
   - Efficiency measurements
   - Fairness indicators
   - Manipulation detection
   - Participation statistics
   - Innovation tracking

## Critical Interfaces

### Order Management Interface
```typescript
interface OrderManagement {
  // Order Types
  orderTypes: {
    market: MarketOrderSpec;
    limit: LimitOrderSpec;
    stop: StopOrderSpec;
    iceberg: IcebergOrderSpec;
    smart: SmartOrderSpec;
  };
  
  // Execution Rules
  matching: {
    algorithm: 'price-time' | 'price-quality-time' | 'custom';
    priorityFunction: string; // Mathematical formula
    partialFills: boolean;
    minimumSize?: number;
  };
  
  // Market Structure
  sessions: {
    continuous: boolean;
    auctionSchedule?: AuctionTime[];
    circuitBreakers: CircuitBreaker[];
    haltConditions: HaltCondition[];
  };
}
```

### Market Data Distribution
```typescript
interface MarketDataFeed {
  // Real-time Data
  orderBook: {
    depth: number; // levels to display
    aggregation: boolean;
    updateFrequency: number; // milliseconds
  };
  
  trades: {
    history: number; // trades to retain
    anonymity: 'full' | 'partial' | 'none';
    delays?: DelaySchedule;
  };
  
  analytics: {
    vwap: boolean;
    volatility: boolean;
    liquidityMetrics: boolean;
    microstructureStats: boolean;
  };
  
  // Access Control
  subscription: {
    tiers: DataTier[];
    authentication: AuthMethod;
    rateLimit: RateLimit;
  };
}
```

## Potential Conflicts

### With Agent 2 (Currency Architect):
- **Issue**: Market needs liquidity vs. non-transferable currencies
- **Your Position**: All assets in markets must be freely tradeable
- **Resolution Approach**: Create wrapped versions or derivatives for restricted currencies

### With Agent 4 (Financial Engineer):
- **Issue**: Market stability vs. derivative complexity
- **Your Position**: Core markets need circuit breakers and position limits
- **Resolution Approach**: Separate tiers - stable base markets and experimental derivative markets

### With Agent 5 (Governance):
- **Issue**: Market efficiency vs. intervention rights
- **Your Position**: Markets work best without interference
- **Resolution Approach**: Pre-defined intervention triggers with clear criteria

## Coordination Timeline

1. **Week 1**: Wait for Agent 1's mechanisms and Agent 2's currencies
2. **Week 2**: Design order book structure and matching engines
3. **Week 3**: Create liquidity provision framework
4. **Week 4**: Build market data distribution and integrate with Agent 2

## Success Criteria

Your work succeeds when:
1. Markets discover prices within 30 seconds
2. Bid-ask spreads remain under 1%
3. Matching achieves 95%+ allocative efficiency
4. No flash crashes or liquidity crises
5. Seamless multi-currency trading

## Synchronization Requirements

### With Agent 2 (Daily during Week 3-4):
- Morning: Review currency specifications
- Afternoon: Test market integration
- End of day: Update settlement procedures
- Focus: Multi-currency order books, exchange mechanics

### With Agent 4 (Week 4):
- Define market data API specifications
- Create derivative underlying feeds
- Design risk metric calculations
- Establish position limits

## Critical Design Decisions

### Order Book Architecture
- Separate books per currency pair vs. unified multi-asset book
- Central limit order book vs. distributed fragments
- Continuous trading vs. periodic auctions

### Liquidity Provision
- Designated market makers vs. incentivized crowds
- Minimum quote obligations
- Rebate structures
- Emergency liquidity protocols

### Information Policy
- Full transparency vs. strategic opacity
- Immediate vs. delayed reporting
- Anonymity levels
- Data access tiers

## Communication Protocol

- **Output Format**: Technical specifications with performance benchmarks
- **Documentation**: Detailed API docs and integration guides
- **Testing**: Market simulators with test harnesses
- **Support**: On-call during Agent 4 and 5 integration phases

---

*Remember: You're building the engine room of the economy. Every trade, every price, every match flows through your infrastructure. Make it fast, fair, and bulletproof.*