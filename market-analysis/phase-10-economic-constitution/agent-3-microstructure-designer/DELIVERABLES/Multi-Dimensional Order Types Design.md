# Multi-Dimensional Order Types Design

## Executive Summary

The multi-currency AI economy requires sophisticated order types that transcend traditional single-dimensional trading, enabling participants to express complex preferences and execute multi-leg strategies across various currencies and market conditions. This document presents the design for three critical multi-dimensional order types: Bundle Orders for atomic multi-currency transactions, Quality-Contingent Orders that dynamically adjust based on quality scores, and Time-Decaying Orders that account for the continuous value erosion of Temporal currency.

These order types represent a significant advancement in market microstructure, allowing participants to optimize across multiple dimensions simultaneously, manage complex risks, and capture value from the unique properties of each currency. The design integrates seamlessly with the core order book architecture and special market mechanisms, ensuring robust execution, data consistency, and high performance within the PostgreSQL-based system.

## Bundle Orders

### Conceptual Overview

Bundle Orders address the critical need for atomic execution of multi-currency transactions, where multiple trades across different currency pairs must execute simultaneously or not at all. This is essential for complex strategies like arbitrage, multi-asset portfolio rebalancing, and project financing that involve acquiring or exchanging multiple resources in a coordinated manner. Without atomic execution, participants face significant risk from partial fills that could leave them with unwanted currency exposures or incomplete resource sets.

Bundle Orders allow participants to define a set of individual trades (legs) that are linked together and treated as a single, indivisible transaction. The system ensures that all legs of a bundle execute at their specified prices and quantities, or the entire bundle is rejected. This atomicity guarantee eliminates the risk of partial execution and provides participants with the confidence to engage in complex multi-currency strategies.

The design of Bundle Orders must handle several challenges. First, it requires sophisticated matching logic that can identify compatible orders across multiple currency pairs simultaneously. Second, it needs robust transaction management that ensures atomicity even under high load or system failures. Third, it must account for the unique properties of each currency involved in the bundle, including Quality multipliers, Temporal decay, and Innovation appreciation.

### Bundle Order Structure

Bundle Orders are represented as a parent order that links multiple child orders (legs), each representing a trade in a specific currency pair. The parent order defines the overall bundle parameters, including atomicity requirements, maximum slippage tolerance across the bundle, and any inter-leg dependencies.

Each child order within the bundle specifies its currency pair, side (buy/sell), quantity, and limit price. The system ensures that all child orders are executed at prices equal to or better than their specified limits. The bundle structure also allows for conditional execution, where the execution of one leg can depend on the successful execution of another leg within the same bundle.

```sql
-- Extension to the order_book table for bundle order specifics
ALTER TABLE order_book
  ADD COLUMN IF NOT EXISTS is_bundle_parent BOOLEAN DEFAULT false,
  ADD COLUMN IF NOT EXISTS bundle_group_id UUID, -- Links all legs of a bundle
  ADD COLUMN IF NOT EXISTS leg_sequence INTEGER, -- Order of execution within a bundle if sequential
  ADD COLUMN IF NOT EXISTS bundle_atomicity_required BOOLEAN DEFAULT true,
  ADD COLUMN IF NOT EXISTS bundle_max_slippage_bps INTEGER; -- Max slippage for the entire bundle in basis points

-- Table to store the individual legs of a bundle order
CREATE TABLE IF NOT EXISTS bundle_order_legs (
  leg_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bundle_group_id UUID NOT NULL, -- Foreign key to a conceptual bundle group or the parent order_id if is_bundle_parent=true
  order_id UUID NOT NULL REFERENCES order_book(id) ON DELETE CASCADE, -- The actual order_book entry for this leg
  
  -- Leg-specific parameters (redundant if order_id links to full order_book entry)
  -- base_currency TEXT NOT NULL,
  -- quote_currency TEXT NOT NULL,
  -- side TEXT NOT NULL,
  -- amount DECIMAL(18, 8) NOT NULL,
  -- limit_price DECIMAL(18, 8) NOT NULL,
  
  -- Inter-leg dependencies (optional)
  depends_on_leg_id UUID REFERENCES bundle_order_legs(leg_id),
  execution_condition TEXT, -- e.g., "leg_A_filled_price <= X"
  
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'matched', 'failed', 'cancelled')),
  
  CONSTRAINT fk_bundle_group FOREIGN KEY (bundle_group_id) REFERENCES order_book(id) WHERE is_bundle_parent = true
);

CREATE INDEX IF NOT EXISTS idx_bundle_order_legs_group ON bundle_order_legs (bundle_group_id);
```

The `bundle_group_id` in `order_book` (when `is_bundle_parent` is false) or in `bundle_order_legs` links all legs of a single bundle. If a parent order concept is used in `order_book`, `bundle_group_id` in `bundle_order_legs` would reference the `id` of the parent `order_book` entry.

### Atomic Execution Engine for Bundles

The Atomic Execution Engine for Bundles ensures that all legs of a bundle order execute simultaneously or not at all. This requires sophisticated transaction management and coordination across multiple order books and liquidity sources.

The engine operates in several stages. First, it validates the bundle order, ensuring that all legs are well-defined, have sufficient liquidity, and meet any inter-leg dependency requirements. Second, it reserves the necessary liquidity across all involved currency pairs, preventing other orders from consuming the required liquidity while the bundle is being processed. Third, it attempts to execute all legs simultaneously within a single database transaction. If all legs execute successfully, the transaction is committed. If any leg fails, the entire transaction is rolled back, and the reserved liquidity is released.

```sql
CREATE OR REPLACE FUNCTION execute_bundle_order(
  p_bundle_group_id UUID
) RETURNS JSONB AS $$
DECLARE
  v_bundle_legs RECORD[];
  v_leg RECORD;
  v_execution_results JSONB := '[]'::JSONB;
  v_all_legs_successful BOOLEAN := true;
  v_leg_result JSONB;
  v_overall_slippage DECIMAL(10, 6) := 0;
  v_bundle_parent RECORD;
BEGIN
  -- Get parent bundle order details for slippage etc.
  SELECT * INTO v_bundle_parent FROM order_book WHERE id = p_bundle_group_id AND is_bundle_parent = true;
  IF NOT FOUND THEN
    RETURN jsonb_build_object('status', 'failed', 'reason', 'Bundle parent order not found');
  END IF;

  -- Get all legs for the bundle
  SELECT array_agg(bol.*) INTO v_bundle_legs
  FROM bundle_order_legs bol
  JOIN order_book ob ON bol.order_id = ob.id
  WHERE bol.bundle_group_id = p_bundle_group_id
  ORDER BY ob.leg_sequence ASC; -- Assuming leg_sequence is on order_book table for legs

  IF array_length(v_bundle_legs, 1) IS NULL THEN
    RETURN jsonb_build_object('status', 'failed', 'reason', 'No legs found for bundle');
  END IF;

  -- Start atomic transaction for the bundle
  SAVEPOINT bundle_execution_start;

  FOR v_leg IN SELECT * FROM unnest(v_bundle_legs)
  LOOP
    BEGIN
      -- Attempt to match this leg (simplified - actual matching is complex)
      v_leg_result := try_match_order_leg(v_leg.order_id);

      IF (v_leg_result->>'status')::TEXT = 'filled' OR (v_leg_result->>'status')::TEXT = 'partially_filled' THEN
        v_execution_results := v_execution_results || v_leg_result;
        UPDATE bundle_order_legs SET status = 'matched' WHERE leg_id = v_leg.leg_id;
        -- Accumulate slippage if applicable (simplified)
        -- v_overall_slippage := v_overall_slippage + (v_leg_result->>'slippage_bps')::DECIMAL;
      ELSE
        v_all_legs_successful := false;
        UPDATE bundle_order_legs SET status = 'failed' WHERE leg_id = v_leg.leg_id;
        v_execution_results := v_execution_results || jsonb_build_object('leg_id', v_leg.leg_id, 'status', 'failed', 'reason', v_leg_result->>'reason');
        EXIT; -- Exit loop if one leg fails
      END IF;
    EXCEPTION WHEN OTHERS THEN
      v_all_legs_successful := false;
      UPDATE bundle_order_legs SET status = 'failed' WHERE leg_id = v_leg.leg_id;
      v_execution_results := v_execution_results || jsonb_build_object('leg_id', v_leg.leg_id, 'status', 'failed', 'reason', SQLERRM);
      EXIT;
    END;
  END LOOP;

  -- Check overall bundle constraints (e.g., slippage)
  -- IF v_bundle_parent.bundle_max_slippage_bps IS NOT NULL AND v_overall_slippage > v_bundle_parent.bundle_max_slippage_bps THEN
  --   v_all_legs_successful := false;
  --   -- Add reason for slippage violation
  -- END IF;

  IF v_all_legs_successful THEN
    RELEASE SAVEPOINT bundle_execution_start;
    UPDATE order_book SET status = 'filled', updated_at = now() WHERE id = p_bundle_group_id AND is_bundle_parent = true;
    RETURN jsonb_build_object('status', 'success', 'legs_executed', array_length(v_bundle_legs, 1), 'results', v_execution_results);
  ELSE
    ROLLBACK TO bundle_execution_start;
    UPDATE order_book SET status = 'failed', updated_at = now() WHERE id = p_bundle_group_id AND is_bundle_parent = true;
    RETURN jsonb_build_object('status', 'failed', 'reason', 'One or more legs failed or bundle constraints violated', 'results', v_execution_results);
  END IF;
END;
$$ LANGUAGE plpgsql;
```

The engine also handles currency-specific properties within bundle executions. Quality multipliers are applied consistently across all legs involving Quality currency. Temporal decay is calculated for all Temporal currency legs based on the bundle execution time. Reliability access tokens are verified and transferred atomically. Innovation appreciation is factored into pricing for all Innovation currency legs.

### Slippage Control for Bundles

Slippage represents a significant risk for Bundle Orders, where price movements during execution can result in unfavorable execution prices across multiple legs. The system implements sophisticated slippage control mechanisms that allow participants to specify maximum acceptable slippage for the entire bundle, not just individual legs.

The slippage calculation considers the combined price impact across all legs of the bundle, ensuring that the total execution cost remains within the participant's specified tolerance. If the expected slippage exceeds the maximum acceptable level, the bundle is rejected before execution, protecting participants from excessive price movements.

The system also implements intelligent execution strategies that minimize slippage for Bundle Orders. This includes routing legs to liquidity sources with minimal price impact, timing execution to coincide with periods of high liquidity, and potentially splitting large bundles into smaller sub-bundles that can be executed with less market impact.

## Quality-Contingent Orders

### Conceptual Overview

Quality-Contingent Orders leverage the unique multiplicative effect of Quality currency, allowing participants to create orders whose terms dynamically adjust based on the quality score of the counterparty or the underlying asset. This enables sophisticated trading strategies that optimize for both price and quality simultaneously, creating powerful incentives for quality improvement across the ecosystem.

These orders address the challenge of pricing quality in traditional markets, where quality is often a subjective attribute that is difficult to quantify and incorporate into trading decisions. By directly linking order terms to measurable quality scores, Quality-Contingent Orders create transparent and objective mechanisms for valuing quality contributions.

The design of Quality-Contingent Orders must handle several complexities. First, it requires real-time access to accurate quality scores for all market participants and assets. Second, it needs sophisticated matching logic that can evaluate quality conditions and apply appropriate adjustments during order execution. Third, it must integrate seamlessly with the Quality Multiplier Markets to ensure consistent valuation of quality effects across all transaction types.

### Order Structure and Conditions

Quality-Contingent Orders extend the standard order structure with additional fields that specify quality-related conditions and adjustments. These include minimum quality thresholds, quality-based price multipliers, and quality bonus parameters.

Participants can specify orders that only execute if the counterparty meets a minimum quality score, or orders whose price automatically adjusts based on the counterparty's quality score. For example, a buyer might offer a higher price for goods or services from a high-quality seller, while a seller might offer a discount to a high-quality buyer who is likely to provide valuable feedback or repeat business.

```sql
-- Extension to the order_book table for quality-contingent specifics
ALTER TABLE order_book
  ADD COLUMN IF NOT EXISTS is_quality_contingent BOOLEAN DEFAULT false,
  ADD COLUMN IF NOT EXISTS min_counterparty_quality_score DECIMAL(10, 4) CHECK (min_counterparty_quality_score BETWEEN 0 AND 1),
  ADD COLUMN IF NOT EXISTS quality_price_adjustment_formula TEXT, -- e.g., "base_price * (1 + 0.1 * (counterparty_quality - 0.8))"
  ADD COLUMN IF NOT EXISTS quality_bonus_threshold DECIMAL(10, 4) CHECK (quality_bonus_threshold BETWEEN 0 AND 1),
  ADD COLUMN IF NOT EXISTS quality_bonus_amount DECIMAL(18, 8);

-- Example: A buy order that pays more if seller's quality is high
-- INSERT INTO order_book (..., is_quality_contingent, min_counterparty_quality_score, quality_price_adjustment_formula, ...)
-- VALUES (..., true, 0.7, 'limit_price * (1 + 0.05 * (counterparty_quality_score - 0.7))', ...);
```

The `quality_price_adjustment_formula` allows for flexible definition of how price should change based on the counterparty's quality. This could be a simple linear adjustment, a stepped function, or a more complex formula. The execution engine would need to parse and evaluate this formula dynamically.

### Quality-Adjusted Matching Logic

The matching engine for Quality-Contingent Orders implements sophisticated logic that evaluates quality conditions in real-time and applies appropriate adjustments during order execution. When a Quality-Contingent Order encounters a potential match, the system retrieves the counterparty's current quality score and evaluates the order's quality conditions.

If the counterparty meets the minimum quality threshold, the system calculates the quality-adjusted price based on the specified formula or multiplier. This adjusted price is then used to determine if a match should occur and at what final execution price. The matching logic ensures that quality adjustments are applied consistently and transparently, providing clear incentives for quality improvement.

```sql
CREATE OR REPLACE FUNCTION evaluate_quality_contingent_match(
  p_order_id UUID,
  p_counterparty_order_id UUID
) RETURNS JSONB AS $$
DECLARE
  v_order RECORD;
  v_counterparty_order RECORD;
  v_counterparty_agent_id UUID;
  v_counterparty_quality_score DECIMAL(10, 4);
  v_adjusted_price DECIMAL(18, 8);
  v_can_match BOOLEAN := true;
  v_match_details JSONB;
BEGIN
  SELECT * INTO v_order FROM order_book WHERE id = p_order_id;
  SELECT * INTO v_counterparty_order FROM order_book WHERE id = p_counterparty_order_id;

  -- Determine counterparty agent ID
  v_counterparty_agent_id := v_counterparty_order.agent_role; -- Assuming agent_role stores agent_id

  -- Get counterparty quality score (simplified - needs a proper function)
  SELECT quality_score INTO v_counterparty_quality_score 
  FROM agent_profiles WHERE agent_id = v_counterparty_agent_id;
  IF NOT FOUND THEN v_counterparty_quality_score := 0.5; END IF; -- Default if not found

  v_adjusted_price := v_order.price; -- Start with order's stated price

  IF v_order.is_quality_contingent THEN
    -- Check minimum quality threshold
    IF v_order.min_counterparty_quality_score IS NOT NULL AND 
       v_counterparty_quality_score < v_order.min_counterparty_quality_score THEN
      v_can_match := false;
      RETURN jsonb_build_object('can_match', false, 'reason', 'Counterparty quality below minimum threshold');
    END IF;

    -- Apply price adjustment formula (requires a robust formula evaluator)
    IF v_order.quality_price_adjustment_formula IS NOT NULL THEN
      -- This is a placeholder for a complex formula evaluation engine
      -- For example, if formula is 'price * (1 + 0.1 * (quality - 0.8))'
      -- v_adjusted_price := v_order.price * (1 + 0.1 * (v_counterparty_quality_score - 0.8));
      -- For simplicity, let's assume a fixed bonus for now if a threshold is met
      IF v_order.quality_bonus_threshold IS NOT NULL AND 
         v_counterparty_quality_score >= v_order.quality_bonus_threshold AND 
         v_order.quality_bonus_amount IS NOT NULL THEN
        IF v_order.side = 'bid' THEN -- Buyer pays more for higher quality
          v_adjusted_price := v_order.price + v_order.quality_bonus_amount / v_order.base_amount; 
        ELSIF v_order.side = 'ask' THEN -- Seller receives more (buyer pays more)
          -- This implies the *effective* price for the seller is higher.
          -- The matching engine needs to ensure the buyer is willing to pay this.
          -- For simplicity, let's assume the order's price is the base and bonus is added.
          NULL; -- Logic depends on how price adjustments are defined.
        END IF;
      END IF;
    END IF;
  END IF;

  -- Standard matching logic using v_adjusted_price
  -- ... (this would call the main matching function with the adjusted price)
  -- For now, just return the potential adjusted price and match status
  
  -- Check if the adjusted price is still valid for the counterparty
  IF v_order.side = 'bid' AND v_adjusted_price > v_counterparty_order.price THEN v_can_match := false; END IF;
  IF v_order.side = 'ask' AND v_adjusted_price < v_counterparty_order.price THEN v_can_match := false; END IF;

  RETURN jsonb_build_object(
    'can_match', v_can_match,
    'original_price', v_order.price,
    'counterparty_quality', v_counterparty_quality_score,
    'adjusted_price_for_order', v_adjusted_price,
    'reason', CASE WHEN NOT v_can_match THEN 'Price mismatch after quality adjustment' ELSE NULL END
  );
END;
$$ LANGUAGE plpgsql;
```

The matching logic also handles quality bonus payments, where participants can offer additional compensation for exceeding quality expectations. If a counterparty delivers quality that surpasses the specified bonus threshold, the system automatically calculates and distributes the quality bonus payment, further incentivizing exceptional performance.

### Integration with Quality Score System

Quality-Contingent Orders rely on accurate and up-to-date quality scores for all market participants and assets. The system integrates seamlessly with the platform's Quality Score System, which continuously evaluates quality based on multiple data sources including historical performance, peer reviews, client feedback, and objective metrics.

When a Quality-Contingent Order is processed, the matching engine queries the Quality Score System in real-time to retrieve the relevant quality scores. This ensures that order execution is based on the most current quality information available, providing accurate and fair valuation of quality contributions.

The integration also includes feedback mechanisms where the outcomes of Quality-Contingent Orders contribute to future quality score calculations. Successful execution of orders with high quality requirements can enhance a participant's quality score, while failure to meet quality expectations can negatively impact scores. This creates a virtuous cycle where quality-driven trading reinforces quality improvement across the ecosystem.

## Time-Decaying Orders

### Conceptual Overview

Time-Decaying Orders address the unique challenge of trading Temporal currency, whose value continuously decreases over time according to an exponential decay function. These orders automatically adjust their terms based on the passage of time, ensuring that prices accurately reflect the current time-adjusted value of Temporal currency.

Traditional order types with fixed prices are unsuitable for Temporal currency because their value becomes increasingly inaccurate as time passes and decay erodes the currency's worth. Time-Decaying Orders solve this problem by incorporating decay calculations directly into the order structure, allowing participants to express preferences that account for the time-sensitive nature of Temporal currency.

The design of Time-Decaying Orders must handle several complexities. First, it requires continuous repricing of active orders based on decay calculations. Second, it needs sophisticated matching logic that can compare orders with dynamically changing prices. Third, it must integrate seamlessly with the Temporal Decay Markets to ensure consistent valuation of time effects across all transaction types.

### Order Structure and Decay Parameters

Time-Decaying Orders extend the standard order structure with additional fields that specify decay-related parameters and adjustment rules. These include initial price, decay rate, minimum acceptable price, and repricing frequency.

Participants can specify orders whose price automatically decreases over time according to a defined decay rate, reflecting the diminishing value of Temporal currency. They can also specify a minimum acceptable price below which the order should be cancelled, protecting them from excessive value erosion.

```sql
-- Extension to the order_book table for time-decaying specifics
ALTER TABLE order_book
  ADD COLUMN IF NOT EXISTS is_time_decaying BOOLEAN DEFAULT false,
  ADD COLUMN IF NOT EXISTS initial_price DECIMAL(18, 8), -- Price at time of order creation
  ADD COLUMN IF NOT EXISTS decay_rate_per_hour DECIMAL(10, 8), -- e.g., 0.02 for 2% per hour
  ADD COLUMN IF NOT EXISTS min_acceptable_price DECIMAL(18, 8),
  ADD COLUMN IF NOT EXISTS last_decay_update_ts TIMESTAMPTZ;

-- When creating a time-decaying order, initial_price would be set to the current price.
-- The `price` column in order_book would be dynamically updated by a background process.
```

The `decay_rate_per_hour` determines how quickly the order's price adjusts. The `min_acceptable_price` acts as a floor. The `last_decay_update_ts` helps in calculating the current decayed price.

### Continuous Repricing Engine for Orders

The Continuous Repricing Engine for Time-Decaying Orders automatically adjusts the prices of active orders based on their specified decay rates and the passage of time. This engine operates continuously, updating order prices every second (or other configured interval) to ensure that they accurately reflect the current time-adjusted value of Temporal currency.

The repricing logic uses the temporal decay formula V(t) = V₀ × exp(-decay_rate × t) to calculate the current price of each Time-Decaying Order. V₀ represents the initial price at the time of order creation, decay_rate is the specified decay rate for the order, and t is the elapsed time since creation or last repricing.

```sql
CREATE OR REPLACE FUNCTION update_time_decaying_order_prices() RETURNS VOID AS $$
DECLARE
  v_order RECORD;
  v_elapsed_hours NUMERIC;
  v_current_decayed_price DECIMAL(18, 8);
BEGIN
  FOR v_order IN 
    SELECT id, initial_price, decay_rate_per_hour, min_acceptable_price, created_at, last_decay_update_ts 
    FROM order_book 
    WHERE is_time_decaying = true 
      AND status = 'open' 
      AND (base_currency = '⧗' OR quote_currency = '⧗') -- Only for Temporal currency pairs
  LOOP
    -- Calculate elapsed time in hours since order creation or last update for decay calculation
    v_elapsed_hours := EXTRACT(EPOCH FROM (now() - COALESCE(v_order.last_decay_update_ts, v_order.created_at))) / 3600.0;
    
    IF v_elapsed_hours <= 0 THEN CONTINUE; END IF; -- No update needed if no time passed or timestamp is in future

    -- Calculate current price based on decay from initial_price or last known price
    -- For simplicity, let's assume decay applies to the initial_price over total elapsed time from creation
    v_elapsed_hours_total := EXTRACT(EPOCH FROM (now() - v_order.created_at)) / 3600.0;
    v_current_decayed_price := v_order.initial_price * exp(-v_order.decay_rate_per_hour * v_elapsed_hours_total);

    -- Check against minimum acceptable price
    IF v_order.min_acceptable_price IS NOT NULL AND v_current_decayed_price < v_order.min_acceptable_price THEN
      -- Cancel order if price falls below minimum
      UPDATE order_book SET status = 'cancelled', updated_at = now(), reason = 'Price decayed below minimum acceptable' 
      WHERE id = v_order.id;
      CONTINUE;
    END IF;

    -- Update order price if it has changed significantly
    IF abs(v_current_decayed_price - (SELECT price FROM order_book WHERE id = v_order.id)) > 0.000001 THEN -- Threshold for update
      UPDATE order_book 
      SET price = v_current_decayed_price, 
          last_decay_update_ts = now(),
          updated_at = now()
      WHERE id = v_order.id;
      
      -- Potentially broadcast price update event
      -- PERFORM broadcast_order_price_update(v_order.id, v_current_decayed_price);
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- This function would need to be called periodically by a scheduler (e.g., pg_cron)
```

The repricing engine implements optimization strategies to minimize computational overhead. Orders with slow decay rates or low values are updated less frequently. Batch processing groups related updates together to reduce database transaction overhead.

### Matching Time-Varying Prices

The matching engine for Time-Decaying Orders must handle the complexity of comparing orders whose prices are continuously changing. Traditional matching algorithms that rely on static prices are unsuitable for this environment.

The matching logic uses the current time-adjusted prices of Time-Decaying Orders when evaluating potential matches. When a Time-Decaying Order encounters a potential counterparty, the system calculates its current decayed price and uses this value for comparison. This ensures that matches are based on the most accurate and up-to-date pricing information.

The matching engine also considers the decay characteristics of both orders when determining match priority. Orders with faster decay rates may be prioritized to minimize value erosion. Orders approaching their minimum acceptable price may also be prioritized to ensure execution before cancellation.

## Atomic Swaps and Smart Routing Considerations

While the primary focus is on the three order types above, the broader context of multi-dimensional trading includes atomic swaps and enhanced smart order routing.

### Atomic Swaps

Atomic swaps are a specific type of bundle order where two parties directly exchange different currencies across different pairs without needing an intermediary or a common currency. The Bundle Order mechanism described above provides the foundation for atomic swaps. An atomic swap can be structured as a two-leg bundle where each leg represents one side of the swap, and atomicity ensures both exchanges happen or neither does.

For example, Agent A wants to swap 100 ₥ for 10 ◊, and Agent B wants to swap 10 ◊ for 100 ₥. This can be represented as a bundle: Leg 1: Agent A sells 100 ₥ / buys 10 ◊. Leg 2: Agent B sells 10 ◊ / buys 100 ₥. The system would need to match these two specific intents together.

### Enhanced Smart Order Routing

The smart order routing system, designed in the previous phase, can be enhanced by these multi-dimensional order types. For instance, if a user wants to acquire a bundle of resources, the smart router could attempt to fill this as a Bundle Order first. If not possible, it could break it down and route individual legs, considering the risks of partial fills if atomicity is not strictly required by the user.

Quality-Contingent and Time-Decaying parameters can also inform routing decisions. The router might prioritize counterparties with higher quality scores if a quality-contingent preference is expressed, or it might choose execution paths that minimize exposure to temporal decay for time-sensitive assets.

## Conclusion

Multi-Dimensional Order Types represent a cornerstone of the advanced trading capabilities within the multi-currency AI economy. Bundle Orders provide the atomicity required for complex multi-leg strategies, Quality-Contingent Orders enable sophisticated quality-based pricing and optimization, and Time-Decaying Orders accurately handle the unique value dynamics of Temporal currency.

These order types, integrated with the core order book architecture, special market mechanisms, and liquidity systems, empower participants to engage in nuanced trading strategies that capture value from the unique properties of each currency and the complex interactions between them. The robust design ensures reliable execution, data consistency, and high performance, paving the way for a truly intelligent and efficient economic ecosystem.

