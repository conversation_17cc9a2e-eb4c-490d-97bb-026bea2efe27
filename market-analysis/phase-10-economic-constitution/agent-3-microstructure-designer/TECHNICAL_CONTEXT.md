# Technical Context for Microstructure Designer

## Current VibeLaunch Market Microstructure

VibeLaunch currently operates as a simple sealed-bid auction with manual winner selection. Your challenge is to design sophisticated market microstructure within PostgreSQL and real-time event constraints.

## Existing Order Flow Architecture

### Current Bid Submission Flow

```sql
-- 1. Contract Published
UPDATE contracts SET status = 'published' WHERE id = ?;

-- 2. Agents Submit Sealed Bids
INSERT INTO bids (contract_id, agent_role, price, confidence, expected_kpi)
VALUES (?, ?, ?, ?, ?);

-- 3. Human Selects Winner (Manual!)
UPDATE bids SET status = 'accepted' WHERE id = ?;
UPDATE bids SET status = 'rejected' WHERE contract_id = ? AND id != ?;

-- No continuous market, no order book, no automatic matching
```

### Event System Infrastructure

```sql
-- Bus Events table (your real-time backbone)
bus_events (
  id UUID PRIMARY KEY,
  organisation_id UUID,
  event_type TEXT,           -- Your design space
  payload JSONB,             -- Flexible event data
  channel TEXT,              -- 'pipeline', 'sequential', 'mcp', 'agent_events'
  processed BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT now()
)

-- PostgreSQL NOTIFY/LISTEN
NOTIFY agent_events, '{"type": "new_order", "data": {...}}';
LISTEN agent_events; -- Agents subscribe to channels
```

### Real-Time Capabilities

```javascript
// Supabase real-time subscriptions
const subscription = supabase
  .channel('market_events')
  .on('postgres_changes', 
    { 
      event: 'INSERT', 
      schema: 'public', 
      table: 'bus_events',
      filter: 'channel=eq.market' 
    },
    handleMarketEvent
  )
  .subscribe();
```

## Market Microstructure Limitations

### 1. No Order Book
- No bid/ask spread tracking
- No market depth visibility  
- No limit orders
- No price-time priority

### 2. No Continuous Trading
- One-shot sealed bids only
- No ability to update bids
- No cancellations
- No partial fills

### 3. No Market Making
- No liquidity providers
- No spread capture
- No inventory management
- No automated pricing

### 4. No Price Discovery
- Fixed budget contracts
- No dynamic pricing
- No market feedback
- No price signals

## Technical Building Blocks for Advanced Microstructure

### 1. Order Book Implementation

```sql
-- Create limit order book structure
CREATE TABLE order_book (
  id UUID PRIMARY KEY,
  contract_id UUID REFERENCES contracts(id),
  side TEXT CHECK (side IN ('bid', 'ask')),
  agent_role TEXT NOT NULL,
  price DECIMAL(10, 2) NOT NULL,
  quantity INTEGER DEFAULT 1,
  time_in_force TEXT DEFAULT 'GTC', -- 'GTC', 'IOC', 'FOK'
  status TEXT DEFAULT 'open',       -- 'open', 'filled', 'cancelled'
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes for order matching performance
CREATE INDEX idx_order_book_matching ON order_book 
  (contract_id, side, price, created_at) 
  WHERE status = 'open';

-- Best bid/ask view
CREATE VIEW market_quotes AS
SELECT 
  contract_id,
  MAX(CASE WHEN side = 'bid' THEN price END) as best_bid,
  MIN(CASE WHEN side = 'ask' THEN price END) as best_ask,
  MIN(CASE WHEN side = 'ask' THEN price END) - 
  MAX(CASE WHEN side = 'bid' THEN price END) as spread
FROM order_book
WHERE status = 'open'
GROUP BY contract_id;
```

### 2. Matching Engine (SQL-Based)

```sql
-- Order matching function
CREATE OR REPLACE FUNCTION match_orders(
  p_contract_id UUID
) RETURNS TABLE (
  bid_id UUID,
  ask_id UUID,
  match_price DECIMAL,
  match_quantity INTEGER
) AS $$
BEGIN
  RETURN QUERY
  WITH ranked_bids AS (
    SELECT *, ROW_NUMBER() OVER (ORDER BY price DESC, created_at) as rn
    FROM order_book 
    WHERE contract_id = p_contract_id 
      AND side = 'bid' 
      AND status = 'open'
  ),
  ranked_asks AS (
    SELECT *, ROW_NUMBER() OVER (ORDER BY price ASC, created_at) as rn
    FROM order_book 
    WHERE contract_id = p_contract_id 
      AND side = 'ask' 
      AND status = 'open'
  )
  SELECT 
    b.id as bid_id,
    a.id as ask_id,
    CASE 
      WHEN b.created_at < a.created_at THEN b.price
      ELSE a.price
    END as match_price,
    LEAST(b.quantity, a.quantity) as match_quantity
  FROM ranked_bids b
  JOIN ranked_asks a ON b.rn = a.rn
  WHERE b.price >= a.price;
END;
$$ LANGUAGE plpgsql;

-- Trigger for automatic matching
CREATE TRIGGER auto_match_orders
AFTER INSERT OR UPDATE ON order_book
FOR EACH ROW
WHEN (NEW.status = 'open')
EXECUTE FUNCTION try_match_order();
```

### 3. Market Data Distribution

```sql
-- Real-time market data events
CREATE OR REPLACE FUNCTION broadcast_market_data() 
RETURNS TRIGGER AS $$
DECLARE
  market_snapshot JSONB;
BEGIN
  -- Build market data snapshot
  SELECT json_build_object(
    'contract_id', NEW.contract_id,
    'event_type', TG_OP,
    'order', row_to_json(NEW),
    'market_stats', (
      SELECT json_build_object(
        'best_bid', best_bid,
        'best_ask', best_ask,
        'spread', spread,
        'depth', (
          SELECT COUNT(*) FROM order_book 
          WHERE contract_id = NEW.contract_id 
            AND status = 'open'
        )
      )
      FROM market_quotes 
      WHERE contract_id = NEW.contract_id
    ),
    'timestamp', now()
  ) INTO market_snapshot;
  
  -- Broadcast to market participants
  INSERT INTO bus_events (
    organisation_id,
    event_type,
    payload,
    channel
  ) VALUES (
    NEW.organisation_id,
    'market_data',
    market_snapshot,
    'market'
  );
  
  -- Also use NOTIFY for real-time push
  PERFORM pg_notify('market_data', market_snapshot::text);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### 4. Circuit Breakers & Risk Controls

```sql
-- Market circuit breakers
CREATE TABLE market_controls (
  contract_id UUID REFERENCES contracts(id),
  max_price_move_percent DECIMAL(5, 2) DEFAULT 10.0,
  max_orders_per_minute INTEGER DEFAULT 100,
  trading_halted BOOLEAN DEFAULT false,
  halt_reason TEXT,
  PRIMARY KEY (contract_id)
);

-- Price movement circuit breaker
CREATE OR REPLACE FUNCTION check_price_limits(
  p_order_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
  v_order order_book;
  v_last_price DECIMAL;
  v_price_change_pct DECIMAL;
  v_max_move DECIMAL;
BEGIN
  SELECT * INTO v_order FROM order_book WHERE id = p_order_id;
  
  -- Get last traded price
  SELECT match_price INTO v_last_price
  FROM trade_history
  WHERE contract_id = v_order.contract_id
  ORDER BY created_at DESC
  LIMIT 1;
  
  IF v_last_price IS NOT NULL THEN
    v_price_change_pct := ABS(
      (v_order.price - v_last_price) / v_last_price * 100
    );
    
    SELECT max_price_move_percent INTO v_max_move
    FROM market_controls
    WHERE contract_id = v_order.contract_id;
    
    IF v_price_change_pct > v_max_move THEN
      -- Halt trading
      UPDATE market_controls
      SET trading_halted = true,
          halt_reason = format('Price move %.2f%% exceeds limit', 
                              v_price_change_pct)
      WHERE contract_id = v_order.contract_id;
      
      RETURN false;
    END IF;
  END IF;
  
  RETURN true;
END;
$$ LANGUAGE plpgsql;
```

### 5. Market Making Infrastructure

```sql
-- Market maker inventory tracking
CREATE TABLE market_maker_positions (
  agent_role TEXT,
  contract_id UUID REFERENCES contracts(id),
  position INTEGER DEFAULT 0,        -- Current inventory
  total_volume INTEGER DEFAULT 0,    -- Total traded
  pnl DECIMAL(10, 2) DEFAULT 0,     -- Profit/loss
  last_update TIMESTAMPTZ DEFAULT now(),
  PRIMARY KEY (agent_role, contract_id)
);

-- Automated market making function
CREATE OR REPLACE FUNCTION generate_market_maker_quotes(
  p_agent_role TEXT,
  p_contract_id UUID
) RETURNS TABLE (
  side TEXT,
  price DECIMAL,
  quantity INTEGER
) AS $$
DECLARE
  v_mid_price DECIMAL;
  v_spread DECIMAL;
  v_position INTEGER;
  v_skew DECIMAL;
BEGIN
  -- Get current mid price
  SELECT (best_bid + best_ask) / 2 INTO v_mid_price
  FROM market_quotes
  WHERE contract_id = p_contract_id;
  
  -- Get current position
  SELECT position INTO v_position
  FROM market_maker_positions
  WHERE agent_role = p_agent_role
    AND contract_id = p_contract_id;
  
  -- Calculate spread based on volatility
  v_spread := calculate_optimal_spread(p_contract_id);
  
  -- Position-based skew
  v_skew := v_position * 0.001; -- 0.1% per unit
  
  -- Generate quotes
  RETURN QUERY
  SELECT 'bid'::TEXT, 
         v_mid_price - (v_spread/2) - v_skew,
         1
  UNION ALL
  SELECT 'ask'::TEXT,
         v_mid_price + (v_spread/2) - v_skew,
         1;
END;
$$ LANGUAGE plpgsql;
```

## Performance Considerations

### 1. PostgreSQL NOTIFY Limitations
- Single-threaded delivery
- No guaranteed ordering
- May drop messages under load
- ~1000 messages/second practical limit

### 2. Lock Contention
```sql
-- Optimistic locking for order updates
UPDATE order_book 
SET status = 'filled',
    updated_at = now()
WHERE id = ? 
  AND status = 'open'
  AND updated_at = ?; -- Version check

-- Use advisory locks for critical sections
SELECT pg_advisory_lock(contract_id::BIGINT);
-- ... critical operations ...
SELECT pg_advisory_unlock(contract_id::BIGINT);
```

### 3. Index Strategy
```sql
-- Essential indexes for performance
CREATE INDEX idx_orders_active ON order_book 
  (contract_id, side, status) 
  WHERE status = 'open';

CREATE INDEX idx_market_events_recent ON bus_events 
  (channel, created_at DESC) 
  WHERE created_at > now() - interval '1 hour';

CREATE INDEX idx_trades_history ON trade_history 
  (contract_id, created_at DESC);
```

## Implementation Roadmap

### Phase 1: Basic Order Book
- Add order_book table
- Simple limit orders
- Manual matching
- Basic market data

### Phase 2: Automated Matching
- Matching engine function
- Automatic execution
- Trade history
- Position tracking

### Phase 3: Market Making
- MM registration system
- Quote generation
- Inventory management
- P&L tracking

### Phase 4: Advanced Features
- Complex order types
- Iceberg orders
- Stop losses
- Options/derivatives

## Example: Implementing a Simple Continuous Double Auction

```sql
-- Migration: 20250114_microstructure_continuous_auction.sql

-- Enable continuous double auction for contracts
ALTER TABLE contracts ADD COLUMN 
  market_type TEXT DEFAULT 'sealed_bid' 
  CHECK (market_type IN ('sealed_bid', 'continuous_auction', 'hybrid'));

-- Real-time order matching
CREATE OR REPLACE FUNCTION continuous_match_orders()
RETURNS TRIGGER AS $$
DECLARE
  v_match RECORD;
BEGIN
  -- Only for continuous auction contracts
  IF EXISTS (
    SELECT 1 FROM contracts 
    WHERE id = NEW.contract_id 
      AND market_type = 'continuous_auction'
  ) THEN
    -- Try to match immediately
    FOR v_match IN 
      SELECT * FROM match_orders(NEW.contract_id)
    LOOP
      -- Execute trade
      PERFORM execute_trade(
        v_match.bid_id, 
        v_match.ask_id, 
        v_match.match_price,
        v_match.match_quantity
      );
    END LOOP;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_continuous_matching
AFTER INSERT ON order_book
FOR EACH ROW
EXECUTE FUNCTION continuous_match_orders();
```

## Critical Success Factors

1. **Start Simple**: Basic order book before complex features
2. **Ensure Atomicity**: All trades must be atomic
3. **Monitor Performance**: Watch for lock contention
4. **Test Thoroughly**: Edge cases in matching logic
5. **Gradual Rollout**: One contract type at a time
6. **Fallback Mode**: Can revert to sealed bids
7. **Clear Documentation**: How matching works
8. **Audit Trail**: Complete trade history
9. **Real-time Focus**: Optimize for low latency
10. **Scale Awareness**: Design for 1000+ orders/second

Your microstructure must balance sophistication with PostgreSQL's constraints. Remember: AI agents can react in microseconds, so your design must handle high-frequency interaction patterns while maintaining fairness and stability.