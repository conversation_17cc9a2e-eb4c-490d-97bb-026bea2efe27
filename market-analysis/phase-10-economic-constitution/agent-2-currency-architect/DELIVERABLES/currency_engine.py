"""
VibeLaunch Multi-Dimensional Currency System
Core Implementation Module

This module provides the core functionality for the multi-dimensional currency system,
including currency operations, exchange mechanisms, and balance management.
"""

import asyncio
import asyncpg
import json
import time
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CurrencyType(Enum):
    ECONOMIC = "economic"
    QUALITY = "quality"
    TEMPORAL = "temporal"
    RELIABILITY = "reliability"
    INNOVATION = "innovation"

class TransactionType(Enum):
    MINT = "mint"
    BURN = "burn"
    TRANSFER = "transfer"
    EXCHANGE = "exchange"
    INTEREST = "interest"
    DECAY = "decay"
    APPRECIATION = "appreciation"

@dataclass
class Balance:
    agent_id: str
    currency_type: CurrencyType
    balance: Decimal
    locked_balance: Decimal
    last_updated: datetime

@dataclass
class Transaction:
    transaction_id: str
    from_agent_id: Optional[str]
    to_agent_id: Optional[str]
    currency_type: CurrencyType
    transaction_type: TransactionType
    amount: Decimal
    fee: Decimal
    status: str
    created_at: datetime
    completed_at: Optional[datetime]

@dataclass
class ExchangeQuote:
    from_currency: CurrencyType
    to_currency: CurrencyType
    amount_in: Decimal
    amount_out: Decimal
    exchange_rate: Decimal
    fee: Decimal
    price_impact: Decimal
    valid_until: datetime

class CurrencyEngine:
    """
    Core currency engine that handles all currency operations
    """
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.connection_pool = None
        
        # Performance tracking
        self.performance_metrics = {
            'total_transactions': 0,
            'avg_processing_time': 0.0,
            'max_processing_time': 0.0,
            'failed_transactions': 0
        }
    
    async def initialize(self):
        """Initialize database connection pool"""
        self.connection_pool = await asyncpg.create_pool(
            self.database_url,
            min_size=5,
            max_size=20,
            command_timeout=60
        )
        logger.info("Currency engine initialized with database connection pool")
    
    async def close(self):
        """Close database connections"""
        if self.connection_pool:
            await self.connection_pool.close()
    
    async def _execute_with_timing(self, operation_name: str, coro):
        """Execute operation with performance timing"""
        start_time = time.perf_counter()
        try:
            result = await coro
            end_time = time.perf_counter()
            processing_time = end_time - start_time
            
            # Update performance metrics
            self.performance_metrics['total_transactions'] += 1
            self.performance_metrics['avg_processing_time'] = (
                (self.performance_metrics['avg_processing_time'] * (self.performance_metrics['total_transactions'] - 1) + processing_time) /
                self.performance_metrics['total_transactions']
            )
            self.performance_metrics['max_processing_time'] = max(
                self.performance_metrics['max_processing_time'], processing_time
            )
            
            # Log performance warning if over 100ms
            if processing_time > 0.1:
                logger.warning(f"{operation_name} took {processing_time*1000:.2f}ms (exceeds 100ms target)")
            else:
                logger.info(f"{operation_name} completed in {processing_time*1000:.2f}ms")
            
            return result
            
        except Exception as e:
            end_time = time.perf_counter()
            processing_time = end_time - start_time
            self.performance_metrics['failed_transactions'] += 1
            logger.error(f"{operation_name} failed after {processing_time*1000:.2f}ms: {str(e)}")
            raise
    
    async def get_balance(self, agent_id: str, currency_type: CurrencyType) -> Decimal:
        """Get current balance for agent and currency type"""
        async def _get_balance():
            async with self.connection_pool.acquire() as conn:
                # Get agent UUID
                agent_uuid = await conn.fetchval(
                    "SELECT id FROM agents WHERE agent_id = $1", agent_id
                )
                if not agent_uuid:
                    raise ValueError(f"Agent {agent_id} not found")
                
                # Get balance using the database function
                balance = await conn.fetchval(
                    "SELECT get_currency_balance($1, $2)", 
                    agent_uuid, currency_type.value
                )
                return Decimal(str(balance)) if balance else Decimal('0')
        
        return await self._execute_with_timing(f"get_balance({agent_id}, {currency_type.value})", _get_balance())
    
    async def transfer_currency(
        self, 
        from_agent_id: str, 
        to_agent_id: str, 
        currency_type: CurrencyType, 
        amount: Decimal,
        transaction_id: Optional[str] = None
    ) -> str:
        """Transfer currency between agents"""
        async def _transfer():
            async with self.connection_pool.acquire() as conn:
                # Get agent UUIDs
                from_uuid = await conn.fetchval(
                    "SELECT id FROM agents WHERE agent_id = $1", from_agent_id
                )
                to_uuid = await conn.fetchval(
                    "SELECT id FROM agents WHERE agent_id = $1", to_agent_id
                )
                
                if not from_uuid:
                    raise ValueError(f"From agent {from_agent_id} not found")
                if not to_uuid:
                    raise ValueError(f"To agent {to_agent_id} not found")
                
                # Execute transfer using database function
                result_uuid = await conn.fetchval(
                    "SELECT transfer_currency($1, $2, $3, $4, $5)",
                    from_uuid, to_uuid, currency_type.value, amount, transaction_id
                )
                
                return str(result_uuid)
        
        return await self._execute_with_timing(
            f"transfer({from_agent_id}->{to_agent_id}, {amount} {currency_type.value})", 
            _transfer()
        )
    
    async def mint_currency(
        self, 
        agent_id: str, 
        currency_type: CurrencyType, 
        amount: Decimal,
        reason: str = "manual_mint"
    ) -> str:
        """Mint new currency for agent"""
        async def _mint():
            async with self.connection_pool.acquire() as conn:
                # Get agent UUID
                agent_uuid = await conn.fetchval(
                    "SELECT id FROM agents WHERE agent_id = $1", agent_id
                )
                if not agent_uuid:
                    raise ValueError(f"Agent {agent_id} not found")
                
                # Execute mint using database function
                result_uuid = await conn.fetchval(
                    "SELECT mint_currency($1, $2, $3, $4)",
                    agent_uuid, currency_type.value, amount, reason
                )
                
                return str(result_uuid)
        
        return await self._execute_with_timing(
            f"mint({agent_id}, {amount} {currency_type.value})", 
            _mint()
        )
    
    async def get_exchange_quote(
        self, 
        from_currency: CurrencyType, 
        to_currency: CurrencyType, 
        amount: Decimal
    ) -> ExchangeQuote:
        """Get exchange quote for currency pair"""
        async def _get_quote():
            async with self.connection_pool.acquire() as conn:
                # Use database function to calculate exchange
                result = await conn.fetchrow(
                    "SELECT * FROM calculate_exchange_rate($1, $2, $3)",
                    from_currency.value, to_currency.value, amount
                )
                
                return ExchangeQuote(
                    from_currency=from_currency,
                    to_currency=to_currency,
                    amount_in=amount,
                    amount_out=Decimal(str(result['amount_out'])),
                    exchange_rate=Decimal(str(result['rate'])),
                    fee=Decimal(str(result['fee'])),
                    price_impact=Decimal(str(result['price_impact'])),
                    valid_until=datetime.now() + timedelta(minutes=5)
                )
        
        return await self._execute_with_timing(
            f"quote({from_currency.value}->{to_currency.value}, {amount})", 
            _get_quote()
        )
    
    async def execute_exchange(
        self, 
        agent_id: str,
        from_currency: CurrencyType, 
        to_currency: CurrencyType, 
        amount: Decimal,
        max_slippage: Decimal = Decimal('0.05')  # 5% max slippage
    ) -> Tuple[str, Decimal]:
        """Execute currency exchange"""
        async def _exchange():
            async with self.connection_pool.acquire() as conn:
                async with conn.transaction():
                    # Get agent UUID
                    agent_uuid = await conn.fetchval(
                        "SELECT id FROM agents WHERE agent_id = $1", agent_id
                    )
                    if not agent_uuid:
                        raise ValueError(f"Agent {agent_id} not found")
                    
                    # Get current quote
                    quote_result = await conn.fetchrow(
                        "SELECT * FROM calculate_exchange_rate($1, $2, $3)",
                        from_currency.value, to_currency.value, amount
                    )
                    
                    exchange_rate = Decimal(str(quote_result['rate']))
                    amount_out = Decimal(str(quote_result['amount_out']))
                    fee = Decimal(str(quote_result['fee']))
                    price_impact = Decimal(str(quote_result['price_impact']))
                    
                    # Check slippage
                    if price_impact > max_slippage:
                        raise ValueError(f"Price impact {price_impact} exceeds max slippage {max_slippage}")
                    
                    # Execute the exchange by updating balances
                    # Deduct from source currency
                    await conn.fetchval(
                        "SELECT update_currency_balance($1, $2, $3, $4)",
                        agent_uuid, from_currency.value, -amount, f"exchange_{int(time.time())}"
                    )
                    
                    # Add to destination currency (amount after fee)
                    await conn.fetchval(
                        "SELECT update_currency_balance($1, $2, $3, $4)",
                        agent_uuid, to_currency.value, amount_out, f"exchange_{int(time.time())}"
                    )
                    
                    # Record exchange transaction
                    transaction_id = f"exchange_{agent_id}_{int(time.time())}"
                    result_uuid = await conn.fetchval("""
                        INSERT INTO transactions (
                            transaction_id, from_agent_id, to_agent_id, currency_type,
                            transaction_type, amount, fee, exchange_rate, to_currency_type,
                            to_amount, status, completed_at
                        ) VALUES ($1, $2, $2, $3, 'exchange', $4, $5, $6, $7, $8, 'completed', NOW())
                        RETURNING id
                    """, transaction_id, agent_uuid, from_currency.value, amount, fee, 
                         exchange_rate, to_currency.value, amount_out)
                    
                    return str(result_uuid), amount_out
        
        return await self._execute_with_timing(
            f"exchange({agent_id}, {amount} {from_currency.value}->{to_currency.value})", 
            _exchange()
        )
    
    async def get_agent_balances(self, agent_id: str) -> Dict[CurrencyType, Decimal]:
        """Get all currency balances for an agent"""
        async def _get_all_balances():
            balances = {}
            for currency_type in CurrencyType:
                balance = await self.get_balance(agent_id, currency_type)
                balances[currency_type] = balance
            return balances
        
        return await self._execute_with_timing(
            f"get_all_balances({agent_id})", 
            _get_all_balances()
        )
    
    async def get_transaction_history(
        self, 
        agent_id: str, 
        limit: int = 100,
        currency_type: Optional[CurrencyType] = None
    ) -> List[Dict]:
        """Get transaction history for agent"""
        async def _get_history():
            async with self.connection_pool.acquire() as conn:
                # Get agent UUID
                agent_uuid = await conn.fetchval(
                    "SELECT id FROM agents WHERE agent_id = $1", agent_id
                )
                if not agent_uuid:
                    raise ValueError(f"Agent {agent_id} not found")
                
                # Build query
                query = """
                    SELECT t.*, 
                           fa.agent_id as from_agent_name,
                           ta.agent_id as to_agent_name
                    FROM transactions t
                    LEFT JOIN agents fa ON t.from_agent_id = fa.id
                    LEFT JOIN agents ta ON t.to_agent_id = ta.id
                    WHERE (t.from_agent_id = $1 OR t.to_agent_id = $1)
                """
                params = [agent_uuid]
                
                if currency_type:
                    query += " AND t.currency_type = $2"
                    params.append(currency_type.value)
                
                query += " ORDER BY t.created_at DESC LIMIT $" + str(len(params) + 1)
                params.append(limit)
                
                rows = await conn.fetch(query, *params)
                
                return [dict(row) for row in rows]
        
        return await self._execute_with_timing(
            f"get_history({agent_id}, limit={limit})", 
            _get_history()
        )
    
    async def get_performance_metrics(self) -> Dict:
        """Get system performance metrics"""
        return {
            **self.performance_metrics,
            'target_processing_time_ms': 100,
            'current_avg_processing_time_ms': self.performance_metrics['avg_processing_time'] * 1000,
            'performance_target_met': self.performance_metrics['avg_processing_time'] < 0.1,
            'success_rate': (
                (self.performance_metrics['total_transactions'] - self.performance_metrics['failed_transactions']) /
                max(self.performance_metrics['total_transactions'], 1)
            ) * 100
        }
    
    async def create_agent(self, agent_id: str, name: str, initial_balances: Optional[Dict[CurrencyType, Decimal]] = None) -> str:
        """Create a new agent with optional initial balances"""
        async def _create_agent():
            async with self.connection_pool.acquire() as conn:
                async with conn.transaction():
                    # Create agent
                    agent_uuid = await conn.fetchval(
                        "INSERT INTO agents (agent_id, name) VALUES ($1, $2) RETURNING id",
                        agent_id, name
                    )
                    
                    # Create initial balances if provided
                    if initial_balances:
                        for currency_type, amount in initial_balances.items():
                            if amount > 0:
                                await conn.execute(
                                    "INSERT INTO currency_balances (agent_id, currency_type, balance) VALUES ($1, $2, $3)",
                                    agent_uuid, currency_type.value, amount
                                )
                    
                    return str(agent_uuid)
        
        return await self._execute_with_timing(
            f"create_agent({agent_id})", 
            _create_agent()
        )

class CurrencyAPI:
    """
    REST API interface for the currency system
    """
    
    def __init__(self, currency_engine: CurrencyEngine):
        self.engine = currency_engine
    
    async def handle_get_balance(self, agent_id: str, currency_type: str) -> Dict:
        """Handle GET /balance/{agent_id}/{currency_type}"""
        try:
            currency = CurrencyType(currency_type)
            balance = await self.engine.get_balance(agent_id, currency)
            
            return {
                "success": True,
                "agent_id": agent_id,
                "currency_type": currency_type,
                "balance": str(balance),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def handle_get_all_balances(self, agent_id: str) -> Dict:
        """Handle GET /balances/{agent_id}"""
        try:
            balances = await self.engine.get_agent_balances(agent_id)
            
            return {
                "success": True,
                "agent_id": agent_id,
                "balances": {k.value: str(v) for k, v in balances.items()},
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def handle_transfer(self, request_data: Dict) -> Dict:
        """Handle POST /transfer"""
        try:
            from_agent = request_data["from_agent_id"]
            to_agent = request_data["to_agent_id"]
            currency_type = CurrencyType(request_data["currency_type"])
            amount = Decimal(str(request_data["amount"]))
            transaction_id = request_data.get("transaction_id")
            
            result_id = await self.engine.transfer_currency(
                from_agent, to_agent, currency_type, amount, transaction_id
            )
            
            return {
                "success": True,
                "transaction_id": result_id,
                "from_agent_id": from_agent,
                "to_agent_id": to_agent,
                "currency_type": currency_type.value,
                "amount": str(amount),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def handle_exchange_quote(self, request_data: Dict) -> Dict:
        """Handle POST /exchange/quote"""
        try:
            from_currency = CurrencyType(request_data["from_currency"])
            to_currency = CurrencyType(request_data["to_currency"])
            amount = Decimal(str(request_data["amount"]))
            
            quote = await self.engine.get_exchange_quote(from_currency, to_currency, amount)
            
            return {
                "success": True,
                "from_currency": quote.from_currency.value,
                "to_currency": quote.to_currency.value,
                "amount_in": str(quote.amount_in),
                "amount_out": str(quote.amount_out),
                "exchange_rate": str(quote.exchange_rate),
                "fee": str(quote.fee),
                "price_impact": str(quote.price_impact),
                "valid_until": quote.valid_until.isoformat(),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def handle_exchange_execute(self, request_data: Dict) -> Dict:
        """Handle POST /exchange/execute"""
        try:
            agent_id = request_data["agent_id"]
            from_currency = CurrencyType(request_data["from_currency"])
            to_currency = CurrencyType(request_data["to_currency"])
            amount = Decimal(str(request_data["amount"]))
            max_slippage = Decimal(str(request_data.get("max_slippage", "0.05")))
            
            transaction_id, amount_out = await self.engine.execute_exchange(
                agent_id, from_currency, to_currency, amount, max_slippage
            )
            
            return {
                "success": True,
                "transaction_id": transaction_id,
                "agent_id": agent_id,
                "from_currency": from_currency.value,
                "to_currency": to_currency.value,
                "amount_in": str(amount),
                "amount_out": str(amount_out),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def handle_get_performance(self) -> Dict:
        """Handle GET /performance"""
        try:
            metrics = await self.engine.get_performance_metrics()
            return {
                "success": True,
                "metrics": metrics,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

# Example usage and testing functions
async def run_performance_test():
    """Run comprehensive performance test"""
    # This would be used to validate the <100ms requirement
    engine = CurrencyEngine("postgresql://user:pass@localhost/vibelaunch_currency")
    await engine.initialize()
    
    try:
        # Create test agents
        await engine.create_agent("test_001", "Performance Test Agent 1", {
            CurrencyType.ECONOMIC: Decimal('1000'),
            CurrencyType.QUALITY: Decimal('500'),
            CurrencyType.TEMPORAL: Decimal('200'),
            CurrencyType.RELIABILITY: Decimal('300'),
            CurrencyType.INNOVATION: Decimal('100')
        })
        
        await engine.create_agent("test_002", "Performance Test Agent 2", {
            CurrencyType.ECONOMIC: Decimal('1000'),
            CurrencyType.QUALITY: Decimal('500'),
            CurrencyType.TEMPORAL: Decimal('200'),
            CurrencyType.RELIABILITY: Decimal('300'),
            CurrencyType.INNOVATION: Decimal('100')
        })
        
        # Run performance tests
        print("Running performance tests...")
        
        # Test 1: Balance queries
        start_time = time.perf_counter()
        for i in range(100):
            await engine.get_balance("test_001", CurrencyType.ECONOMIC)
        end_time = time.perf_counter()
        avg_balance_time = (end_time - start_time) / 100
        print(f"Average balance query time: {avg_balance_time*1000:.2f}ms")
        
        # Test 2: Transfers
        start_time = time.perf_counter()
        for i in range(50):
            await engine.transfer_currency(
                "test_001", "test_002", CurrencyType.ECONOMIC, Decimal('10')
            )
        end_time = time.perf_counter()
        avg_transfer_time = (end_time - start_time) / 50
        print(f"Average transfer time: {avg_transfer_time*1000:.2f}ms")
        
        # Test 3: Exchanges
        start_time = time.perf_counter()
        for i in range(25):
            await engine.execute_exchange(
                "test_001", CurrencyType.ECONOMIC, CurrencyType.QUALITY, Decimal('10')
            )
        end_time = time.perf_counter()
        avg_exchange_time = (end_time - start_time) / 25
        print(f"Average exchange time: {avg_exchange_time*1000:.2f}ms")
        
        # Get final performance metrics
        metrics = await engine.get_performance_metrics()
        print(f"Overall performance metrics: {metrics}")
        
        # Validate performance targets
        if metrics['performance_target_met']:
            print("✅ Performance target (<100ms) MET")
        else:
            print("❌ Performance target (<100ms) NOT MET")
            
    finally:
        await engine.close()

if __name__ == "__main__":
    # Run the performance test
    asyncio.run(run_performance_test())

