-- VibeLaunch Multi-Dimensional Currency System
-- PostgreSQL Database Schema Implementation

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Currency types enumeration
CREATE TYPE currency_type AS ENUM (
    'economic',
    'quality', 
    'temporal',
    'reliability',
    'innovation'
);

-- Transaction types enumeration
CREATE TYPE transaction_type AS ENUM (
    'mint',
    'burn',
    'transfer',
    'exchange',
    'interest',
    'decay',
    'appreciation'
);

-- Transaction status enumeration
CREATE TYPE transaction_status AS ENUM (
    'pending',
    'completed',
    'failed',
    'cancelled'
);

-- Agents table
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    reputation_score DECIMAL(10,8) DEFAULT 0.0,
    quality_history JSONB DEFAULT '[]'::jsonb,
    performance_metrics JSONB DEFAULT '{}'::jsonb
);

-- Currency balances table
CREATE TABLE currency_balances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    currency_type currency_type NOT NULL,
    balance DECIMAL(28,18) NOT NULL DEFAULT 0.0,
    locked_balance DECIMAL(28,18) NOT NULL DEFAULT 0.0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_interest_calculation TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_decay_calculation TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(agent_id, currency_type),
    CONSTRAINT positive_balance CHECK (balance >= 0),
    CONSTRAINT positive_locked_balance CHECK (locked_balance >= 0)
);

-- Transactions table
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    from_agent_id UUID REFERENCES agents(id),
    to_agent_id UUID REFERENCES agents(id),
    currency_type currency_type NOT NULL,
    transaction_type transaction_type NOT NULL,
    amount DECIMAL(28,18) NOT NULL,
    fee DECIMAL(28,18) DEFAULT 0.0,
    exchange_rate DECIMAL(28,18),
    to_currency_type currency_type,
    to_amount DECIMAL(28,18),
    status transaction_status DEFAULT 'pending',
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT positive_amount CHECK (amount > 0),
    CONSTRAINT positive_fee CHECK (fee >= 0),
    CONSTRAINT exchange_consistency CHECK (
        (transaction_type = 'exchange' AND to_currency_type IS NOT NULL AND to_amount IS NOT NULL AND exchange_rate IS NOT NULL) OR
        (transaction_type != 'exchange' AND to_currency_type IS NULL AND to_amount IS NULL)
    )
);

-- Exchange rates table
CREATE TABLE exchange_rates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_currency currency_type NOT NULL,
    to_currency currency_type NOT NULL,
    rate DECIMAL(28,18) NOT NULL,
    bid_rate DECIMAL(28,18) NOT NULL,
    ask_rate DECIMAL(28,18) NOT NULL,
    volume_24h DECIMAL(28,18) DEFAULT 0.0,
    last_trade_price DECIMAL(28,18),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(from_currency, to_currency),
    CONSTRAINT different_currencies CHECK (from_currency != to_currency),
    CONSTRAINT positive_rate CHECK (rate > 0),
    CONSTRAINT rate_bounds CHECK (rate >= 0.1 AND rate <= 10.0),
    CONSTRAINT bid_ask_spread CHECK (ask_rate >= bid_rate)
);

-- Market making liquidity pools
CREATE TABLE liquidity_pools (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    currency_a currency_type NOT NULL,
    currency_b currency_type NOT NULL,
    reserve_a DECIMAL(28,18) NOT NULL DEFAULT 0.0,
    reserve_b DECIMAL(28,18) NOT NULL DEFAULT 0.0,
    total_liquidity_tokens DECIMAL(28,18) NOT NULL DEFAULT 0.0,
    fee_rate DECIMAL(10,8) NOT NULL DEFAULT 0.003, -- 0.3% default fee
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(currency_a, currency_b),
    CONSTRAINT different_pool_currencies CHECK (currency_a != currency_b),
    CONSTRAINT positive_reserves CHECK (reserve_a >= 0 AND reserve_b >= 0)
);

-- Liquidity provider positions
CREATE TABLE liquidity_positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    pool_id UUID REFERENCES liquidity_pools(id) ON DELETE CASCADE,
    liquidity_tokens DECIMAL(28,18) NOT NULL DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(agent_id, pool_id),
    CONSTRAINT positive_liquidity_tokens CHECK (liquidity_tokens >= 0)
);

-- Currency supply tracking
CREATE TABLE currency_supply (
    currency_type currency_type PRIMARY KEY,
    total_supply DECIMAL(28,18) NOT NULL DEFAULT 0.0,
    circulating_supply DECIMAL(28,18) NOT NULL DEFAULT 0.0,
    target_supply DECIMAL(28,18) NOT NULL DEFAULT 1000000.0,
    max_supply DECIMAL(28,18),
    inflation_rate DECIMAL(10,8) DEFAULT 0.0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT positive_supply CHECK (total_supply >= 0 AND circulating_supply >= 0),
    CONSTRAINT supply_consistency CHECK (circulating_supply <= total_supply)
);

-- Performance indexes for optimization
CREATE INDEX idx_currency_balances_agent_currency ON currency_balances(agent_id, currency_type);
CREATE INDEX idx_currency_balances_updated ON currency_balances(last_updated);
CREATE INDEX idx_transactions_agent_from ON transactions(from_agent_id);
CREATE INDEX idx_transactions_agent_to ON transactions(to_agent_id);
CREATE INDEX idx_transactions_created ON transactions(created_at);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_exchange_rates_currencies ON exchange_rates(from_currency, to_currency);
CREATE INDEX idx_exchange_rates_updated ON exchange_rates(created_at);
CREATE INDEX idx_agents_agent_id ON agents(agent_id);
CREATE INDEX idx_agents_reputation ON agents(reputation_score);

-- Initialize currency supply data
INSERT INTO currency_supply (currency_type, total_supply, circulating_supply, target_supply, max_supply) VALUES
('economic', 0.0, 0.0, 1000000.0, 10000000.0),
('quality', 0.0, 0.0, 500000.0, 5000000.0),
('temporal', 0.0, 0.0, 2000000.0, NULL), -- No max supply due to decay
('reliability', 0.0, 0.0, 100000.0, 1000000.0),
('innovation', 0.0, 0.0, 50000.0, 500000.0);

-- Initialize exchange rate pairs with default rates
INSERT INTO exchange_rates (from_currency, to_currency, rate, bid_rate, ask_rate) VALUES
-- Economic to others
('economic', 'quality', 1.2, 1.194, 1.206),
('economic', 'temporal', 0.8, 0.796, 0.804),
('economic', 'reliability', 1.5, 1.485, 1.515),
('economic', 'innovation', 0.9, 0.891, 0.909),

-- Quality to others (excluding economic)
('quality', 'temporal', 0.667, 0.660, 0.674),
('quality', 'reliability', 1.25, 1.238, 1.263),
('quality', 'innovation', 0.75, 0.743, 0.758),

-- Temporal to others (excluding economic, quality)
('temporal', 'reliability', 1.875, 1.856, 1.894),
('temporal', 'innovation', 1.125, 1.114, 1.136),

-- Reliability to innovation
('reliability', 'innovation', 0.6, 0.594, 0.606);

-- Add reverse pairs (calculated as 1/rate)
INSERT INTO exchange_rates (from_currency, to_currency, rate, bid_rate, ask_rate)
SELECT 
    to_currency as from_currency,
    from_currency as to_currency,
    1.0/rate as rate,
    1.0/ask_rate as bid_rate,
    1.0/bid_rate as ask_rate
FROM exchange_rates;

-- Initialize liquidity pools for major pairs
INSERT INTO liquidity_pools (currency_a, currency_b, reserve_a, reserve_b, total_liquidity_tokens) VALUES
('economic', 'quality', 10000.0, 8333.************333, 9128.************),
('economic', 'temporal', 10000.0, 12500.0, 11180.************),
('economic', 'reliability', 10000.0, 6666.************, 8164.************),
('quality', 'temporal', 10000.0, 15000.0, 12247.************),
('temporal', 'reliability', 10000.0, 5333.************, 7302.************);

-- Functions for currency operations

-- Function to get current balance
CREATE OR REPLACE FUNCTION get_currency_balance(
    p_agent_id UUID,
    p_currency_type currency_type
) RETURNS DECIMAL(28,18) AS $$
DECLARE
    current_balance DECIMAL(28,18);
BEGIN
    SELECT balance INTO current_balance
    FROM currency_balances
    WHERE agent_id = p_agent_id AND currency_type = p_currency_type;
    
    RETURN COALESCE(current_balance, 0.0);
END;
$$ LANGUAGE plpgsql;

-- Function to update balance with automatic decay/interest calculation
CREATE OR REPLACE FUNCTION update_currency_balance(
    p_agent_id UUID,
    p_currency_type currency_type,
    p_amount_change DECIMAL(28,18),
    p_transaction_id VARCHAR(255) DEFAULT NULL
) RETURNS DECIMAL(28,18) AS $$
DECLARE
    current_balance DECIMAL(28,18);
    new_balance DECIMAL(28,18);
    time_since_update INTERVAL;
    decay_factor DECIMAL(28,18);
    interest_factor DECIMAL(28,18);
    agent_reputation DECIMAL(10,8);
BEGIN
    -- Get current balance and time since last update
    SELECT balance, NOW() - last_updated, NOW() - last_decay_calculation, NOW() - last_interest_calculation
    INTO current_balance, time_since_update
    FROM currency_balances
    WHERE agent_id = p_agent_id AND currency_type = p_currency_type;
    
    -- If no balance record exists, create one
    IF current_balance IS NULL THEN
        INSERT INTO currency_balances (agent_id, currency_type, balance)
        VALUES (p_agent_id, p_currency_type, 0.0);
        current_balance := 0.0;
        time_since_update := INTERVAL '0';
    END IF;
    
    -- Apply currency-specific time effects
    CASE p_currency_type
        WHEN 'temporal' THEN
            -- Apply exponential decay: V = V₀ * exp(-β * time_hours)
            -- Using β = 0.01 per hour (1% hourly decay)
            decay_factor := EXP(-0.01 * EXTRACT(EPOCH FROM time_since_update) / 3600.0);
            current_balance := current_balance * decay_factor;
            
        WHEN 'reliability' THEN
            -- Apply reputation interest: 5-15% annual based on reputation
            SELECT reputation_score INTO agent_reputation
            FROM agents WHERE id = p_agent_id;
            
            -- Interest rate: 5% + (reputation * 10%)
            interest_factor := 1.0 + ((0.05 + agent_reputation * 0.10) * EXTRACT(EPOCH FROM time_since_update) / 31536000.0);
            current_balance := current_balance * interest_factor;
            
        ELSE
            -- No time effects for economic, quality, innovation currencies
            NULL;
    END CASE;
    
    -- Apply the balance change
    new_balance := current_balance + p_amount_change;
    
    -- Ensure balance doesn't go negative
    IF new_balance < 0 THEN
        RAISE EXCEPTION 'Insufficient balance. Current: %, Requested: %', current_balance, p_amount_change;
    END IF;
    
    -- Update the balance record
    UPDATE currency_balances
    SET 
        balance = new_balance,
        last_updated = NOW(),
        last_decay_calculation = CASE WHEN p_currency_type = 'temporal' THEN NOW() ELSE last_decay_calculation END,
        last_interest_calculation = CASE WHEN p_currency_type = 'reliability' THEN NOW() ELSE last_interest_calculation END
    WHERE agent_id = p_agent_id AND currency_type = p_currency_type;
    
    RETURN new_balance;
END;
$$ LANGUAGE plpgsql;

-- Function to execute currency transfer
CREATE OR REPLACE FUNCTION transfer_currency(
    p_from_agent_id UUID,
    p_to_agent_id UUID,
    p_currency_type currency_type,
    p_amount DECIMAL(28,18),
    p_transaction_id VARCHAR(255) DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    transaction_uuid UUID;
    fee_amount DECIMAL(28,18) := 0.001; -- 0.1% transfer fee
    net_amount DECIMAL(28,18);
BEGIN
    -- Calculate net amount after fee
    net_amount := p_amount * (1.0 - fee_amount);
    
    -- Generate transaction ID if not provided
    IF p_transaction_id IS NULL THEN
        p_transaction_id := 'transfer_' || uuid_generate_v4()::text;
    END IF;
    
    -- Start transaction
    BEGIN
        -- Deduct from sender (including fee)
        PERFORM update_currency_balance(p_from_agent_id, p_currency_type, -p_amount, p_transaction_id);
        
        -- Add to receiver (net amount)
        PERFORM update_currency_balance(p_to_agent_id, p_currency_type, net_amount, p_transaction_id);
        
        -- Record transaction
        INSERT INTO transactions (
            transaction_id, from_agent_id, to_agent_id, currency_type,
            transaction_type, amount, fee, status, completed_at
        ) VALUES (
            p_transaction_id, p_from_agent_id, p_to_agent_id, p_currency_type,
            'transfer', p_amount, p_amount - net_amount, 'completed', NOW()
        ) RETURNING id INTO transaction_uuid;
        
        RETURN transaction_uuid;
        
    EXCEPTION WHEN OTHERS THEN
        -- Record failed transaction
        INSERT INTO transactions (
            transaction_id, from_agent_id, to_agent_id, currency_type,
            transaction_type, amount, status, metadata
        ) VALUES (
            p_transaction_id, p_from_agent_id, p_to_agent_id, p_currency_type,
            'transfer', p_amount, 'failed', jsonb_build_object('error', SQLERRM)
        );
        
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql;

-- Function to mint currency
CREATE OR REPLACE FUNCTION mint_currency(
    p_agent_id UUID,
    p_currency_type currency_type,
    p_amount DECIMAL(28,18),
    p_reason TEXT DEFAULT 'manual_mint'
) RETURNS UUID AS $$
DECLARE
    transaction_uuid UUID;
    transaction_id_str VARCHAR(255);
    current_supply DECIMAL(28,18);
    max_supply_limit DECIMAL(28,18);
BEGIN
    -- Generate transaction ID
    transaction_id_str := 'mint_' || uuid_generate_v4()::text;
    
    -- Check supply limits
    SELECT total_supply, max_supply INTO current_supply, max_supply_limit
    FROM currency_supply WHERE currency_type = p_currency_type;
    
    IF max_supply_limit IS NOT NULL AND (current_supply + p_amount) > max_supply_limit THEN
        RAISE EXCEPTION 'Minting would exceed max supply limit. Current: %, Requested: %, Max: %', 
            current_supply, p_amount, max_supply_limit;
    END IF;
    
    BEGIN
        -- Add to agent balance
        PERFORM update_currency_balance(p_agent_id, p_currency_type, p_amount, transaction_id_str);
        
        -- Update total supply
        UPDATE currency_supply
        SET 
            total_supply = total_supply + p_amount,
            circulating_supply = circulating_supply + p_amount,
            last_updated = NOW()
        WHERE currency_type = p_currency_type;
        
        -- Record transaction
        INSERT INTO transactions (
            transaction_id, to_agent_id, currency_type, transaction_type,
            amount, status, completed_at, metadata
        ) VALUES (
            transaction_id_str, p_agent_id, p_currency_type, 'mint',
            p_amount, 'completed', NOW(), jsonb_build_object('reason', p_reason)
        ) RETURNING id INTO transaction_uuid;
        
        RETURN transaction_uuid;
        
    EXCEPTION WHEN OTHERS THEN
        -- Record failed transaction
        INSERT INTO transactions (
            transaction_id, to_agent_id, currency_type, transaction_type,
            amount, status, metadata
        ) VALUES (
            transaction_id_str, p_agent_id, p_currency_type, 'mint',
            p_amount, 'failed', jsonb_build_object('error', SQLERRM, 'reason', p_reason)
        );
        
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate exchange rate with market impact
CREATE OR REPLACE FUNCTION calculate_exchange_rate(
    p_from_currency currency_type,
    p_to_currency currency_type,
    p_amount DECIMAL(28,18)
) RETURNS TABLE(
    rate DECIMAL(28,18),
    amount_out DECIMAL(28,18),
    price_impact DECIMAL(10,8),
    fee DECIMAL(28,18)
) AS $$
DECLARE
    base_rate DECIMAL(28,18);
    pool_reserve_from DECIMAL(28,18);
    pool_reserve_to DECIMAL(28,18);
    pool_fee_rate DECIMAL(10,8);
    k_constant DECIMAL(28,18);
    amount_out_calc DECIMAL(28,18);
    effective_rate DECIMAL(28,18);
    impact DECIMAL(10,8);
    fee_amount DECIMAL(28,18);
BEGIN
    -- Get base exchange rate
    SELECT er.rate INTO base_rate
    FROM exchange_rates er
    WHERE er.from_currency = p_from_currency AND er.to_currency = p_to_currency;
    
    -- Get liquidity pool data
    SELECT 
        CASE WHEN currency_a = p_from_currency THEN reserve_a ELSE reserve_b END,
        CASE WHEN currency_a = p_from_currency THEN reserve_b ELSE reserve_a END,
        fee_rate
    INTO pool_reserve_from, pool_reserve_to, pool_fee_rate
    FROM liquidity_pools
    WHERE (currency_a = p_from_currency AND currency_b = p_to_currency) OR
          (currency_a = p_to_currency AND currency_b = p_from_currency);
    
    -- If no pool exists, use base rate with higher fee
    IF pool_reserve_from IS NULL THEN
        fee_amount := p_amount * 0.01; -- 1% fee without pool
        amount_out_calc := (p_amount - fee_amount) * base_rate;
        effective_rate := base_rate;
        impact := 0.01; -- 1% impact
    ELSE
        -- Calculate AMM exchange using constant product formula
        -- k = x * y (constant)
        k_constant := pool_reserve_from * pool_reserve_to;
        
        -- Calculate fee
        fee_amount := p_amount * pool_fee_rate;
        
        -- Amount after fee
        DECLARE
            amount_in_after_fee DECIMAL(28,18) := p_amount - fee_amount;
        BEGIN
            -- New reserve after adding input
            DECLARE
                new_reserve_from DECIMAL(28,18) := pool_reserve_from + amount_in_after_fee;
                new_reserve_to DECIMAL(28,18);
            BEGIN
                -- Calculate new reserve_to to maintain k constant
                new_reserve_to := k_constant / new_reserve_from;
                
                -- Amount out is the difference
                amount_out_calc := pool_reserve_to - new_reserve_to;
                
                -- Effective rate
                effective_rate := amount_out_calc / p_amount;
                
                -- Price impact
                impact := ABS(effective_rate - base_rate) / base_rate;
            END;
        END;
    END IF;
    
    RETURN QUERY SELECT effective_rate, amount_out_calc, impact, fee_amount;
END;
$$ LANGUAGE plpgsql;

-- Create initial test agents
INSERT INTO agents (agent_id, name, reputation_score) VALUES
('agent_001', 'Test Agent Alpha', 0.85),
('agent_002', 'Test Agent Beta', 0.92),
('agent_003', 'Test Agent Gamma', 0.78),
('agent_004', 'Test Agent Delta', 0.95),
('agent_005', 'Test Agent Epsilon', 0.88);

-- Initialize balances for test agents
INSERT INTO currency_balances (agent_id, currency_type, balance) 
SELECT a.id, c.currency_type, 1000.0
FROM agents a
CROSS JOIN (VALUES 
    ('economic'::currency_type),
    ('quality'::currency_type),
    ('temporal'::currency_type),
    ('reliability'::currency_type),
    ('innovation'::currency_type)
) c(currency_type);

-- Update currency supply to reflect initial balances
UPDATE currency_supply 
SET 
    total_supply = 5000.0,
    circulating_supply = 5000.0
WHERE currency_type IN ('economic', 'quality', 'temporal', 'reliability', 'innovation');

