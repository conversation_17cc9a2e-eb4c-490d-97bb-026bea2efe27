# Technical Context for Currency Architect

## Current VibeLaunch Value System

VibeLaunch currently uses traditional decimal currency (USD) with no actual payment processing. Your challenge is to design a multi-dimensional value system within these constraints.

## Existing Financial Infrastructure

### Current Money Handling

```sql
-- Simple decimal fields (10,2 precision = cents)
contracts.budget DECIMAL(10, 2)    -- Max: $99,999,999.99
bids.price DECIMAL(10, 2)          -- What agents charge

-- No actual payment tables
-- No transaction history
-- No wallet/balance tracking
-- No escrow mechanisms
```

### What's Missing
- ❌ Payment gateway integration
- ❌ Transaction/ledger tables  
- ❌ Balance tracking
- ❌ Refund mechanisms
- ❌ Multi-currency support
- ❌ Micropayments (<$1.00)
- ❌ Token/credit system

## Technical Opportunities for Currency Design

### 1. JSONB for Multi-Dimensional Value

```sql
-- Extend bids table with value vectors
ALTER TABLE bids ADD COLUMN value_proposition JSONB DEFAULT '{
  "monetary": {
    "base_price": 100.00,
    "rush_premium": 50.00,
    "quality_tier": "premium"
  },
  "temporal": {
    "delivery_hours": 24,
    "response_time_minutes": 15
  },
  "quality": {
    "accuracy_guarantee": 0.95,
    "revision_rounds": 3,
    "satisfaction_score": 4.8
  },
  "reputation": {
    "completed_similar": 47,
    "success_rate": 0.94,
    "client_ratings": [4.9, 5.0, 4.8]
  }
}';
```

### 2. Virtual Currency Tables (Your Design Space)

```sql
-- Option 1: Credit-based system
CREATE TABLE organization_credits (
  id UUID PRIMARY KEY,
  organization_id UUID NOT NULL,
  credit_balance DECIMAL(15, 4), -- More precision for fractional credits
  credit_type TEXT, -- 'purchased', 'earned', 'promotional'
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Option 2: Multi-currency ledger
CREATE TABLE value_ledger (
  id UUID PRIMARY KEY,
  organization_id UUID NOT NULL,
  transaction_type TEXT, -- 'deposit', 'withdrawal', 'transfer'
  value_dimensions JSONB, -- Multiple value types
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Option 3: Token economy
CREATE TABLE token_balances (
  organization_id UUID,
  token_type TEXT, -- 'VIBE', 'QUALITY', 'SPEED', 'INNOVATION'
  balance DECIMAL(18, 6), -- Crypto-style precision
  locked_until TIMESTAMPTZ,
  PRIMARY KEY (organization_id, token_type)
);
```

### 3. Exchange Rate Mechanisms

```sql
-- Dynamic exchange rates between value dimensions
CREATE TABLE exchange_rates (
  from_dimension TEXT,
  to_dimension TEXT,
  rate DECIMAL(10, 6),
  algorithm TEXT, -- 'fixed', 'market', 'algorithmic'
  updated_at TIMESTAMPTZ,
  PRIMARY KEY (from_dimension, to_dimension)
);

-- Function to convert between value types
CREATE FUNCTION convert_value(
  amount DECIMAL,
  from_dim TEXT,
  to_dim TEXT
) RETURNS DECIMAL AS $$
DECLARE
  rate DECIMAL;
BEGIN
  SELECT rate INTO rate
  FROM exchange_rates
  WHERE from_dimension = from_dim
  AND to_dimension = to_dim;
  
  RETURN amount * rate;
END;
$$ LANGUAGE plpgsql;
```

### 4. Escrow Patterns (Without External Systems)

```sql
-- Implement escrow via state management
CREATE TYPE escrow_status AS ENUM (
  'locked',      -- Funds committed but not released
  'releasing',   -- Partial release in progress
  'released',    -- Fully released to agent
  'refunded'     -- Returned to client
);

CREATE TABLE escrow_holds (
  id UUID PRIMARY KEY,
  contract_id UUID REFERENCES contracts(id),
  amount DECIMAL(10, 2),
  value_dimensions JSONB, -- Multi-dimensional value
  status escrow_status DEFAULT 'locked',
  release_conditions JSONB, -- Smart contract-like rules
  created_at TIMESTAMPTZ DEFAULT now()
);
```

## Constraints You Must Work Within

### 1. No External Payment Processing
- Cannot integrate Stripe/PayPal/crypto
- Must track value internally only
- No real money movement
- Focus on value accounting, not payments

### 2. Decimal Precision Limits
```sql
DECIMAL(precision, scale)
-- precision: total digits
-- scale: decimal places
-- Max precision in PostgreSQL: 131072 digits

-- Current: DECIMAL(10,2) = $$$$$$$$$.cc
-- Could extend to: DECIMAL(18,6) for more precision
```

### 3. Multi-Tenant Isolation
```sql
-- Every currency operation must be org-scoped
CREATE POLICY currency_isolation ON value_ledger
  USING (organization_id = current_org_id());

-- No cross-organization transfers without special handling
-- Each org has independent currency space
```

### 4. Transaction Atomicity
```sql
-- All value transfers must be atomic
BEGIN;
  -- Deduct from buyer
  UPDATE organization_credits 
  SET credit_balance = credit_balance - 100
  WHERE organization_id = ? AND credit_balance >= 100;
  
  -- Add to seller (minus platform fee)
  UPDATE organization_credits
  SET credit_balance = credit_balance + 90
  WHERE organization_id = ?;
  
  -- Record platform fee
  INSERT INTO platform_fees (amount, transaction_id)
  VALUES (10, ?);
COMMIT;
```

## Implementation Patterns

### 1. Multi-Dimensional Value Storage

```sql
-- Store complex value in JSONB
CREATE TABLE bid_values (
  bid_id UUID REFERENCES bids(id),
  value_vector JSONB NOT NULL,
  /*
  Example value_vector:
  {
    "price": {"amount": 100, "currency": "USD"},
    "time": {"delivery_days": 3, "rush_available": true},
    "quality": {"tier": "premium", "guarantees": ["accuracy", "originality"]},
    "support": {"revisions": 3, "consultation_hours": 2},
    "innovation": {"novel_techniques": ["AI-enhanced", "data-driven"]},
    "reputation": {"score": 0.94, "reviews": 127}
  }
  */
  computed_score DECIMAL(10, 4),
  PRIMARY KEY (bid_id)
);
```

### 2. Value Aggregation Functions

```sql
-- Aggregate multi-dimensional value
CREATE FUNCTION calculate_total_value(
  value_vector JSONB,
  weights JSONB DEFAULT '{"price": 0.4, "time": 0.2, "quality": 0.4}'
) RETURNS DECIMAL AS $$
DECLARE
  total DECIMAL := 0;
  dimension TEXT;
  weight DECIMAL;
BEGIN
  FOR dimension, weight IN 
    SELECT * FROM jsonb_each_text(weights)
  LOOP
    total := total + (
      (value_vector->dimension->>'normalized_score')::DECIMAL * weight
    );
  END LOOP;
  
  RETURN total;
END;
$$ LANGUAGE plpgsql;
```

### 3. Currency Event System

```sql
-- Broadcast value changes
CREATE TRIGGER value_change_notify
AFTER INSERT OR UPDATE ON value_ledger
FOR EACH ROW
EXECUTE FUNCTION notify_value_change();

CREATE FUNCTION notify_value_change() RETURNS TRIGGER AS $$
BEGIN
  PERFORM pg_notify('value_events', json_build_object(
    'type', 'value_change',
    'organization_id', NEW.organization_id,
    'change', NEW.value_dimensions,
    'timestamp', now()
  )::text);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## Platform Economics to Consider

### 1. Fee Structure Options
```sql
-- Platform fee calculation
CREATE FUNCTION calculate_platform_fee(
  base_amount DECIMAL,
  fee_structure JSONB DEFAULT '{
    "base_rate": 0.10,
    "volume_discount": {"threshold": 10000, "rate": 0.08},
    "premium_rate": 0.15,
    "minimum_fee": 1.00
  }'
) RETURNS DECIMAL AS $$
  -- Implement tiered fee structure
$$ LANGUAGE plpgsql;
```

### 2. Value Creation Mechanisms
```sql
-- Bonus/reward distribution
CREATE TABLE value_rewards (
  id UUID PRIMARY KEY,
  recipient_type TEXT, -- 'agent', 'organization', 'platform'
  recipient_id UUID,
  reward_type TEXT, -- 'quality_bonus', 'speed_bonus', 'innovation_reward'
  value_dimensions JSONB,
  reason TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

### 3. Inflation/Deflation Controls
```sql
-- Track total value in system
CREATE VIEW system_value_metrics AS
SELECT
  SUM((value_dimensions->>'total_usd')::DECIMAL) as total_value,
  COUNT(DISTINCT organization_id) as active_orgs,
  AVG((value_dimensions->>'transaction_size')::DECIMAL) as avg_transaction
FROM value_ledger
WHERE created_at > now() - interval '30 days';
```

## Migration Path for Currency System

### Phase 1: Value Tracking (No Real Money)
```sql
-- Start with simple credit tracking
ALTER TABLE organizations ADD COLUMN 
  virtual_credits DECIMAL(10, 2) DEFAULT 1000.00;
```

### Phase 2: Multi-Dimensional Bids
```sql
-- Add value vectors to existing bids
ALTER TABLE bids ADD COLUMN 
  value_proposition JSONB;
```

### Phase 3: Ledger System
```sql
-- Implement full transaction history
CREATE TABLE value_transactions (...);
```

### Phase 4: Exchange Mechanisms
```sql
-- Enable value type conversions
CREATE TABLE exchange_rates (...);
```

## Example Implementation: Reputation-Weighted Credits

```sql
-- Migration: 20250114_currency_architect_reputation_credits.sql

-- Reputation-based currency multiplier
CREATE OR REPLACE FUNCTION calculate_reputation_credits(
  base_credits DECIMAL,
  agent_role TEXT
) RETURNS DECIMAL AS $$
DECLARE
  reputation_score DECIMAL;
  multiplier DECIMAL;
BEGIN
  -- Get reputation (stub for now)
  reputation_score := 0.85; -- Would query agent_performance
  
  -- Calculate multiplier
  multiplier := 1.0 + (reputation_score * 0.5); -- Up to 50% bonus
  
  RETURN base_credits * multiplier;
END;
$$ LANGUAGE plpgsql;

-- Automatic credit adjustment
CREATE TRIGGER adjust_credits_for_reputation
BEFORE INSERT ON value_ledger
FOR EACH ROW
WHEN (NEW.transaction_type = 'agent_earning')
EXECUTE FUNCTION apply_reputation_multiplier();
```

## Critical Design Principles

1. **Start Simple**: Basic credits before complex tokens
2. **Use JSONB**: Flexible value representation
3. **Maintain Atomicity**: All transfers must be atomic
4. **Enable Auditing**: Full transaction history
5. **Plan for Scale**: Design for 1M+ transactions
6. **Consider Orgs**: Multi-tenant from the start
7. **No External Deps**: Everything in PostgreSQL
8. **Incremental Rollout**: Phase implementation
9. **Clear Documentation**: SQL migrations for everything
10. **Testability**: Can simulate without real money

Your currency system must create proper incentives while working within PostgreSQL constraints. Think beyond traditional money - you're designing value representation for AI agents who can process complex, multi-dimensional worth.