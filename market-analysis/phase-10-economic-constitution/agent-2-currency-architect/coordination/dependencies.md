# Agent 2 (Currency Architect) - Coordination Dependencies

## Your Dependencies

### From Agent 1 (Market Theorist):
1. **Value Dimension Definitions**
   - Mathematical definitions of quality, speed, reliability, innovation
   - How these dimensions interact and combine
   - Measurement methodologies for each dimension

2. **Economic Laws**
   - Conservation of Value principles
   - Information value dynamics
   - Network effect formulations
   - Team synergy calculations

3. **Exchange Principles**
   - How different types of value can be exchanged
   - Conversion rates between value dimensions
   - Arbitrage prevention mechanisms

## Your Outputs (What Others Need From You)

### For Agent 3 (Microstructure Designer):
1. **Currency Specifications**
   - Complete properties of each currency
   - Divisibility and minimum units
   - Transfer and settlement rules
   - Real-time balance tracking requirements

2. **Exchange Pair Definitions**
   - Which currencies can be exchanged
   - Initial exchange rate models
   - Market making requirements
   - Liquidity provision rules

### For Agent 4 (Financial Ecosystem Engineer):
1. **Currency Risk Profiles**
   - Volatility characteristics of each currency
   - Correlation matrices between currencies
   - Supply/demand elasticities
   - Crisis behavior models

2. **Derivative Underlying Assets**
   - Which currencies can be used as collateral
   - Interest rate models for each currency
   - Currency future specifications
   - Option-eligible currencies

### For Agent 5 (Governance Philosopher):
1. **Monetary Policy Framework**
   - Currency supply control mechanisms
   - Emergency intervention tools
   - Governance parameters for each currency
   - Amendment procedures for currency rules

2. **Currency Rights**
   - Ownership and transfer rights
   - Privacy considerations
   - Freezing/seizure conditions
   - Cross-border/cross-system portability

## Critical Interfaces

### Currency Definition Interface
```typescript
interface CurrencyDefinition {
  // Basic Properties
  symbol: string;
  name: string;
  type: 'base' | 'specialized' | 'meta';
  
  // Economic Properties
  supply: {
    initial: number;
    maximum?: number;
    mintingRate?: number;
    burningRate?: number;
  };
  
  // Technical Properties
  divisibility: number; // decimal places
  transferable: boolean;
  storable: boolean;
  
  // Behavioral Properties
  interestRate?: number;
  decayFunction?: DecayFunction;
  networkEffects?: NetworkFunction;
  
  // Governance
  governanceLevel: 'platform' | 'market' | 'autonomous';
  parameterControl: ParameterControl[];
}
```

### Exchange Mechanism Interface
```typescript
interface ExchangeMechanism {
  baseCurrency: string;
  quoteCurrency: string;
  
  pricingModel: {
    type: 'fixed' | 'floating' | 'managed' | 'algorithmic';
    parameters: any;
  };
  
  marketStructure: {
    continuous: boolean;
    sessionTimes?: TradingSession[];
    minimumTickSize: number;
    minimumOrderSize: number;
  };
  
  liquidityRequirements: {
    minimumDepth: number;
    maximumSpread: number;
    marketMakerIncentives: Incentive[];
  };
}
```

## Potential Conflicts

### With Agent 3 (Microstructure Designer):
- **Issue**: Non-transferable reputation vs. liquid markets
- **Your Position**: Reputation integrity requires non-transferability
- **Resolution Approach**: Create reputation-backed derivatives that can trade while base reputation remains locked

### With Agent 4 (Financial Engineer):
- **Issue**: Currency stability vs. derivative leverage
- **Your Position**: Core currencies need stability for system function
- **Resolution Approach**: Create separate "trading" currencies for speculation while keeping core currencies stable

### With Agent 1 (Market Theorist):
- **Issue**: Complex value dimensions vs. practical currencies
- **Your Position**: Currencies must be simple enough for agents to use
- **Resolution Approach**: Create currency indices that aggregate complex value dimensions

## Coordination Timeline

1. **Week 1**: Wait for Agent 1's value definitions and laws
2. **Week 2**: Design base currency suite and properties
3. **Week 3**: Create exchange mechanisms and monetary policy
4. **Week 4**: Coordinate with Agent 3 on market integration

## Success Criteria

Your work succeeds when:
1. All value dimensions have corresponding currencies
2. Exchange rates enable efficient value transfer
3. Currency system remains stable under stress
4. Monetary policy tools can maintain targets
5. Integration with markets is seamless

## Synchronization Requirements

### With Agent 3 (Daily during Week 3-4):
- Morning: Share currency specifications
- Afternoon: Review market integration issues
- End of day: Update interface definitions
- Focus: Order book compatibility, settlement procedures

### With Agent 4 (Week 4):
- Define which currencies support derivatives
- Specify collateral requirements
- Design margin currencies
- Create risk parameters

## Communication Protocol

- **Output Format**: Technical specifications with economic rationale
- **Documentation**: Each currency gets a detailed design document
- **Testing**: Provide simulation parameters for each currency
- **Updates**: Daily progress reports during parallel work with Agent 3

---

*Remember: Your currencies are the lifeblood of the economy. They must be stable enough for commerce but flexible enough for innovation.*