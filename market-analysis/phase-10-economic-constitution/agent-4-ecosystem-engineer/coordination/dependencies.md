# Agent 4 (Financial Ecosystem Engineer) - Coordination Dependencies

## Your Dependencies

### From Agent 1 (Market Theorist):
1. **Risk Allocation Theory**
   - Optimal risk-bearing principles
   - Information asymmetry solutions
   - Moral hazard prevention mechanisms
   - Welfare maximization under uncertainty

2. **Market Dynamics**
   - Volatility generation mechanisms
   - Price formation under uncertainty
   - Liquidity creation principles
   - Market failure modes

### From Agent 2 (Currency Architect):
1. **Currency Properties**
   - Risk profiles of each currency
   - Interest rate models
   - Correlation structures
   - Supply/demand dynamics

2. **Collateral Specifications**
   - Which currencies can serve as collateral
   - Haircut schedules
   - Cross-currency margins
   - Settlement currencies

### From Agent 3 (Microstructure Designer):
1. **Market Infrastructure**
   - Trading APIs and protocols
   - Real-time data feeds
   - Order execution capabilities
   - Settlement mechanisms

2. **Market Analytics**
   - Price history and volatility
   - Liquidity depth metrics
   - Microstructure statistics
   - Trading patterns

## Your Outputs (What Others Need From You)

### For Agent 5 (Governance Philosopher):
1. **Risk Management Framework**
   - Systemic risk indicators
   - Intervention triggers
   - Capital requirements
   - Stress test results
   - Default procedures

2. **Financial Instrument Registry**
   - All derivatives and their properties
   - Risk classifications
   - Governance requirements
   - Innovation pathways
   - Sunset provisions

## Critical Interfaces

### Derivative Specification Interface
```typescript
interface DerivativeInstrument {
  // Basic Properties
  name: string;
  type: 'future' | 'option' | 'swap' | 'structured';
  underlying: AssetIdentifier | AssetBasket;
  
  // Contract Terms
  terms: {
    notional: CurrencyAmount;
    maturity?: Date;
    strike?: number;
    settlementType: 'physical' | 'cash' | 'hybrid';
  };
  
  // Risk Profile
  riskMetrics: {
    delta?: number;
    gamma?: number;
    vega?: number;
    theta?: number;
    rho?: number;
    customMetrics?: CustomRisk[];
  };
  
  // Trading Rules
  trading: {
    minimumSize: number;
    tickSize: number;
    positionLimits: PositionLimit[];
    marginRequirements: MarginSchedule;
  };
  
  // Governance
  approval: {
    level: 'automatic' | 'committee' | 'vote';
    riskCategory: 1 | 2 | 3 | 4 | 5;
    reporting: ReportingRequirement[];
  };
}
```

### Risk Monitoring Interface
```typescript
interface RiskMonitoring {
  // Real-time Metrics
  marketRisk: {
    VaR: ValueAtRisk;
    stressTests: StressScenario[];
    sensitivities: GreekLimits;
    backtesting: BacktestResult[];
  };
  
  creditRisk: {
    exposures: CounterpartyExposure[];
    pdModel: ProbabilityOfDefault;
    lgdModel: LossGivenDefault;
    collateralCoverage: CollateralRatio[];
  };
  
  liquidityRisk: {
    cashflow: CashflowProjection;
    fundingSources: FundingLadder;
    liquidityRatios: LiquidityMetric[];
    stressFunding: StressFunding;
  };
  
  // Alerts and Actions
  alerts: {
    thresholds: AlertThreshold[];
    escalation: EscalationPath;
    automaticActions: AutoAction[];
    reporting: AlertReport[];
  };
}
```

## Potential Conflicts

### With Agent 3 (Microstructure Designer):
- **Issue**: Derivative complexity vs. market stability
- **Your Position**: Innovation requires complex instruments
- **Resolution Approach**: Tiered market structure with sandbox for experimental products

### With Agent 5 (Governance):
- **Issue**: Financial innovation vs. regulatory control
- **Your Position**: Excessive regulation stifles beneficial innovation
- **Resolution Approach**: Risk-based governance with automatic approval for low-risk innovations

### With Agent 2 (Currency):
- **Issue**: Derivative leverage vs. currency stability
- **Your Position**: Derivatives improve price discovery and risk management
- **Resolution Approach**: Separate base currency stability from derivative markets

## Coordination Timeline

1. **Week 1-2**: Wait for Agents 1-3 to complete foundations
2. **Week 3**: Design core derivatives based on available assets
3. **Week 4**: Create risk management framework
4. **Week 5**: Build prediction markets and insurance products
5. **Week 6**: Integrate with governance requirements

## Success Criteria

Your work succeeds when:
1. Risk can be efficiently transferred to willing bearers
2. Quality insurance is affordable and effective
3. Prediction markets accurately forecast outcomes
4. Capital velocity increases 3x
5. No systemic risk buildup

## Critical Design Decisions

### Product Priorities
1. Which derivatives to launch first?
2. Insurance vs. prediction markets focus?
3. Structured products complexity level?
4. Innovation sandbox parameters?

### Risk Management
1. Margin methodology (SPAN vs. VaR vs. custom)?
2. Central clearing requirements?
3. Position limit framework?
4. Default waterfall structure?

### Market Structure
1. Separate derivatives exchange vs. integrated?
2. Professional vs. retail access?
3. Leverage limits by participant type?
4. Cross-margining capabilities?

## Integration Requirements

### From Markets (Agent 3):
- Latency: <10ms for price feeds
- Reliability: 99.99% uptime
- Capacity: 100,000 updates/second
- History: 5 years of tick data

### To Governance (Agent 5):
- Risk reports: Real-time dashboard
- Alerts: <1 second for critical events
- Audit trail: Complete transaction history
- Compliance: Automated rule checking

## Communication Protocol

- **Output Format**: Product specifications with risk assessments
- **Documentation**: Complete term sheets and risk disclosures
- **Testing**: Monte Carlo simulations for all products
- **Support**: 24/7 during market hours post-launch

## Special Considerations

### For AI Agents:
- Can calculate complex derivatives instantly
- Perfect hedging without behavioral biases
- Need protection from correlated strategies
- Require position limits to prevent dominance

### For System Evolution:
- New products should be easy to add
- Failed products gracefully sunset
- Innovation metrics tracked
- Continuous risk model improvement

---

*Remember: You're building the tools that transform simple contracts into a sophisticated financial ecosystem. Your instruments should enable innovation while preventing catastrophe.*