#!/usr/bin/env python3
"""
VibeLaunch Prediction Market System Implementation
Agent 4: Financial Innovation Architect

This module implements the comprehensive prediction market infrastructure including
contract outcome markets, quality achievement markets, timeline accuracy markets,
and agent performance markets with automated market makers.

Author: Manus AI
Date: June 14, 2025
Version: 1.0
"""

import numpy as np
import pandas as pd
from scipy.stats import norm, beta, gamma
from scipy.optimize import minimize_scalar
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import warnings
import json
from datetime import datetime, timedelta
warnings.filterwarnings('ignore')

class MarketType(Enum):
    """Types of prediction markets"""
    BINARY_COMPLETION = "binary_completion"
    QUALITY_SCORE = "quality_score"
    TIMELINE_ACCURACY = "timeline_accuracy"
    AGENT_REPUTATION = "agent_reputation"
    TEAM_FORMATION = "team_formation"
    SKILL_DEVELOPMENT = "skill_development"

class OutcomeType(Enum):
    """Types of market outcomes"""
    BINARY = "binary"
    CONTINUOUS = "continuous"
    CATEGORICAL = "categorical"
    MULTI_DIMENSIONAL = "multi_dimensional"

@dataclass
class PredictionMarket:
    """Prediction market structure"""
    market_id: str
    market_type: MarketType
    outcome_type: OutcomeType
    description: str
    settlement_date: datetime
    settlement_criteria: Dict[str, any]
    current_price: float
    volume: float
    liquidity: float
    participants: int
    accuracy_history: List[float] = field(default_factory=list)

@dataclass
class MarketPosition:
    """Market participant position"""
    participant_id: str
    market_id: str
    position_type: str  # 'long', 'short'
    quantity: float
    entry_price: float
    current_value: float
    unrealized_pnl: float

@dataclass
class QualityDimension:
    """Quality assessment dimension"""
    dimension_name: str
    weight: float
    measurement_method: str
    current_score: Optional[float] = None
    predicted_score: Optional[float] = None

class AutomatedMarketMaker:
    """Automated market maker for prediction markets"""
    
    def __init__(self, initial_liquidity: float = 10000, fee_rate: float = 0.003):
        self.liquidity = initial_liquidity
        self.fee_rate = fee_rate
        self.inventory = {'yes': 0.5, 'no': 0.5}  # Balanced initial inventory
        self.total_volume = 0
        self.price_history = []
        
    def calculate_price(self, outcome: str, trade_size: float, 
                       trade_direction: str) -> Tuple[float, float]:
        """Calculate price and slippage for trade"""
        
        # Logarithmic market scoring rule (LMSR)
        b = self.liquidity / 100  # Liquidity parameter
        
        current_yes_shares = self.inventory['yes'] * self.liquidity
        current_no_shares = self.inventory['no'] * self.liquidity
        
        if outcome == 'yes':
            if trade_direction == 'buy':
                new_yes_shares = current_yes_shares + trade_size
                new_no_shares = current_no_shares
            else:  # sell
                new_yes_shares = current_yes_shares - trade_size
                new_no_shares = current_no_shares
        else:  # 'no'
            if trade_direction == 'buy':
                new_yes_shares = current_yes_shares
                new_no_shares = current_no_shares + trade_size
            else:  # sell
                new_yes_shares = current_yes_shares
                new_no_shares = current_no_shares - trade_size
        
        # LMSR pricing
        old_cost = b * np.log(np.exp(current_yes_shares/b) + np.exp(current_no_shares/b))
        new_cost = b * np.log(np.exp(new_yes_shares/b) + np.exp(new_no_shares/b))
        
        trade_cost = abs(new_cost - old_cost)
        
        # Calculate price including fees
        price_before_fees = trade_cost / trade_size if trade_size > 0 else 0
        price_with_fees = price_before_fees * (1 + self.fee_rate)
        
        # Calculate slippage
        market_price = self._get_current_market_price()
        slippage = abs(price_with_fees - market_price) / market_price if market_price > 0 else 0
        
        return price_with_fees, slippage
    
    def _get_current_market_price(self) -> float:
        """Get current market price for 'yes' outcome"""
        yes_shares = self.inventory['yes'] * self.liquidity
        no_shares = self.inventory['no'] * self.liquidity
        b = self.liquidity / 100
        
        # LMSR price calculation
        exp_yes = np.exp(yes_shares / b)
        exp_no = np.exp(no_shares / b)
        
        price = exp_yes / (exp_yes + exp_no)
        return price
    
    def execute_trade(self, outcome: str, trade_size: float, 
                     trade_direction: str) -> Dict[str, float]:
        """Execute trade and update market state"""
        
        price, slippage = self.calculate_price(outcome, trade_size, trade_direction)
        
        # Update inventory
        if outcome == 'yes':
            if trade_direction == 'buy':
                self.inventory['yes'] += trade_size / self.liquidity
                self.inventory['no'] -= trade_size / self.liquidity
            else:
                self.inventory['yes'] -= trade_size / self.liquidity
                self.inventory['no'] += trade_size / self.liquidity
        else:
            if trade_direction == 'buy':
                self.inventory['no'] += trade_size / self.liquidity
                self.inventory['yes'] -= trade_size / self.liquidity
            else:
                self.inventory['no'] -= trade_size / self.liquidity
                self.inventory['yes'] += trade_size / self.liquidity
        
        # Normalize inventory
        total_inventory = self.inventory['yes'] + self.inventory['no']
        self.inventory['yes'] /= total_inventory
        self.inventory['no'] /= total_inventory
        
        # Update volume and price history
        self.total_volume += trade_size
        current_price = self._get_current_market_price()
        self.price_history.append(current_price)
        
        return {
            'execution_price': price,
            'slippage': slippage,
            'new_market_price': current_price,
            'total_cost': price * trade_size
        }

class PredictionMarketSystem:
    """Comprehensive prediction market system"""
    
    def __init__(self):
        self.markets = {}
        self.positions = {}
        self.participants = {}
        self.amm_instances = {}
        self.settlement_history = []
        
    def create_binary_completion_market(self, contract_id: str, 
                                      completion_probability: float,
                                      settlement_date: datetime) -> PredictionMarket:
        """Create binary completion prediction market"""
        
        market_id = f"COMPLETION_{contract_id}"
        
        market = PredictionMarket(
            market_id=market_id,
            market_type=MarketType.BINARY_COMPLETION,
            outcome_type=OutcomeType.BINARY,
            description=f"Will contract {contract_id} be completed successfully?",
            settlement_date=settlement_date,
            settlement_criteria={
                'completion_threshold': 0.95,
                'quality_threshold': 0.8,
                'timeline_threshold': 1.0
            },
            current_price=completion_probability,
            volume=0,
            liquidity=10000,
            participants=0
        )
        
        # Initialize AMM
        amm = AutomatedMarketMaker(initial_liquidity=10000)
        amm.inventory['yes'] = completion_probability
        amm.inventory['no'] = 1 - completion_probability
        
        self.markets[market_id] = market
        self.amm_instances[market_id] = amm
        
        return market
    
    def create_quality_score_market(self, contract_id: str,
                                  quality_dimensions: List[QualityDimension],
                                  settlement_date: datetime) -> PredictionMarket:
        """Create quality score prediction market"""
        
        market_id = f"QUALITY_{contract_id}"
        
        # Calculate weighted expected quality
        expected_quality = sum(dim.predicted_score * dim.weight 
                             for dim in quality_dimensions 
                             if dim.predicted_score is not None)
        
        market = PredictionMarket(
            market_id=market_id,
            market_type=MarketType.QUALITY_SCORE,
            outcome_type=OutcomeType.CONTINUOUS,
            description=f"What quality score will contract {contract_id} achieve?",
            settlement_date=settlement_date,
            settlement_criteria={
                'quality_dimensions': quality_dimensions,
                'measurement_methods': [dim.measurement_method for dim in quality_dimensions],
                'weights': [dim.weight for dim in quality_dimensions]
            },
            current_price=expected_quality,
            volume=0,
            liquidity=10000,
            participants=0
        )
        
        # Initialize AMM for continuous outcome
        amm = AutomatedMarketMaker(initial_liquidity=10000)
        
        self.markets[market_id] = market
        self.amm_instances[market_id] = amm
        
        return market
    
    def create_timeline_accuracy_market(self, contract_id: str,
                                      expected_completion_date: datetime,
                                      contracted_deadline: datetime) -> PredictionMarket:
        """Create timeline accuracy prediction market"""
        
        market_id = f"TIMELINE_{contract_id}"
        
        # Calculate expected delay in days
        expected_delay = (expected_completion_date - contracted_deadline).days
        on_time_probability = max(0, min(1, 1 - expected_delay / 30))  # Decreases with delay
        
        market = PredictionMarket(
            market_id=market_id,
            market_type=MarketType.TIMELINE_ACCURACY,
            outcome_type=OutcomeType.BINARY,
            description=f"Will contract {contract_id} be delivered on time?",
            settlement_date=contracted_deadline + timedelta(days=7),
            settlement_criteria={
                'deadline': contracted_deadline,
                'grace_period_days': 1,
                'completion_definition': 'client_acceptance'
            },
            current_price=on_time_probability,
            volume=0,
            liquidity=10000,
            participants=0
        )
        
        # Initialize AMM
        amm = AutomatedMarketMaker(initial_liquidity=10000)
        amm.inventory['yes'] = on_time_probability
        amm.inventory['no'] = 1 - on_time_probability
        
        self.markets[market_id] = market
        self.amm_instances[market_id] = amm
        
        return market
    
    def create_agent_reputation_market(self, agent_id: str,
                                     current_reputation: float,
                                     forecast_horizon_months: int) -> PredictionMarket:
        """Create agent reputation prediction market"""
        
        market_id = f"REPUTATION_{agent_id}_{forecast_horizon_months}M"
        
        # Estimate reputation growth
        monthly_growth_rate = 0.02  # 2% monthly growth assumption
        expected_reputation = current_reputation * (1 + monthly_growth_rate) ** forecast_horizon_months
        expected_reputation = min(1.0, expected_reputation)  # Cap at 1.0
        
        market = PredictionMarket(
            market_id=market_id,
            market_type=MarketType.AGENT_REPUTATION,
            outcome_type=OutcomeType.CONTINUOUS,
            description=f"What will agent {agent_id}'s reputation be in {forecast_horizon_months} months?",
            settlement_date=datetime.now() + timedelta(days=30 * forecast_horizon_months),
            settlement_criteria={
                'reputation_components': ['technical_skill', 'communication', 'reliability', 'innovation'],
                'measurement_method': 'weighted_average',
                'minimum_sample_size': 5
            },
            current_price=expected_reputation,
            volume=0,
            liquidity=10000,
            participants=0
        )
        
        # Initialize AMM
        amm = AutomatedMarketMaker(initial_liquidity=10000)
        
        self.markets[market_id] = market
        self.amm_instances[market_id] = amm
        
        return market
    
    def simulate_market_trading(self, market_id: str, 
                              n_participants: int = 50,
                              n_trades: int = 200) -> Dict[str, any]:
        """Simulate trading activity in prediction market"""
        
        if market_id not in self.markets:
            raise ValueError(f"Market {market_id} not found")
        
        market = self.markets[market_id]
        amm = self.amm_instances[market_id]
        
        # Simulate participants with different information and biases
        participants = []
        for i in range(n_participants):
            # Each participant has different information quality and bias
            information_quality = np.random.beta(2, 2)  # 0-1 scale
            bias = np.random.normal(0, 0.1)  # Bias in probability assessment
            risk_tolerance = np.random.exponential(1)  # Risk tolerance
            
            participants.append({
                'id': f"P_{i:03d}",
                'information_quality': information_quality,
                'bias': bias,
                'risk_tolerance': risk_tolerance,
                'cash': 10000,
                'positions': {}
            })
        
        # Simulate trading
        trade_history = []
        price_history = [amm._get_current_market_price()]
        
        for trade_num in range(n_trades):
            # Select random participant
            participant = np.random.choice(participants)
            
            # Determine participant's belief about true probability
            true_prob = market.current_price  # Assume market price is close to true probability
            noise = np.random.normal(0, 0.1 * (1 - participant['information_quality']))
            perceived_prob = np.clip(true_prob + participant['bias'] + noise, 0.01, 0.99)
            
            # Determine trade direction and size
            current_market_price = amm._get_current_market_price()
            
            if perceived_prob > current_market_price + 0.02:  # Buy threshold
                trade_direction = 'buy'
                outcome = 'yes'
                trade_size = min(1000, participant['cash'] * 0.1 * participant['risk_tolerance'])
            elif perceived_prob < current_market_price - 0.02:  # Sell threshold
                trade_direction = 'sell'
                outcome = 'yes'
                trade_size = min(1000, participant['cash'] * 0.1 * participant['risk_tolerance'])
            else:
                continue  # No trade
            
            # Execute trade
            if trade_size > 100:  # Minimum trade size
                execution_result = amm.execute_trade(outcome, trade_size, trade_direction)
                
                # Update participant cash and positions
                cost = execution_result['total_cost']
                if trade_direction == 'buy' and participant['cash'] >= cost:
                    participant['cash'] -= cost
                    if market_id not in participant['positions']:
                        participant['positions'][market_id] = 0
                    participant['positions'][market_id] += trade_size
                elif trade_direction == 'sell' and participant['positions'].get(market_id, 0) >= trade_size:
                    participant['cash'] += cost
                    participant['positions'][market_id] -= trade_size
                
                # Record trade
                trade_history.append({
                    'trade_num': trade_num,
                    'participant': participant['id'],
                    'outcome': outcome,
                    'direction': trade_direction,
                    'size': trade_size,
                    'price': execution_result['execution_price'],
                    'slippage': execution_result['slippage']
                })
                
                price_history.append(execution_result['new_market_price'])
        
        # Update market statistics
        market.volume = amm.total_volume
        market.participants = len([p for p in participants if market_id in p['positions']])
        market.current_price = amm._get_current_market_price()
        
        return {
            'final_price': market.current_price,
            'total_volume': market.volume,
            'num_trades': len(trade_history),
            'price_volatility': np.std(price_history),
            'average_slippage': np.mean([t['slippage'] for t in trade_history]),
            'price_history': price_history,
            'trade_history': trade_history[-10:],  # Last 10 trades
            'market_efficiency': self._calculate_market_efficiency(price_history, true_prob)
        }
    
    def _calculate_market_efficiency(self, price_history: List[float], 
                                   true_probability: float) -> float:
        """Calculate market efficiency based on price convergence"""
        
        if len(price_history) < 2:
            return 0.0
        
        # Calculate how quickly price converges to true probability
        final_prices = price_history[-10:]  # Last 10 prices
        avg_final_price = np.mean(final_prices)
        
        # Efficiency = 1 - normalized absolute error
        error = abs(avg_final_price - true_probability)
        efficiency = max(0, 1 - error * 2)  # Scale error to 0-1 range
        
        return efficiency
    
    def calculate_prediction_accuracy(self, market_id: str, 
                                    actual_outcome: float) -> Dict[str, float]:
        """Calculate prediction accuracy for settled market"""
        
        if market_id not in self.markets:
            raise ValueError(f"Market {market_id} not found")
        
        market = self.markets[market_id]
        predicted_outcome = market.current_price
        
        # Calculate various accuracy metrics
        absolute_error = abs(predicted_outcome - actual_outcome)
        squared_error = (predicted_outcome - actual_outcome) ** 2
        
        # For binary outcomes
        if market.outcome_type == OutcomeType.BINARY:
            # Brier score (lower is better)
            brier_score = squared_error
            
            # Log score (higher is better)
            epsilon = 1e-15  # Prevent log(0)
            if actual_outcome == 1:
                log_score = np.log(max(predicted_outcome, epsilon))
            else:
                log_score = np.log(max(1 - predicted_outcome, epsilon))
            
            accuracy_metrics = {
                'absolute_error': absolute_error,
                'brier_score': brier_score,
                'log_score': log_score,
                'calibration_error': absolute_error
            }
        else:
            # For continuous outcomes
            relative_error = absolute_error / max(actual_outcome, 0.01)
            
            accuracy_metrics = {
                'absolute_error': absolute_error,
                'relative_error': relative_error,
                'squared_error': squared_error,
                'rmse': np.sqrt(squared_error)
            }
        
        # Update market accuracy history
        market.accuracy_history.append(1 - absolute_error)  # Convert to accuracy (0-1)
        
        # Record settlement
        self.settlement_history.append({
            'market_id': market_id,
            'predicted_outcome': predicted_outcome,
            'actual_outcome': actual_outcome,
            'accuracy_metrics': accuracy_metrics,
            'settlement_date': datetime.now()
        })
        
        return accuracy_metrics

def demonstrate_prediction_markets():
    """Demonstrate the prediction market system"""
    
    print("VibeLaunch Prediction Market System Demonstration")
    print("=" * 60)
    
    # Initialize system
    pm_system = PredictionMarketSystem()
    
    print("\n1. BINARY COMPLETION MARKETS")
    print("-" * 40)
    
    # Create binary completion market
    completion_market = pm_system.create_binary_completion_market(
        contract_id="CONTRACT_001",
        completion_probability=0.85,
        settlement_date=datetime.now() + timedelta(days=30)
    )
    
    print(f"Market Created: {completion_market.description}")
    print(f"Initial Price: {completion_market.current_price:.1%}")
    print(f"Market ID: {completion_market.market_id}")
    
    # Simulate trading
    trading_results = pm_system.simulate_market_trading(
        completion_market.market_id, 
        n_participants=30, 
        n_trades=150
    )
    
    print(f"\nTrading Results:")
    print(f"  Final Price: {trading_results['final_price']:.1%}")
    print(f"  Total Volume: ${trading_results['total_volume']:,.2f}")
    print(f"  Number of Trades: {trading_results['num_trades']}")
    print(f"  Price Volatility: {trading_results['price_volatility']:.1%}")
    print(f"  Average Slippage: {trading_results['average_slippage']:.2%}")
    print(f"  Market Efficiency: {trading_results['market_efficiency']:.1%}")
    
    # Calculate accuracy (simulate actual outcome)
    actual_completion = 1  # Contract completed successfully
    accuracy_metrics = pm_system.calculate_prediction_accuracy(
        completion_market.market_id, actual_completion
    )
    
    print(f"\nPrediction Accuracy:")
    print(f"  Absolute Error: {accuracy_metrics['absolute_error']:.1%}")
    print(f"  Brier Score: {accuracy_metrics['brier_score']:.3f}")
    print(f"  Log Score: {accuracy_metrics['log_score']:.3f}")
    
    print("\n2. QUALITY SCORE MARKETS")
    print("-" * 35)
    
    # Create quality dimensions
    quality_dimensions = [
        QualityDimension("technical_quality", 0.3, "automated_testing", predicted_score=0.88),
        QualityDimension("aesthetic_quality", 0.25, "peer_review", predicted_score=0.82),
        QualityDimension("usability", 0.25, "user_testing", predicted_score=0.85),
        QualityDimension("innovation", 0.2, "expert_panel", predicted_score=0.78)
    ]
    
    # Create quality market
    quality_market = pm_system.create_quality_score_market(
        contract_id="CONTRACT_002",
        quality_dimensions=quality_dimensions,
        settlement_date=datetime.now() + timedelta(days=45)
    )
    
    print(f"Market Created: {quality_market.description}")
    print(f"Expected Quality Score: {quality_market.current_price:.1%}")
    
    print(f"Quality Dimensions:")
    for dim in quality_dimensions:
        print(f"  {dim.dimension_name}: {dim.weight:.0%} weight, predicted {dim.predicted_score:.1%}")
    
    # Simulate trading
    quality_trading = pm_system.simulate_market_trading(
        quality_market.market_id,
        n_participants=25,
        n_trades=100
    )
    
    print(f"\nQuality Market Trading:")
    print(f"  Final Expected Score: {quality_trading['final_price']:.1%}")
    print(f"  Market Efficiency: {quality_trading['market_efficiency']:.1%}")
    
    # Calculate accuracy (simulate actual quality outcome)
    actual_quality = 0.84  # Actual achieved quality
    quality_accuracy = pm_system.calculate_prediction_accuracy(
        quality_market.market_id, actual_quality
    )
    
    print(f"\nQuality Prediction Accuracy:")
    print(f"  Absolute Error: {quality_accuracy['absolute_error']:.1%}")
    print(f"  Relative Error: {quality_accuracy['relative_error']:.1%}")
    
    print("\n3. TIMELINE ACCURACY MARKETS")
    print("-" * 38)
    
    # Create timeline market
    contracted_deadline = datetime.now() + timedelta(days=21)
    expected_completion = datetime.now() + timedelta(days=19)  # 2 days early
    
    timeline_market = pm_system.create_timeline_accuracy_market(
        contract_id="CONTRACT_003",
        expected_completion_date=expected_completion,
        contracted_deadline=contracted_deadline
    )
    
    print(f"Market Created: {timeline_market.description}")
    print(f"On-Time Probability: {timeline_market.current_price:.1%}")
    print(f"Expected Completion: 2 days early")
    
    # Simulate trading
    timeline_trading = pm_system.simulate_market_trading(
        timeline_market.market_id,
        n_participants=20,
        n_trades=80
    )
    
    print(f"\nTimeline Market Trading:")
    print(f"  Final On-Time Probability: {timeline_trading['final_price']:.1%}")
    print(f"  Market Efficiency: {timeline_trading['market_efficiency']:.1%}")
    
    # Calculate accuracy (simulate actual timeline outcome)
    actual_on_time = 1  # Delivered on time
    timeline_accuracy = pm_system.calculate_prediction_accuracy(
        timeline_market.market_id, actual_on_time
    )
    
    print(f"\nTimeline Prediction Accuracy:")
    print(f"  Absolute Error: {timeline_accuracy['absolute_error']:.1%}")
    print(f"  Brier Score: {timeline_accuracy['brier_score']:.3f}")
    
    print("\n4. AGENT REPUTATION MARKETS")
    print("-" * 37)
    
    # Create agent reputation market
    reputation_market = pm_system.create_agent_reputation_market(
        agent_id="AGENT_A001",
        current_reputation=0.78,
        forecast_horizon_months=6
    )
    
    print(f"Market Created: {reputation_market.description}")
    print(f"Current Reputation: 78.0%")
    print(f"Expected 6-Month Reputation: {reputation_market.current_price:.1%}")
    
    # Simulate trading
    reputation_trading = pm_system.simulate_market_trading(
        reputation_market.market_id,
        n_participants=15,
        n_trades=60
    )
    
    print(f"\nReputation Market Trading:")
    print(f"  Final Expected Reputation: {reputation_trading['final_price']:.1%}")
    print(f"  Market Efficiency: {reputation_trading['market_efficiency']:.1%}")
    
    print("\n5. OVERALL SYSTEM PERFORMANCE")
    print("-" * 40)
    
    # Calculate overall system statistics
    total_markets = len(pm_system.markets)
    total_volume = sum(market.volume for market in pm_system.markets.values())
    avg_accuracy = np.mean([
        1 - metrics['absolute_error'] 
        for settlement in pm_system.settlement_history 
        for metrics in [settlement['accuracy_metrics']]
    ])
    
    print(f"Total Markets Created: {total_markets}")
    print(f"Total Trading Volume: ${total_volume:,.2f}")
    print(f"Average Prediction Accuracy: {avg_accuracy:.1%}")
    
    # Market efficiency summary
    all_efficiencies = [
        trading_results['market_efficiency'],
        quality_trading['market_efficiency'],
        timeline_trading['market_efficiency'],
        reputation_trading['market_efficiency']
    ]
    avg_efficiency = np.mean(all_efficiencies)
    
    print(f"Average Market Efficiency: {avg_efficiency:.1%}")
    
    # Building on Agent 3's 94.5% accuracy
    agent3_accuracy = 0.945
    system_improvement = avg_accuracy - agent3_accuracy
    
    print(f"\nBuilding on Agent 3's Performance:")
    print(f"  Agent 3 Baseline Accuracy: {agent3_accuracy:.1%}")
    print(f"  Prediction Market Accuracy: {avg_accuracy:.1%}")
    if system_improvement > 0:
        print(f"  Improvement: +{system_improvement:.1%}")
    else:
        print(f"  Difference: {system_improvement:.1%}")
    
    print("\n" + "=" * 60)
    print("Prediction market system demonstration completed!")
    print("Enabling collective intelligence and superior forecasting accuracy.")

if __name__ == "__main__":
    demonstrate_prediction_markets()

