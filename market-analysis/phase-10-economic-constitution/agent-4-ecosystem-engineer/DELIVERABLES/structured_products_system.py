#!/usr/bin/env python3
"""
VibeLaunch Structured Products Implementation
Agent 4: Financial Innovation Architect

This module implements the comprehensive structured products framework including
Collateralized Task Obligations (CTOs), multi-currency baskets, reputation
yield curves, and dynamic optimization algorithms.

Author: Manus AI
Date: June 14, 2025
Version: 1.0
"""

import numpy as np
import pandas as pd
from scipy.stats import norm, multivariate_normal
from scipy.optimize import minimize, differential_evolution
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import warnings
import json
from datetime import datetime, timedelta
warnings.filterwarnings('ignore')

class TrancheType(Enum):
    """Types of CTO tranches"""
    SENIOR = "senior"
    MEZZANINE = "mezzanine"
    EQUITY = "equity"

class BasketType(Enum):
    """Types of multi-currency baskets"""
    VIBELAUNCH_INDEX = "vibelaunch_index"
    CONTENT_CREATION = "content_creation"
    SEO_ANALYTICS = "seo_analytics"
    DESIGN_UX = "design_ux"
    STRATEGIC_CONSULTING = "strategic_consulting"
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"

@dataclass
class AgentContract:
    """Individual agent contract for CTO pooling"""
    contract_id: str
    agent_id: str
    service_category: str
    contract_value: float
    completion_probability: float
    quality_score: float
    reputation_score: float
    time_to_completion: float
    complexity_rating: float
    currency_exposures: Dict[str, float]

@dataclass
class CTOTranche:
    """CTO tranche structure"""
    tranche_type: TrancheType
    principal_amount: float
    coupon_rate: float
    credit_rating: str
    subordination_level: int
    loss_allocation: float
    payment_priority: int

@dataclass
class BasketComposition:
    """Multi-currency basket composition"""
    basket_type: BasketType
    currency_weights: Dict[str, float]
    rebalancing_frequency: str
    volatility_target: Optional[float]
    risk_budget: Dict[str, float]

class CollateralizedTaskObligations:
    """CTO creation and management system"""
    
    def __init__(self):
        self.pools = {}
        self.tranches = {}
        self.rating_criteria = self._initialize_rating_criteria()
        
    def _initialize_rating_criteria(self) -> Dict[str, Dict[str, float]]:
        """Initialize credit rating criteria for tranches"""
        return {
            'AAA': {'min_subordination': 0.15, 'max_loss_rate': 0.02, 'min_overcollat': 1.20},
            'AA': {'min_subordination': 0.12, 'max_loss_rate': 0.04, 'min_overcollat': 1.15},
            'A': {'min_subordination': 0.08, 'max_loss_rate': 0.08, 'min_overcollat': 1.10},
            'BBB': {'min_subordination': 0.05, 'max_loss_rate': 0.15, 'min_overcollat': 1.05},
            'BB': {'min_subordination': 0.02, 'max_loss_rate': 0.25, 'min_overcollat': 1.02}
        }
    
    def create_contract_pool(self, contracts: List[AgentContract], 
                           pool_id: str) -> Dict[str, any]:
        """Create diversified pool of agent contracts"""
        
        # Calculate pool statistics
        total_value = sum(contract.contract_value for contract in contracts)
        avg_completion_prob = np.mean([c.completion_probability for c in contracts])
        avg_quality_score = np.mean([c.quality_score for c in contracts])
        avg_reputation = np.mean([c.reputation_score for c in contracts])
        
        # Diversification metrics
        service_categories = set(c.service_category for c in contracts)
        agent_count = len(set(c.agent_id for c in contracts))
        
        # Risk assessment
        pool_risk = self._assess_pool_risk(contracts)
        
        pool_info = {
            'pool_id': pool_id,
            'contracts': contracts,
            'total_value': total_value,
            'contract_count': len(contracts),
            'avg_completion_prob': avg_completion_prob,
            'avg_quality_score': avg_quality_score,
            'avg_reputation': avg_reputation,
            'service_categories': len(service_categories),
            'unique_agents': agent_count,
            'diversification_score': self._calculate_diversification_score(contracts),
            'risk_metrics': pool_risk
        }
        
        self.pools[pool_id] = pool_info
        return pool_info
    
    def _assess_pool_risk(self, contracts: List[AgentContract]) -> Dict[str, float]:
        """Assess comprehensive risk metrics for contract pool"""
        
        # Default probability estimation
        completion_probs = [c.completion_probability for c in contracts]
        default_probs = [1 - prob for prob in completion_probs]
        
        # Expected loss calculation
        contract_values = [c.contract_value for c in contracts]
        expected_losses = [prob * value * 0.5 for prob, value in zip(default_probs, contract_values)]
        total_expected_loss = sum(expected_losses)
        total_value = sum(contract_values)
        expected_loss_rate = total_expected_loss / total_value
        
        # Concentration risk
        max_contract_size = max(contract_values)
        concentration_risk = max_contract_size / total_value
        
        # Quality risk
        quality_scores = [c.quality_score for c in contracts]
        quality_volatility = np.std(quality_scores)
        
        # Correlation risk (simplified)
        service_categories = [c.service_category for c in contracts]
        category_concentration = max(service_categories.count(cat) for cat in set(service_categories)) / len(contracts)
        
        return {
            'expected_loss_rate': expected_loss_rate,
            'concentration_risk': concentration_risk,
            'quality_volatility': quality_volatility,
            'category_concentration': category_concentration,
            'avg_default_prob': np.mean(default_probs)
        }
    
    def _calculate_diversification_score(self, contracts: List[AgentContract]) -> float:
        """Calculate diversification score for contract pool"""
        
        # Service category diversification
        categories = [c.service_category for c in contracts]
        category_weights = {}
        for cat in categories:
            category_weights[cat] = categories.count(cat) / len(categories)
        
        category_hhi = sum(weight**2 for weight in category_weights.values())
        category_diversification = 1 - category_hhi
        
        # Agent diversification
        agents = [c.agent_id for c in contracts]
        agent_weights = {}
        for agent in agents:
            agent_weights[agent] = agents.count(agent) / len(agents)
        
        agent_hhi = sum(weight**2 for weight in agent_weights.values())
        agent_diversification = 1 - agent_hhi
        
        # Size diversification
        values = [c.contract_value for c in contracts]
        total_value = sum(values)
        value_weights = [v / total_value for v in values]
        value_hhi = sum(weight**2 for weight in value_weights)
        value_diversification = 1 - value_hhi
        
        # Combined diversification score
        diversification_score = (category_diversification * 0.4 + 
                               agent_diversification * 0.3 + 
                               value_diversification * 0.3)
        
        return diversification_score
    
    def structure_tranches(self, pool_id: str, 
                          target_ratings: List[str] = ['AAA', 'BBB', 'Unrated']) -> List[CTOTranche]:
        """Structure CTO tranches based on pool characteristics"""
        
        pool_info = self.pools[pool_id]
        total_value = pool_info['total_value']
        risk_metrics = pool_info['risk_metrics']
        
        tranches = []
        
        # Senior tranche (AAA)
        if 'AAA' in target_ratings:
            senior_size = total_value * 0.80  # 80% senior
            senior_coupon = 0.04 + risk_metrics['expected_loss_rate'] * 0.5  # Base + risk premium
            
            senior_tranche = CTOTranche(
                tranche_type=TrancheType.SENIOR,
                principal_amount=senior_size,
                coupon_rate=senior_coupon,
                credit_rating='AAA',
                subordination_level=0,
                loss_allocation=0.0,  # Protected by subordination
                payment_priority=1
            )
            tranches.append(senior_tranche)
        
        # Mezzanine tranche (BBB)
        if 'BBB' in target_ratings:
            mezz_size = total_value * 0.15  # 15% mezzanine
            mezz_coupon = 0.08 + risk_metrics['expected_loss_rate'] * 1.5  # Higher risk premium
            
            mezz_tranche = CTOTranche(
                tranche_type=TrancheType.MEZZANINE,
                principal_amount=mezz_size,
                coupon_rate=mezz_coupon,
                credit_rating='BBB',
                subordination_level=1,
                loss_allocation=0.75,  # Absorbs losses after equity
                payment_priority=2
            )
            tranches.append(mezz_tranche)
        
        # Equity tranche (Unrated)
        if 'Unrated' in target_ratings:
            equity_size = total_value * 0.05  # 5% equity
            equity_coupon = 0.15  # High base return expectation
            
            equity_tranche = CTOTranche(
                tranche_type=TrancheType.EQUITY,
                principal_amount=equity_size,
                coupon_rate=equity_coupon,
                credit_rating='Unrated',
                subordination_level=2,
                loss_allocation=1.0,  # First loss position
                payment_priority=3
            )
            tranches.append(equity_tranche)
        
        self.tranches[pool_id] = tranches
        return tranches
    
    def simulate_cto_performance(self, pool_id: str, 
                                n_simulations: int = 10000) -> Dict[str, any]:
        """Simulate CTO performance under various scenarios"""
        
        pool_info = self.pools[pool_id]
        contracts = pool_info['contracts']
        tranches = self.tranches[pool_id]
        
        # Simulation parameters
        total_value = pool_info['total_value']
        
        # Simulate contract outcomes
        outcomes = []
        for _ in range(n_simulations):
            scenario_outcome = self._simulate_scenario(contracts)
            outcomes.append(scenario_outcome)
        
        # Calculate tranche performance
        tranche_results = {}
        for tranche in tranches:
            tranche_payments = []
            for outcome in outcomes:
                payment = self._calculate_tranche_payment(tranche, outcome, total_value)
                tranche_payments.append(payment)
            
            tranche_results[tranche.tranche_type.value] = {
                'expected_payment': np.mean(tranche_payments),
                'payment_volatility': np.std(tranche_payments),
                'default_probability': np.mean([p < tranche.principal_amount * 0.95 for p in tranche_payments]),
                'recovery_rate': np.mean([p / tranche.principal_amount for p in tranche_payments]),
                'var_95': np.percentile(tranche_payments, 5),
                'var_99': np.percentile(tranche_payments, 1)
            }
        
        return {
            'pool_performance': {
                'expected_recovery': np.mean([o['total_recovery'] for o in outcomes]),
                'recovery_volatility': np.std([o['total_recovery'] for o in outcomes]),
                'default_rate': np.mean([o['default_rate'] for o in outcomes])
            },
            'tranche_results': tranche_results
        }
    
    def _simulate_scenario(self, contracts: List[AgentContract]) -> Dict[str, float]:
        """Simulate single scenario outcome for contract pool"""
        
        total_value = sum(c.contract_value for c in contracts)
        total_recovery = 0
        defaults = 0
        
        for contract in contracts:
            # Simulate contract completion
            completion_success = np.random.random() < contract.completion_probability
            
            if completion_success:
                # Quality adjustment
                quality_multiplier = np.random.normal(contract.quality_score, 0.1)
                quality_multiplier = max(0.5, min(1.5, quality_multiplier))  # Bounded
                
                recovery = contract.contract_value * quality_multiplier
            else:
                # Default scenario - partial recovery
                recovery_rate = np.random.uniform(0.2, 0.6)  # 20-60% recovery
                recovery = contract.contract_value * recovery_rate
                defaults += 1
            
            total_recovery += recovery
        
        return {
            'total_recovery': total_recovery,
            'recovery_rate': total_recovery / total_value,
            'default_rate': defaults / len(contracts),
            'defaults': defaults
        }
    
    def _calculate_tranche_payment(self, tranche: CTOTranche, 
                                 outcome: Dict[str, float], 
                                 total_value: float) -> float:
        """Calculate payment to specific tranche based on scenario outcome"""
        
        total_recovery = outcome['total_recovery']
        
        # Waterfall payment logic
        if tranche.tranche_type == TrancheType.SENIOR:
            # Senior gets paid first, up to principal + interest
            max_payment = tranche.principal_amount * (1 + tranche.coupon_rate)
            payment = min(total_recovery, max_payment)
            
        elif tranche.tranche_type == TrancheType.MEZZANINE:
            # Mezzanine gets paid after senior
            senior_payment = tranche.principal_amount * 0.8 / 0.15 * (1 + 0.04)  # Approximate senior payment
            remaining_after_senior = max(0, total_recovery - senior_payment)
            max_payment = tranche.principal_amount * (1 + tranche.coupon_rate)
            payment = min(remaining_after_senior, max_payment)
            
        else:  # Equity
            # Equity gets residual after senior and mezzanine
            senior_payment = tranche.principal_amount * 0.8 / 0.05 * (1 + 0.04)  # Approximate
            mezz_payment = tranche.principal_amount * 0.15 / 0.05 * (1 + 0.08)  # Approximate
            payment = max(0, total_recovery - senior_payment - mezz_payment)
        
        return max(0, payment)

class MultiCurrencyBaskets:
    """Multi-currency basket products and index construction"""
    
    def __init__(self):
        self.baskets = {}
        self.index_data = {}
        self.rebalancing_history = {}
        
    def create_vibelaunch_index(self) -> BasketComposition:
        """Create flagship VibeLaunch Index"""
        
        composition = BasketComposition(
            basket_type=BasketType.VIBELAUNCH_INDEX,
            currency_weights={
                '₥': 0.30,  # Economic - stability and liquidity
                '◈': 0.25,  # Quality - value enhancement
                '⧗': 0.20,  # Temporal - efficiency gains
                '☆': 0.15,  # Reliability - trust benefits
                '◊': 0.10   # Innovation - breakthrough potential
            },
            rebalancing_frequency='monthly',
            volatility_target=0.20,  # 20% annual volatility target
            risk_budget={
                '₥': 0.25,
                '◈': 0.25,
                '⧗': 0.20,
                '☆': 0.15,
                '◊': 0.15
            }
        )
        
        self.baskets['vibelaunch_index'] = composition
        return composition
    
    def create_sector_basket(self, sector: str) -> BasketComposition:
        """Create sector-specific basket"""
        
        sector_weights = {
            'content_creation': {
                '₥': 0.25, '◈': 0.35, '⧗': 0.15, '☆': 0.15, '◊': 0.10
            },
            'seo_analytics': {
                '₥': 0.30, '◈': 0.20, '⧗': 0.25, '☆': 0.20, '◊': 0.05
            },
            'design_ux': {
                '₥': 0.20, '◈': 0.40, '⧗': 0.10, '☆': 0.15, '◊': 0.15
            },
            'strategic_consulting': {
                '₥': 0.25, '◈': 0.30, '⧗': 0.15, '☆': 0.25, '◊': 0.05
            }
        }
        
        if sector not in sector_weights:
            raise ValueError(f"Unknown sector: {sector}")
        
        composition = BasketComposition(
            basket_type=BasketType(sector),
            currency_weights=sector_weights[sector],
            rebalancing_frequency='monthly',
            volatility_target=0.25,
            risk_budget=sector_weights[sector]
        )
        
        self.baskets[sector] = composition
        return composition
    
    def create_risk_profile_basket(self, risk_profile: str) -> BasketComposition:
        """Create risk-adjusted basket"""
        
        profile_weights = {
            'conservative': {
                '₥': 0.50, '◈': 0.15, '⧗': 0.10, '☆': 0.20, '◊': 0.05
            },
            'moderate': {
                '₥': 0.30, '◈': 0.25, '⧗': 0.20, '☆': 0.15, '◊': 0.10
            },
            'aggressive': {
                '₥': 0.20, '◈': 0.30, '⧗': 0.15, '☆': 0.15, '◊': 0.20
            }
        }
        
        volatility_targets = {
            'conservative': 0.12,
            'moderate': 0.20,
            'aggressive': 0.35
        }
        
        if risk_profile not in profile_weights:
            raise ValueError(f"Unknown risk profile: {risk_profile}")
        
        composition = BasketComposition(
            basket_type=BasketType(risk_profile),
            currency_weights=profile_weights[risk_profile],
            rebalancing_frequency='monthly',
            volatility_target=volatility_targets[risk_profile],
            risk_budget=profile_weights[risk_profile]
        )
        
        self.baskets[risk_profile] = composition
        return composition
    
    def calculate_basket_performance(self, basket_id: str, 
                                   currency_returns: Dict[str, np.ndarray],
                                   correlation_matrix: np.ndarray) -> Dict[str, float]:
        """Calculate basket performance metrics"""
        
        composition = self.baskets[basket_id]
        weights = np.array(list(composition.currency_weights.values()))
        currencies = list(composition.currency_weights.keys())
        
        # Portfolio returns
        returns_matrix = np.column_stack([currency_returns[curr] for curr in currencies])
        portfolio_returns = np.dot(returns_matrix, weights)
        
        # Performance metrics
        annual_return = np.mean(portfolio_returns) * 252  # Annualized
        annual_volatility = np.std(portfolio_returns) * np.sqrt(252)
        sharpe_ratio = annual_return / annual_volatility if annual_volatility > 0 else 0
        
        # Risk metrics
        var_95 = np.percentile(portfolio_returns, 5)
        var_99 = np.percentile(portfolio_returns, 1)
        max_drawdown = self._calculate_max_drawdown(portfolio_returns)
        
        # Diversification metrics
        portfolio_variance = np.dot(weights, np.dot(correlation_matrix, weights))
        diversification_ratio = np.sum(weights * np.sqrt(np.diag(correlation_matrix))) / np.sqrt(portfolio_variance)
        
        return {
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'sharpe_ratio': sharpe_ratio,
            'var_95': var_95,
            'var_99': var_99,
            'max_drawdown': max_drawdown,
            'diversification_ratio': diversification_ratio
        }
    
    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """Calculate maximum drawdown"""
        cumulative = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return np.min(drawdown)
    
    def optimize_basket_weights(self, expected_returns: Dict[str, float],
                              correlation_matrix: np.ndarray,
                              volatilities: Dict[str, float],
                              risk_aversion: float = 1.0) -> Dict[str, float]:
        """Optimize basket weights using mean-variance optimization"""
        
        currencies = list(expected_returns.keys())
        n_assets = len(currencies)
        
        # Convert to arrays
        mu = np.array([expected_returns[curr] for curr in currencies])
        sigma = np.array([volatilities[curr] for curr in currencies])
        
        # Covariance matrix
        cov_matrix = correlation_matrix * np.outer(sigma, sigma)
        
        # Objective function (negative utility)
        def objective(weights):
            portfolio_return = np.dot(weights, mu)
            portfolio_variance = np.dot(weights, np.dot(cov_matrix, weights))
            utility = portfolio_return - 0.5 * risk_aversion * portfolio_variance
            return -utility  # Minimize negative utility
        
        # Constraints
        constraints = [
            {'type': 'eq', 'fun': lambda w: np.sum(w) - 1},  # Weights sum to 1
        ]
        
        # Bounds (no short selling, max 50% in any asset)
        bounds = [(0.05, 0.50) for _ in range(n_assets)]
        
        # Initial guess (equal weights)
        x0 = np.ones(n_assets) / n_assets
        
        # Optimize
        result = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=constraints)
        
        if result.success:
            optimal_weights = {curr: weight for curr, weight in zip(currencies, result.x)}
            return optimal_weights
        else:
            # Fallback to equal weights
            return {curr: 1/n_assets for curr in currencies}

class ReputationYieldCurve:
    """Reputation yield curve construction and bond pricing"""
    
    def __init__(self):
        self.yield_curve = {}
        self.bonds = {}
        
    def construct_yield_curve(self, market_data: Dict[str, float]) -> Dict[str, float]:
        """Construct reputation yield curve from market data"""
        
        # Base yield curve points (in years)
        maturities = [0.25, 0.5, 1.0, 2.0, 5.0]
        
        # Base yields (starting points)
        base_yields = {
            0.25: 0.05,  # 3-month: 5%
            0.5: 0.06,   # 6-month: 6%
            1.0: 0.08,   # 1-year: 8%
            2.0: 0.11,   # 2-year: 11%
            5.0: 0.14    # 5-year: 14%
        }
        
        # Market adjustments
        market_stress = market_data.get('market_stress', 0.0)
        reputation_demand = market_data.get('reputation_demand', 1.0)
        competition_level = market_data.get('competition_level', 0.5)
        
        # Adjust yields based on market conditions
        adjusted_yields = {}
        for maturity, base_yield in base_yields.items():
            # Stress adjustment
            stress_adjustment = market_stress * 0.02 * maturity  # Higher impact on longer maturities
            
            # Demand adjustment
            demand_adjustment = (reputation_demand - 1.0) * 0.01 * maturity
            
            # Competition adjustment
            competition_adjustment = (0.5 - competition_level) * 0.015 * maturity
            
            adjusted_yield = base_yield + stress_adjustment + demand_adjustment + competition_adjustment
            adjusted_yields[maturity] = max(0.01, adjusted_yield)  # Minimum 1% yield
        
        self.yield_curve = adjusted_yields
        return adjusted_yields
    
    def price_reputation_bond(self, face_value: float, 
                            coupon_rate: float,
                            maturity: float,
                            bond_type: str = 'fixed') -> Dict[str, float]:
        """Price reputation bond using yield curve"""
        
        if not self.yield_curve:
            raise ValueError("Yield curve not constructed")
        
        # Interpolate yield for exact maturity
        yield_rate = self._interpolate_yield(maturity)
        
        if bond_type == 'fixed':
            # Fixed-rate bond pricing
            periods = int(maturity * 4)  # Quarterly payments
            quarterly_coupon = face_value * coupon_rate / 4
            quarterly_yield = yield_rate / 4
            
            # Present value of coupons
            coupon_pv = 0
            for t in range(1, periods + 1):
                coupon_pv += quarterly_coupon / (1 + quarterly_yield) ** t
            
            # Present value of principal
            principal_pv = face_value / (1 + quarterly_yield) ** periods
            
            bond_price = coupon_pv + principal_pv
            
        elif bond_type == 'floating':
            # Floating-rate bond (priced at par with spread)
            spread = 0.01  # 100 bps spread
            bond_price = face_value  # Floating rate bonds trade near par
            effective_yield = yield_rate + spread
            
        else:
            raise ValueError(f"Unknown bond type: {bond_type}")
        
        # Calculate yield to maturity
        ytm = self._calculate_ytm(bond_price, face_value, coupon_rate, maturity)
        
        # Duration calculation
        duration = self._calculate_duration(bond_price, face_value, coupon_rate, maturity, ytm)
        
        return {
            'bond_price': bond_price,
            'yield_to_maturity': ytm,
            'duration': duration,
            'convexity': duration ** 2,  # Simplified convexity
            'current_yield': (face_value * coupon_rate) / bond_price,
            'price_yield_ratio': bond_price / face_value
        }
    
    def _interpolate_yield(self, maturity: float) -> float:
        """Interpolate yield for specific maturity"""
        
        maturities = sorted(self.yield_curve.keys())
        yields = [self.yield_curve[m] for m in maturities]
        
        if maturity <= maturities[0]:
            return yields[0]
        elif maturity >= maturities[-1]:
            return yields[-1]
        else:
            # Linear interpolation
            for i in range(len(maturities) - 1):
                if maturities[i] <= maturity <= maturities[i + 1]:
                    weight = (maturity - maturities[i]) / (maturities[i + 1] - maturities[i])
                    return yields[i] * (1 - weight) + yields[i + 1] * weight
        
        return yields[-1]  # Fallback
    
    def _calculate_ytm(self, price: float, face_value: float, 
                      coupon_rate: float, maturity: float) -> float:
        """Calculate yield to maturity using approximation"""
        
        annual_coupon = face_value * coupon_rate
        
        # Approximation formula
        ytm = (annual_coupon + (face_value - price) / maturity) / ((face_value + price) / 2)
        
        return ytm
    
    def _calculate_duration(self, price: float, face_value: float,
                          coupon_rate: float, maturity: float, ytm: float) -> float:
        """Calculate modified duration"""
        
        periods = int(maturity * 4)  # Quarterly
        quarterly_coupon = face_value * coupon_rate / 4
        quarterly_ytm = ytm / 4
        
        weighted_time = 0
        total_pv = 0
        
        for t in range(1, periods + 1):
            if t < periods:
                cash_flow = quarterly_coupon
            else:
                cash_flow = quarterly_coupon + face_value
            
            pv = cash_flow / (1 + quarterly_ytm) ** t
            weighted_time += (t / 4) * pv  # Convert to years
            total_pv += pv
        
        macaulay_duration = weighted_time / total_pv
        modified_duration = macaulay_duration / (1 + ytm)
        
        return modified_duration

def demonstrate_structured_products():
    """Demonstrate the structured products system"""
    
    print("VibeLaunch Structured Products Demonstration")
    print("=" * 60)
    
    # Initialize systems
    cto_system = CollateralizedTaskObligations()
    basket_system = MultiCurrencyBaskets()
    yield_curve_system = ReputationYieldCurve()
    
    print("\n1. COLLATERALIZED TASK OBLIGATIONS (CTOs)")
    print("-" * 50)
    
    # Create sample contracts
    contracts = [
        AgentContract("C001", "A001", "content_creation", 5000, 0.9, 0.85, 0.8, 30, 0.6, 
                     {'₥': 0.4, '◈': 0.4, '⧗': 0.1, '☆': 0.05, '◊': 0.05}),
        AgentContract("C002", "A002", "seo_analytics", 3000, 0.95, 0.9, 0.85, 14, 0.4,
                     {'₥': 0.3, '◈': 0.2, '⧗': 0.3, '☆': 0.15, '◊': 0.05}),
        AgentContract("C003", "A003", "design_ux", 8000, 0.85, 0.95, 0.9, 45, 0.8,
                     {'₥': 0.2, '◈': 0.5, '⧗': 0.1, '☆': 0.1, '◊': 0.1}),
        AgentContract("C004", "A004", "strategic_consulting", 12000, 0.8, 0.8, 0.95, 60, 0.9,
                     {'₥': 0.3, '◈': 0.3, '⧗': 0.1, '☆': 0.25, '◊': 0.05}),
        AgentContract("C005", "A005", "content_creation", 4000, 0.92, 0.88, 0.82, 21, 0.5,
                     {'₥': 0.35, '◈': 0.45, '⧗': 0.1, '☆': 0.05, '◊': 0.05})
    ]
    
    # Create contract pool
    pool_info = cto_system.create_contract_pool(contracts, "POOL_001")
    print(f"Contract Pool Created:")
    print(f"  Total Value: ${pool_info['total_value']:,.2f}")
    print(f"  Contract Count: {pool_info['contract_count']}")
    print(f"  Diversification Score: {pool_info['diversification_score']:.2f}")
    print(f"  Expected Loss Rate: {pool_info['risk_metrics']['expected_loss_rate']:.1%}")
    
    # Structure tranches
    tranches = cto_system.structure_tranches("POOL_001")
    print(f"\nTranche Structure:")
    for tranche in tranches:
        print(f"  {tranche.tranche_type.value.title()}: ${tranche.principal_amount:,.2f} @ {tranche.coupon_rate:.1%}")
    
    # Simulate performance
    performance = cto_system.simulate_cto_performance("POOL_001")
    print(f"\nSimulated Performance:")
    print(f"  Pool Recovery Rate: {performance['pool_performance']['expected_recovery'] / pool_info['total_value']:.1%}")
    
    for tranche_type, results in performance['tranche_results'].items():
        print(f"  {tranche_type.title()} Tranche:")
        print(f"    Recovery Rate: {results['recovery_rate']:.1%}")
        print(f"    Default Probability: {results['default_probability']:.1%}")
    
    print("\n2. MULTI-CURRENCY BASKETS")
    print("-" * 40)
    
    # Create VibeLaunch Index
    vibelaunch_index = basket_system.create_vibelaunch_index()
    print(f"VibeLaunch Index Composition:")
    for currency, weight in vibelaunch_index.currency_weights.items():
        print(f"  {currency}: {weight:.1%}")
    
    # Create sector baskets
    content_basket = basket_system.create_sector_basket('content_creation')
    print(f"\nContent Creation Index:")
    for currency, weight in content_basket.currency_weights.items():
        print(f"  {currency}: {weight:.1%}")
    
    # Create risk profile baskets
    conservative_basket = basket_system.create_risk_profile_basket('conservative')
    aggressive_basket = basket_system.create_risk_profile_basket('aggressive')
    
    print(f"\nConservative Basket (Vol Target: {conservative_basket.volatility_target:.1%}):")
    for currency, weight in conservative_basket.currency_weights.items():
        print(f"  {currency}: {weight:.1%}")
    
    print(f"\nAggressive Basket (Vol Target: {aggressive_basket.volatility_target:.1%}):")
    for currency, weight in aggressive_basket.currency_weights.items():
        print(f"  {currency}: {weight:.1%}")
    
    # Simulate basket performance
    np.random.seed(42)
    currency_returns = {
        '₥': np.random.normal(0.0008, 0.02, 252),  # 20% annual vol
        '◈': np.random.normal(0.001, 0.035, 252),   # 35% annual vol
        '⧗': np.random.normal(0.0005, 0.04, 252),   # 40% annual vol
        '☆': np.random.normal(0.0006, 0.025, 252),  # 25% annual vol
        '◊': np.random.normal(0.0012, 0.06, 252)    # 60% annual vol
    }
    
    correlation_matrix = np.array([
        [1.00, 0.30, -0.20, 0.40, 0.10],
        [0.30, 1.00, -0.40, 0.60, 0.50],
        [-0.20, -0.40, 1.00, 0.20, -0.10],
        [0.40, 0.60, 0.20, 1.00, 0.30],
        [0.10, 0.50, -0.10, 0.30, 1.00]
    ])
    
    vibelaunch_perf = basket_system.calculate_basket_performance(
        'vibelaunch_index', currency_returns, correlation_matrix
    )
    
    print(f"\nVibeLaunch Index Performance:")
    print(f"  Annual Return: {vibelaunch_perf['annual_return']:.1%}")
    print(f"  Annual Volatility: {vibelaunch_perf['annual_volatility']:.1%}")
    print(f"  Sharpe Ratio: {vibelaunch_perf['sharpe_ratio']:.2f}")
    print(f"  Diversification Ratio: {vibelaunch_perf['diversification_ratio']:.2f}")
    
    print("\n3. REPUTATION YIELD CURVE")
    print("-" * 35)
    
    # Construct yield curve
    market_data = {
        'market_stress': 0.2,
        'reputation_demand': 1.1,
        'competition_level': 0.6
    }
    
    yield_curve = yield_curve_system.construct_yield_curve(market_data)
    print(f"Reputation Yield Curve:")
    for maturity, yield_rate in yield_curve.items():
        print(f"  {maturity} year: {yield_rate:.1%}")
    
    # Price reputation bonds
    fixed_bond = yield_curve_system.price_reputation_bond(
        face_value=10000,
        coupon_rate=0.08,
        maturity=2.0,
        bond_type='fixed'
    )
    
    print(f"\nFixed-Rate Reputation Bond (2-year, 8% coupon):")
    print(f"  Bond Price: ${fixed_bond['bond_price']:.2f}")
    print(f"  Yield to Maturity: {fixed_bond['yield_to_maturity']:.1%}")
    print(f"  Duration: {fixed_bond['duration']:.2f} years")
    print(f"  Current Yield: {fixed_bond['current_yield']:.1%}")
    
    floating_bond = yield_curve_system.price_reputation_bond(
        face_value=10000,
        coupon_rate=0.06,
        maturity=2.0,
        bond_type='floating'
    )
    
    print(f"\nFloating-Rate Reputation Bond (2-year):")
    print(f"  Bond Price: ${floating_bond['bond_price']:.2f}")
    print(f"  Yield to Maturity: {floating_bond['yield_to_maturity']:.1%}")
    
    print("\n4. PORTFOLIO OPTIMIZATION")
    print("-" * 30)
    
    # Optimize basket weights
    expected_returns = {'₥': 0.08, '◈': 0.12, '⧗': 0.06, '☆': 0.09, '◊': 0.15}
    volatilities = {'₥': 0.20, '◈': 0.35, '⧗': 0.40, '☆': 0.25, '◊': 0.60}
    
    optimal_weights = basket_system.optimize_basket_weights(
        expected_returns, correlation_matrix, volatilities, risk_aversion=2.0
    )
    
    print(f"Optimized Portfolio Weights (Risk Aversion = 2.0):")
    for currency, weight in optimal_weights.items():
        print(f"  {currency}: {weight:.1%}")
    
    print("\n" + "=" * 60)
    print("Structured products demonstration completed!")
    print("Enabling sophisticated investment strategies and capital efficiency.")

if __name__ == "__main__":
    demonstrate_structured_products()

