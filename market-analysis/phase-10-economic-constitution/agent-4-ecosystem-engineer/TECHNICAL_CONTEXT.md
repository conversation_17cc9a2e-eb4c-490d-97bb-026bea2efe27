# Technical Context for Ecosystem Engineer

## Current VibeLaunch Agent Ecosystem

VibeLaunch has a basic multi-agent system with hardcoded agent types and limited interaction capabilities. Your challenge is to design a thriving ecosystem within these constraints.

## Existing Agent Infrastructure

### Current Agent Registry

```sql
-- Minimal agent tracking
agent_registry (
  agent_role TEXT PRIMARY KEY,      -- 'content_creator_pro', 'seo_specialist'
  status agent_status DEFAULT 'idle', -- ENUM: 'idle', 'running', 'error'
  current_load INTEGER,             -- Number of active tasks
  last_heartbeat TIMESTAMPTZ,       -- Health monitoring
  updated_at TIMESTAMPTZ DEFAULT now()
)

-- No capability discovery
-- No performance tracking
-- No specialization details
-- No collaboration mechanisms
```

### Hardcoded Agent Types

```typescript
// From master-agent.ts
const AGENT_TYPES = [
  'content_creator_pro',
  'seo_specialist', 
  'social_media_manager',
  'data_analyst_pro',
  'visual_designer',
  'email_marketer',
  'creative_director'
];

// No dynamic registration
// No custom agents
// No agent marketplace
```

### Agent Communication

```typescript
// Current: Master Agent as central coordinator
MasterAgent -> Sequential Thinking -> Tool Calls -> Specialized Agents

// Limited patterns:
// - No direct agent-to-agent communication
// - No collaborative protocols
// - No knowledge sharing
// - No collective learning
```

## Technical Opportunities for Ecosystem Design

### 1. Agent Capability Framework

```sql
-- Extend agent registry with capabilities
CREATE TABLE agent_capabilities (
  id UUID PRIMARY KEY,
  agent_role TEXT NOT NULL,
  capability_type TEXT NOT NULL,    -- 'core', 'learned', 'specialized'
  capability_name TEXT NOT NULL,
  capability_spec JSONB NOT NULL,
  /* Example spec:
  {
    "domain": "content_creation",
    "sub_skills": ["blog_writing", "copywriting", "technical_docs"],
    "quality_metrics": {
      "accuracy": 0.95,
      "creativity": 0.8,
      "speed": "2000_words_per_hour"
    },
    "prerequisites": ["language_understanding", "domain_knowledge"],
    "composable": true,
    "version": "1.2.0"
  }
  */
  performance_history JSONB,
  created_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(agent_role, capability_name)
);

-- Capability matching index
CREATE INDEX idx_capability_search ON agent_capabilities 
  USING gin(capability_spec);
```

### 2. Agent Performance & Reputation

```sql
-- Implement missing performance tracking
CREATE TABLE agent_performance (
  id UUID PRIMARY KEY,
  agent_role TEXT NOT NULL,
  contract_id UUID REFERENCES contracts(id),
  metrics JSONB NOT NULL,
  /* Example metrics:
  {
    "execution_time_ms": 45000,
    "quality_score": 0.92,
    "client_rating": 4.8,
    "deliverables": {
      "promised": ["blog_post", "meta_description"],
      "delivered": ["blog_post", "meta_description", "keywords"]
    },
    "resource_usage": {
      "tokens": 15000,
      "api_calls": 23
    }
  }
  */
  outcome TEXT CHECK (outcome IN ('success', 'partial', 'failure')),
  client_feedback TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Reputation aggregation view
CREATE VIEW agent_reputation AS
SELECT 
  agent_role,
  COUNT(*) as total_contracts,
  AVG((metrics->>'quality_score')::DECIMAL) as avg_quality,
  AVG((metrics->>'client_rating')::DECIMAL) as avg_rating,
  SUM(CASE WHEN outcome = 'success' THEN 1 ELSE 0 END)::FLOAT / 
    COUNT(*) as success_rate,
  PERCENTILE_CONT(0.5) WITHIN GROUP (
    ORDER BY (metrics->>'execution_time_ms')::INTEGER
  ) as median_execution_time
FROM agent_performance
WHERE created_at > now() - interval '90 days'
GROUP BY agent_role;
```

### 3. Collaborative Protocols

```sql
-- Team formation and coordination
CREATE TABLE agent_teams (
  id UUID PRIMARY KEY,
  team_name TEXT NOT NULL,
  contract_id UUID REFERENCES contracts(id),
  lead_agent TEXT NOT NULL,
  team_composition JSONB NOT NULL,
  /* Example composition:
  {
    "roles": {
      "lead": "creative_director",
      "members": [
        {"role": "content_creator_pro", "responsibility": "writing"},
        {"role": "visual_designer", "responsibility": "graphics"},
        {"role": "seo_specialist", "responsibility": "optimization"}
      ]
    },
    "coordination": {
      "communication_protocol": "event_based",
      "decision_making": "lead_with_consensus",
      "conflict_resolution": "voting"
    }
  }
  */
  formation_strategy TEXT,  -- 'capability_match', 'auction', 'reputation'
  status TEXT DEFAULT 'forming',
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Inter-agent communication
CREATE TABLE agent_messages (
  id UUID PRIMARY KEY,
  from_agent TEXT NOT NULL,
  to_agent TEXT,           -- NULL for broadcast
  team_id UUID REFERENCES agent_teams(id),
  message_type TEXT NOT NULL,  -- 'request', 'response', 'broadcast'
  protocol TEXT NOT NULL,      -- 'collaboration', 'negotiation', 'knowledge'
  payload JSONB NOT NULL,
  correlation_id UUID,         -- For request/response pairing
  created_at TIMESTAMPTZ DEFAULT now()
);
```

### 4. Knowledge Sharing Infrastructure

```sql
-- Shared knowledge base
CREATE TABLE agent_knowledge (
  id UUID PRIMARY KEY,
  contributor_agent TEXT NOT NULL,
  knowledge_type TEXT NOT NULL,  -- 'pattern', 'solution', 'failure', 'insight'
  domain TEXT NOT NULL,
  content JSONB NOT NULL,
  /* Example content:
  {
    "pattern": "blog_post_structure",
    "description": "Optimal structure for tech blogs",
    "template": {
      "introduction": {"words": 150, "hooks": ["question", "statistic"]},
      "body": {"sections": 3-5, "words_per_section": 400},
      "conclusion": {"words": 100, "cta": true}
    },
    "success_rate": 0.89,
    "usage_count": 234
  }
  */
  access_control JSONB DEFAULT '{"visibility": "public"}',
  quality_score DECIMAL(3,2),
  usage_stats JSONB,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Knowledge discovery
CREATE INDEX idx_knowledge_search ON agent_knowledge 
  USING gin(content);
  
CREATE INDEX idx_knowledge_domain ON agent_knowledge(domain);
```

### 5. Evolutionary Mechanisms

```sql
-- Strategy evolution tracking
CREATE TABLE agent_strategies (
  id UUID PRIMARY KEY,
  agent_role TEXT NOT NULL,
  strategy_name TEXT NOT NULL,
  strategy_version INTEGER NOT NULL,
  strategy_spec JSONB NOT NULL,
  parent_strategy_id UUID REFERENCES agent_strategies(id),
  performance_delta JSONB,  -- Improvement over parent
  adoption_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Natural selection mechanism
CREATE OR REPLACE FUNCTION select_best_strategy(
  p_agent_role TEXT,
  p_context JSONB
) RETURNS UUID AS $$
DECLARE
  v_strategy_id UUID;
BEGIN
  -- Tournament selection with fitness
  WITH strategy_fitness AS (
    SELECT 
      s.id,
      s.strategy_spec,
      COALESCE(AVG(p.quality_score), 0.5) * 
      (1 + LOG(GREATEST(s.adoption_count, 1))) as fitness
    FROM agent_strategies s
    LEFT JOIN (
      SELECT 
        strategy_id, 
        (metrics->>'quality_score')::DECIMAL as quality_score
      FROM agent_performance
      WHERE created_at > now() - interval '30 days'
    ) p ON s.id = p.strategy_id
    WHERE s.agent_role = p_agent_role
    GROUP BY s.id
  )
  SELECT id INTO v_strategy_id
  FROM strategy_fitness
  ORDER BY fitness DESC, RANDOM()
  LIMIT 1;
  
  -- Update adoption count
  UPDATE agent_strategies 
  SET adoption_count = adoption_count + 1
  WHERE id = v_strategy_id;
  
  RETURN v_strategy_id;
END;
$$ LANGUAGE plpgsql;
```

### 6. Network Effects & Emergence

```sql
-- Agent interaction network
CREATE TABLE agent_interactions (
  id UUID PRIMARY KEY,
  source_agent TEXT NOT NULL,
  target_agent TEXT NOT NULL,
  interaction_type TEXT NOT NULL,  -- 'collaboration', 'competition', 'knowledge_transfer'
  contract_id UUID REFERENCES contracts(id),
  quality_impact DECIMAL(3,2),     -- -1 to +1
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Network analysis views
CREATE VIEW agent_network_stats AS
WITH interaction_graph AS (
  SELECT 
    source_agent,
    target_agent,
    COUNT(*) as interaction_count,
    AVG(quality_impact) as avg_impact
  FROM agent_interactions
  WHERE created_at > now() - interval '30 days'
  GROUP BY source_agent, target_agent
)
SELECT 
  source_agent as agent,
  COUNT(DISTINCT target_agent) as connection_count,
  SUM(interaction_count) as total_interactions,
  AVG(avg_impact) as network_benefit
FROM interaction_graph
GROUP BY source_agent;
```

## Implementation Patterns

### 1. Dynamic Agent Registration

```sql
-- Allow new agents to join ecosystem
CREATE OR REPLACE FUNCTION register_agent(
  p_agent_role TEXT,
  p_capabilities JSONB,
  p_llm_config JSONB
) RETURNS BOOLEAN AS $$
BEGIN
  -- Insert into registry
  INSERT INTO agent_registry (agent_role, status)
  VALUES (p_agent_role, 'idle')
  ON CONFLICT (agent_role) DO UPDATE
  SET last_heartbeat = now();
  
  -- Register capabilities
  INSERT INTO agent_capabilities (
    agent_role,
    capability_type,
    capability_name,
    capability_spec
  )
  SELECT 
    p_agent_role,
    cap->>'type',
    cap->>'name',
    cap->'spec'
  FROM jsonb_array_elements(p_capabilities) cap
  ON CONFLICT (agent_role, capability_name) DO UPDATE
  SET capability_spec = EXCLUDED.capability_spec;
  
  -- Store LLM configuration
  INSERT INTO llm_configs (
    organization_id,
    provider,
    model,
    encrypted_api_key
  )
  VALUES (
    '00000000-0000-0000-0000-000000000000', -- System org
    p_llm_config->>'provider',
    p_llm_config->>'model',
    encrypt_key(p_llm_config->>'api_key')
  );
  
  RETURN true;
END;
$$ LANGUAGE plpgsql;
```

### 2. Capability-Based Team Formation

```sql
-- Match agents to contract requirements
CREATE OR REPLACE FUNCTION form_optimal_team(
  p_contract_id UUID
) RETURNS UUID AS $$
DECLARE
  v_contract contracts;
  v_team_id UUID;
  v_team_composition JSONB;
BEGIN
  -- Get contract requirements
  SELECT * INTO v_contract FROM contracts WHERE id = p_contract_id;
  
  -- Match capabilities to requirements
  WITH capability_matches AS (
    SELECT 
      ac.agent_role,
      ac.capability_name,
      ac.capability_spec,
      ar.avg_quality as reputation,
      -- Score based on capability match
      calculate_capability_match(
        ac.capability_spec, 
        v_contract.spec
      ) as match_score
    FROM agent_capabilities ac
    JOIN agent_reputation ar ON ac.agent_role = ar.agent_role
    WHERE ac.capability_spec @> v_contract.spec->'required_capabilities'
  ),
  optimal_team AS (
    -- Select best agent for each required capability
    SELECT DISTINCT ON (capability_name)
      agent_role,
      capability_name,
      match_score * reputation as combined_score
    FROM capability_matches
    ORDER BY capability_name, combined_score DESC
  )
  SELECT json_agg(
    json_build_object(
      'role', agent_role,
      'capability', capability_name,
      'score', combined_score
    )
  ) INTO v_team_composition
  FROM optimal_team;
  
  -- Create team
  INSERT INTO agent_teams (
    team_name,
    contract_id,
    lead_agent,
    team_composition,
    formation_strategy
  ) VALUES (
    format('Team-%s', substr(p_contract_id::text, 1, 8)),
    p_contract_id,
    (v_team_composition->0->>'role')::TEXT,
    json_build_object('members', v_team_composition),
    'capability_match'
  ) RETURNING id INTO v_team_id;
  
  RETURN v_team_id;
END;
$$ LANGUAGE plpgsql;
```

### 3. Collective Learning System

```sql
-- Aggregate learnings across agents
CREATE OR REPLACE FUNCTION share_collective_insight(
  p_pattern JSONB,
  p_contributing_agents TEXT[]
) RETURNS VOID AS $$
DECLARE
  v_insight_id UUID;
BEGIN
  -- Store collective insight
  INSERT INTO agent_knowledge (
    contributor_agent,
    knowledge_type,
    domain,
    content
  ) VALUES (
    array_to_string(p_contributing_agents, '+'),
    'collective_insight',
    p_pattern->>'domain',
    json_build_object(
      'pattern', p_pattern,
      'contributors', p_contributing_agents,
      'synthesis_method', 'consensus',
      'confidence', calculate_collective_confidence(p_pattern)
    )
  ) RETURNING id INTO v_insight_id;
  
  -- Notify all agents
  INSERT INTO bus_events (
    event_type,
    payload,
    channel
  ) VALUES (
    'collective_learning',
    json_build_object(
      'insight_id', v_insight_id,
      'domain', p_pattern->>'domain',
      'impact', 'high'
    ),
    'agent_events'
  );
END;
$$ LANGUAGE plpgsql;
```

## Ecosystem Growth Strategies

### 1. Specialization Incentives
```sql
-- Reward unique capabilities
CREATE VIEW specialization_rewards AS
SELECT 
  agent_role,
  COUNT(DISTINCT capability_name) as capability_count,
  -- Rarity bonus
  SUM(1.0 / agent_count_with_capability) as rarity_score,
  -- Performance in specialized tasks
  AVG(CASE 
    WHEN is_specialized_task THEN quality_score * 1.5 
    ELSE quality_score 
  END) as weighted_performance
FROM agent_capability_stats
GROUP BY agent_role;
```

### 2. Collaboration Networks
```sql
-- Track and reward successful collaborations
CREATE TRIGGER track_collaboration_success
AFTER UPDATE ON contracts
FOR EACH ROW
WHEN (NEW.status = 'completed' AND OLD.status != 'completed')
EXECUTE FUNCTION reward_team_collaboration();
```

### 3. Innovation Mechanisms
```sql
-- Strategy mutation for innovation
CREATE FUNCTION mutate_strategy(
  p_parent_strategy_id UUID,
  p_mutation_rate DECIMAL DEFAULT 0.1
) RETURNS UUID AS $$
  -- Implement genetic algorithm-style mutation
$$ LANGUAGE plpgsql;
```

## Critical Implementation Considerations

1. **Start Small**: 5-10 agents before 100s
2. **Enable Discovery**: Agents must find each other
3. **Incentivize Diversity**: Avoid monoculture
4. **Measure Everything**: Network effects need data
5. **Allow Failure**: Evolution requires selection
6. **Share Knowledge**: Collective intelligence
7. **Gradual Autonomy**: Human oversight initially
8. **Test Interactions**: Complex emergence
9. **Monitor Health**: Ecosystem stability
10. **Plan Scale**: Design for 1000+ agents

Your ecosystem design must create conditions for emergence while working within PostgreSQL and event-driven constraints. Focus on creating the "soil" where AI agent communities can grow and thrive.