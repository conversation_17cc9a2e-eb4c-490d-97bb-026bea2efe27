# Common Definitions for Phase 10 Economic Constitution

## Core Value Dimensions

### 1. Quality (Q)
- **Definition**: The degree to which a delivered service meets or exceeds specifications and expectations
- **Measurement**: Scale of 0.0 to 1.0
  - 0.0-0.5: Failure (does not meet basic requirements)
  - 0.5-0.7: Acceptable (meets minimum requirements)
  - 0.7-0.85: Good (meets requirements with some excellence)
  - 0.85-0.95: Excellent (exceeds expectations)
  - 0.95-1.0: Exceptional (revolutionary quality)
- **Components**:
  - Technical accuracy
  - Creative excellence
  - Usability/accessibility
  - Documentation completeness
  - Code quality (if applicable)

### 2. Speed (S)
- **Definition**: Time from contract acceptance to delivery
- **Measurement**: 
  - Absolute: Hours/days
  - Relative: Percentage of deadline used
  - Efficiency: Output per unit time
- **Modifiers**:
  - Rush multiplier: Urgency premium
  - Complexity adjustment: Time per complexity unit
  - Parallel capacity: Simultaneous task handling

### 3. Reliability (R)
- **Definition**: Consistency of performance across multiple contracts
- **Measurement**:
  - Success rate: Completed contracts / Total contracts
  - Variance: Standard deviation of quality scores
  - Uptime: Availability when needed
  - Recovery: Bounce-back from failures
- **Formula**: R = Success_Rate × (1 - Quality_Variance) × Uptime

### 4. Innovation (I)
- **Definition**: Introduction of novel methods, approaches, or solutions
- **Measurement**:
  - Novelty score: How different from existing solutions
  - Adoption rate: How many others adopt the innovation
  - Impact score: Improvement in outcomes
  - Patent-ability: Could this be protected IP?
- **Categories**:
  - Incremental: 0.1-0.3
  - Substantial: 0.3-0.7
  - Breakthrough: 0.7-1.0

## Reputation System

### Unified Reputation Score (URS)
```
URS = 0.4×Q_avg + 0.2×R + 0.2×S_score + 0.1×I + 0.1×Collaboration
```

### Properties:
- **Non-transferable**: Tied to specific agent identity
- **Time-weighted**: Recent performance matters more
- **Domain-specific**: Separate scores per service category
- **Stakeable**: Can be put at risk for guarantees
- **Visible**: Publicly queryable with history

### Reputation Levels:
1. **Novice**: 0.0-0.3 (New agents, limited history)
2. **Competent**: 0.3-0.5 (Proven basic ability)
3. **Professional**: 0.5-0.7 (Consistent quality delivery)
4. **Expert**: 0.7-0.85 (High performance, innovation)
5. **Master**: 0.85-1.0 (Top tier, market leaders)

## Efficiency Metrics

### 1. Allocative Efficiency (AE)
- **Definition**: How well resources are distributed to highest-value uses
- **Formula**: AE = Actual_Value_Created / Maximum_Possible_Value
- **Target**: >95%
- **Components**:
  - Matching efficiency
  - Price efficiency
  - Quality allocation
  - Time utilization

### 2. Dynamic Efficiency (DE)
- **Definition**: System's ability to improve over time
- **Measurement**:
  - Innovation rate
  - Learning curves
  - Adaptation speed
  - Obsolescence avoidance

### 3. Informational Efficiency (IE)
- **Definition**: How quickly and accurately information is incorporated into prices
- **Measurement**:
  - Price discovery time
  - Prediction accuracy
  - Information leakage
  - Signal quality

### 4. Operational Efficiency (OE)
- **Definition**: How well the system executes its functions
- **Components**:
  - Transaction costs
  - Settlement speed
  - Error rates
  - System uptime

## Team Dynamics

### Team Value Function
```
V_team = Σ(V_individual) × Synergy_Factor × Coordination_Efficiency
```

### Synergy Factor Calculation:
- **Complementary Skills**: +0.1 to +0.3
- **Shared Experience**: +0.05 per successful project
- **Communication Quality**: +0.1 to +0.2
- **Cultural Fit**: +0.05 to +0.15
- **Maximum Synergy**: 1.5x individual sum

### Team Formation Rules:
1. Minimum team size: 2 agents
2. Maximum team size: Logarithmic penalty above 7
3. Role requirements: Must cover all contract needs
4. Reputation threshold: Average must meet contract requirements
5. Availability matching: All members available for duration

## Risk Categories

### 1. Performance Risk
- **Definition**: Risk of not meeting contract specifications
- **Measurement**: Historical failure rate × Contract complexity
- **Mitigation**: Performance bonds, insurance, reputation staking

### 2. Market Risk
- **Definition**: Risk from price movements and volatility
- **Types**:
  - Price risk: Unfavorable rate changes
  - Liquidity risk: Cannot find counterparty
  - Basis risk: Hedge imperfection
- **Measurement**: VaR, stress tests, Greeks

### 3. Operational Risk
- **Definition**: Risk from system failures or process errors
- **Categories**:
  - Technical failure
  - Human error
  - External events
  - Legal/regulatory
- **Measurement**: Event frequency × Average loss

### 4. Systemic Risk
- **Definition**: Risk that threatens entire system stability
- **Sources**:
  - Cascade failures
  - Liquidity crises
  - Governance breakdown
  - External shocks
- **Monitoring**: Network analysis, stress testing

### 5. Reputation Risk
- **Definition**: Risk of reputation damage affecting future opportunities
- **Sources**:
  - Quality failures
  - Ethical violations
  - Association risk
  - Communication failures
- **Impact**: Reduced contract flow, price penalties

## Standard Time Units

### Base Units:
- **Minute**: For high-frequency trading
- **Hour**: For task-level measurement  
- **Day**: For project planning (8 working hours)
- **Week**: For sprint planning (5 working days)
- **Month**: For capacity planning (20 working days)

### Urgency Classifications:
1. **Critical**: <4 hours (3x price multiplier)
2. **Urgent**: <24 hours (2x multiplier)
3. **Priority**: <3 days (1.5x multiplier)
4. **Standard**: 3-7 days (1x multiplier)
5. **Relaxed**: >7 days (0.9x multiplier)

## Currency Exchange Principles

### Base Exchange Rates (Subject to Market Discovery):
- 1 RepStar ≈ $100 in contract value
- 1 QualToken ≈ 0.1 RepStar
- 1 Chronos ≈ $50 per hour saved
- 1 InnoBond ≈ Variable based on adoption
- 1 CollaboCoins ≈ 0.05 RepStar per connection

### Exchange Rules:
1. All rates float based on supply/demand
2. Minimum tick size: 0.001
3. Maximum daily movement: ±20%
4. Circuit breakers at ±10% in 1 hour
5. Market maker obligations for major pairs

---

*These definitions form the foundational language for the VibeLaunch Economic Constitution. All agents must use these definitions consistently to ensure system coherence.*