# Technical Context for Market Theorist

## Current VibeLaunch Architecture Overview

VibeLaunch is a pre-production digital marketplace built on PostgreSQL/Supabase. Understanding these technical constraints is crucial for designing realistic market mechanisms.

## Database Schema & Capabilities

### Core Market Tables

```sql
-- Contracts table: The demand side of the market
contracts (
  id UUID PRIMARY KEY,
  organisation_id UUID NOT NULL,  -- Multi-tenant isolation
  category TEXT NOT NULL,         -- 'content', 'design', 'video'
  title TEXT NOT NULL,
  brief TEXT NOT NULL,
  spec JSONB,                     -- Flexible specifications
  deadline TIMESTAMPTZ NOT NULL,
  budget DECIMAL(10, 2) NOT NULL, -- Fixed precision money
  created_by UUID,
  status TEXT DEFAULT 'draft',    -- 'draft', 'published', 'assigned', 'completed'
  brand_context_url TEXT
)

-- Bids table: The supply side of the market
bids (
  id UUID PRIMARY KEY,
  contract_id UUID REFERENCES contracts(id),
  agent_role TEXT NOT NULL,       -- Which agent type is bidding
  price DECIMAL(10, 2) NOT NULL,
  status bid_status DEFAULT 'pending', -- ENUM: 'pending', 'accepted', 'rejected'
  confidence DECIMAL(5, 4),       -- 0-1 confidence score
  expected_kpi JSONB,             -- Promised performance metrics
  task_id UUID REFERENCES tasks(id)
)

-- Agent Registry: Market participants
agent_registry (
  agent_role TEXT PRIMARY KEY,
  status agent_status DEFAULT 'idle', -- ENUM: 'idle', 'running', 'error'
  current_load INTEGER,           -- Active tasks
  last_heartbeat TIMESTAMPTZ      -- Health monitoring
)
```

### Event Infrastructure

```sql
-- Bus Events: Real-time market signals
bus_events (
  id UUID PRIMARY KEY,
  organisation_id UUID,
  event_type TEXT NOT NULL,
  payload JSONB NOT NULL,
  channel TEXT,                   -- 'pipeline', 'sequential', 'mcp', 'agent_events'
  processed BOOLEAN DEFAULT false
)
```

## Current Market Limitations You Must Address

### 1. Price-Only Competition
- **Current**: `SELECT * FROM bids WHERE contract_id = ? ORDER BY price ASC LIMIT 1`
- **Problem**: Destroys quality incentives
- **Your Challenge**: Design multi-dimensional bid evaluation

### 2. No Team Formation
- **Current**: Single agent wins entire contract
- **Problem**: Complex tasks need specialist teams
- **Your Challenge**: Enable collaborative bidding within SQL constraints

### 3. No Dynamic Pricing
- **Current**: Fixed budgets, static bids
- **Problem**: No price discovery or market-making
- **Your Challenge**: Design price discovery mechanisms

### 4. No Reputation System
- **Current**: No agent_performance table exists
- **Problem**: No quality signals or trust building
- **Your Challenge**: Design reputation that works without historical data

## Technical Constraints for Your Mechanisms

### 1. SQL-Based Implementation
All mechanisms must be expressible as:
- SQL queries and views
- PostgreSQL functions and triggers
- JSONB operations for complex data
- Common Table Expressions (CTEs) for complex logic

Example template:
```sql
CREATE OR REPLACE FUNCTION calculate_bid_score(
  bid_id UUID
) RETURNS DECIMAL AS $$
DECLARE
  score DECIMAL;
BEGIN
  -- Your market mechanism logic here
  SELECT 
    (1.0 / price) * confidence * 
    (expected_kpi->>'quality_score')::DECIMAL
  INTO score
  FROM bids WHERE id = bid_id;
  
  RETURN score;
END;
$$ LANGUAGE plpgsql;
```

### 2. Multi-Tenant Isolation (RLS)
Every mechanism must respect organization boundaries:
```sql
-- All queries automatically filtered by organization
CREATE POLICY contracts_isolation ON contracts
  USING (organisation_id = current_org_id());
```

### 3. Real-Time Constraints
- PostgreSQL NOTIFY/LISTEN for events (may bottleneck)
- WebSocket subscriptions for UI updates
- No true background processing
- Synchronous operations only

### 4. Transactional Integrity
All market operations must be ACID-compliant:
```sql
BEGIN;
  UPDATE bids SET status = 'accepted' WHERE id = ?;
  UPDATE bids SET status = 'rejected' 
    WHERE contract_id = ? AND id != ?;
  UPDATE contracts SET status = 'assigned' WHERE id = ?;
COMMIT;
```

## Available Building Blocks

### 1. JSONB for Complex Mechanisms
```sql
-- Store multi-dimensional values
bid_data JSONB DEFAULT '{
  "price_vector": {"base": 100, "rush": 150, "premium": 200},
  "quality_metrics": {"accuracy": 0.95, "creativity": 0.8},
  "constraints": {"max_load": 5, "specializations": ["seo", "content"]}
}'
```

### 2. Event Broadcasting
```sql
-- Notify all agents of market changes
PERFORM pg_notify('agent_events', json_build_object(
  'type', 'market_update',
  'contract_id', NEW.id,
  'action', 'new_contract_posted'
)::text);
```

### 3. Sequential Thinking Integration
- Agents can use complex reasoning chains
- Up to 300+ thought steps for decisions
- Can process market history and patterns
- Perfect for mechanism design calculations

### 4. Computed Columns & Views
```sql
-- Create market aggregates
CREATE VIEW market_depth AS
SELECT 
  contract_id,
  COUNT(*) as bid_count,
  AVG(price) as avg_price,
  MIN(price) as best_price,
  STDDEV(price) as price_variance
FROM bids
WHERE status = 'pending'
GROUP BY contract_id;
```

## Performance Boundaries

### Scale Limitations
- Tested with <10 concurrent users
- PostgreSQL NOTIFY may bottleneck at 100+ agents
- No caching layer (every query hits DB)
- No horizontal scaling possible

### Query Complexity
- Complex JOINs with JSONB may be slow
- No query result caching
- Index on: organisation_id, status, contract_id
- Missing indexes for complex mechanisms

### Computational Limits
- SQL functions have timeout limits
- No true parallel processing
- Memory constraints for large computations
- No GPU acceleration for mechanism calculations

## Incremental Implementation Path

### Phase 1: Enhanced Bid Scoring (SQL Functions)
```sql
-- Start with simple multi-factor scoring
CREATE FUNCTION score_bid_v1(bid_id UUID) 
RETURNS DECIMAL AS $$
  -- Implement basic multi-dimensional scoring
$$ LANGUAGE plpgsql;
```

### Phase 2: Team Formation (JSONB + Triggers)
```sql
-- Enable team bids via JSONB
ALTER TABLE bids ADD COLUMN team_composition JSONB;
-- Trigger to validate team constraints
```

### Phase 3: Dynamic Pricing (Views + Events)
```sql
-- Market-making view
CREATE VIEW market_prices AS ...
-- Events for price updates
```

### Phase 4: Reputation System (New Table)
```sql
-- Finally add performance tracking
CREATE TABLE agent_performance (
  agent_role TEXT,
  contract_id UUID,
  outcome_metrics JSONB,
  client_rating DECIMAL
);
```

## Critical Success Factors

Your market mechanisms will succeed if they:

1. **Work within PostgreSQL**: No external systems required
2. **Respect RLS**: Organization isolation maintained
3. **Scale incrementally**: Start with 5 agents, not 500
4. **Use existing tables**: Minimize schema changes
5. **Provide clear value**: Measurable improvement over "lowest price wins"
6. **Enable testing**: Can simulate with mock data
7. **Support rollback**: Can revert to simple mode
8. **Document SQL**: Every mechanism has migration file

## Example: Implementing Your First Mechanism

Here's how you might implement a basic multi-dimensional bid evaluation:

```sql
-- Migration: 20250114_market_theorist_multidim_scoring.sql

-- Add scoring function
CREATE OR REPLACE FUNCTION calculate_multidim_bid_score(
  p_bid_id UUID
) RETURNS JSONB AS $$
DECLARE
  v_bid RECORD;
  v_contract RECORD;
  v_score JSONB;
BEGIN
  -- Get bid details
  SELECT b.*, c.category, c.spec
  INTO v_bid
  FROM bids b
  JOIN contracts c ON b.contract_id = c.id
  WHERE b.id = p_bid_id;
  
  -- Calculate multi-dimensional score
  v_score := jsonb_build_object(
    'price_score', 1.0 / v_bid.price,
    'quality_score', v_bid.confidence,
    'specialization_match', 
      CASE 
        WHEN v_bid.agent_role LIKE '%' || v_contract.category || '%' 
        THEN 1.0 ELSE 0.7 
      END,
    'composite', (1.0 / v_bid.price) * v_bid.confidence * 0.8
  );
  
  RETURN v_score;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add trigger to auto-calculate on bid submission
CREATE TRIGGER calculate_bid_score_trigger
  AFTER INSERT OR UPDATE ON bids
  FOR EACH ROW
  EXECUTE FUNCTION update_bid_score();
```

Remember: You're designing the economic laws for a new form of intelligent life, but these laws must execute within PostgreSQL stored procedures. Think revolutionary, but implement incrementally.