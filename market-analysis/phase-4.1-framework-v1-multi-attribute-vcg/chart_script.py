import plotly.graph_objects as go
import pandas as pd

# Create the dataframe from the provided data
auction_data = {
    "auction_types": ["First-Price Sealed-Bid\n(Current VibeLaunch)", "Second-Price\n(<PERSON><PERSON><PERSON>)", "Multi-Attribute\nScoring", "VCG Mechanism", "Dynamic Auction\nwith Learning", "Combinatorial\nAuction"],
    "allocative_efficiency": [42, 78, 85, 90, 88, 83],
    "revenue_efficiency": [35, 82, 88, 85, 91, 86],
    "strategic_proofness": [2, 9, 7, 9, 6, 8]
}

df = pd.DataFrame(auction_data)

# Abbreviate auction type names to fit 15 character limit for display
abbreviated_types = [
    "First-Price\n(Current)", 
    "Second-Price\n(<PERSON><PERSON>rey)", 
    "Multi-Attr\nScoring", 
    "VCG Mechanism", 
    "Dynamic w/\nLearning", 
    "Combinatorial\nAuction"
]

# Create the grouped bar chart
fig = go.Figure()

# Add bars for each metric using brand colors
fig.add_trace(go.Bar(
    name='Allocative Eff',
    x=abbreviated_types,
    y=df['allocative_efficiency'],
    marker_color='#1FB8CD',
    cliponaxis=False
))

fig.add_trace(go.Bar(
    name='Revenue Eff',
    x=abbreviated_types,
    y=df['revenue_efficiency'],
    marker_color='#FFC185',
    cliponaxis=False
))

fig.add_trace(go.Bar(
    name='Strategic Proof',
    x=abbreviated_types,
    y=df['strategic_proofness'],
    marker_color='#ECEBD5',
    cliponaxis=False
))

# Update layout
fig.update_layout(
    title='Auction Mechanism Performance',
    xaxis_title='Auction Type',
    yaxis_title='Performance %',
    barmode='group',
    legend=dict(orientation='h', yanchor='bottom', y=1.05, xanchor='center', x=0.5)
)

# Save the chart
fig.write_image('auction_performance_comparison.png')