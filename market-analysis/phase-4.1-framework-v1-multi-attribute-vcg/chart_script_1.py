import plotly.graph_objects as go

# Load the data
data = {"users": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100], "total_platform_value": [10.731472, 16.828764, 21.459096, 25.294379, 28.625799, 31.627054, 34.392547, 36.981159, 39.431007, 41.769953, 44.020030, 46.196745, 48.312286, 50.375214, 52.392296, 54.368654, 56.308649, 58.215949, 60.094494, 61.946681, 63.774485, 65.579507, 67.363013, 69.126061, 70.869520, 72.594082, 74.300278, 75.988495, 77.659000, 79.312045, 80.947859, 82.566659, 84.168643, 85.754001, 87.322901, 88.875505, 90.411963, 91.932411, 93.436976, 94.925774, 96.398915, 97.856501, 99.298628, 100.725385, 102.136853, 103.533110, 104.914228, 106.280275, 107.631314, 108.967406, 110.288609, 111.594979, 112.886570, 114.163433, 115.425620, 116.673179, 117.906158, 119.124603, 120.328557, 121.518064, 122.693164, 123.853899, 125.000308, 126.132428, 127.250296, 128.353947, 129.443417, 130.518741, 131.579953, 132.627085, 133.660170, 134.679238, 135.684321, 136.675449, 137.652652, 138.615959, 139.565399, 140.501000, 141.422790, 142.330797, 143.225049, 144.105572, 144.972393, 145.825538, 146.665033, 147.490902, 148.303172, 149.101867, 149.887012, 150.658632, 151.416751, 152.161393, 152.892581, 153.610339, 154.314691, 155.005659, 155.683267, 156.347537, 157.208491, 157.714175, 158.206553], "org_value_from_agents": [0.8, 1.6, 2.4, 3.2, 4.0, 4.8, 5.6, 6.4, 7.2, 8.0, 8.8, 9.6, 10.4, 11.2, 12.0, 12.8, 13.6, 14.4, 15.2, 16.0, 16.8, 17.6, 18.4, 19.2, 20.0, 20.8, 21.6, 22.4, 23.2, 24.0, 24.8, 25.6, 26.4, 27.2, 28.0, 28.8, 29.6, 30.4, 31.2, 32.0, 32.8, 33.6, 34.4, 35.2, 36.0, 36.8, 37.6, 38.4, 39.2, 40.0, 40.8, 41.6, 42.4, 43.2, 44.0, 44.8, 45.6, 46.4, 47.2, 48.0, 48.8, 49.6, 50.4, 51.2, 52.0, 52.8, 53.6, 54.4, 55.2, 56.0, 56.8, 57.6, 58.4, 59.2, 60.0, 60.8, 61.6, 62.4, 63.2, 64.0, 64.8, 65.6, 66.4, 67.2, 68.0, 68.8, 69.6, 70.4, 71.2, 72.0, 72.8, 73.6, 74.4, 75.2, 76.0, 76.8, 77.6, 78.4, 79.2, 80.0], "agent_value_from_orgs": [3.0, 4.242641, 5.196152, 6.0, 6.708204, 7.348469, 7.937254, 8.485281, 9.0, 9.48683, 9.9499, 10.392305, 10.816654, 11.224972, 11.618951, 12.0, 12.369316, 12.727922, 13.076697, 13.416408, 13.747727, 14.071247, 14.387417, 14.696665, 15.0, 15.296815, 15.587451, 15.871953, 16.150329, 16.422577, 16.688688, 16.948683, 17.204651, 17.455696, 17.701851, 18.0, 18.276382, 18.547569, 18.81439, 19.0788, 19.33908, 19.595918, 19.849433, 20.099751, 20.346989, 20.591260, 20.832667, 21.071308, 21.30728, 21.540659, 21.771519, 22.0, 22.226053, 22.449944, 22.671568, 22.891051, 23.108440, 23.323808, 23.537219, 23.748684, 23.958296, 24.166091, 24.372158, 24.576549, 24.779309, 24.980492, 25.180147, 25.378315, 25.575038, 25.770356, 25.964315, 26.156951, 26.348305, 26.538408, 26.727291, 26.914982, 27.101510, 27.286900, 27.471179, 27.654370, 27.836496, 28.017578, 28.197634, 28.376685, 28.554746, 28.731838, 28.907974, 29.083166, 29.257424, 29.430758, 29.603178, 29.774696, 29.945318, 30.115053, 30.283909, 30.451891, 30.619006, 30.785258, 30.950652, 31.115193, 31.278885]}

# Create the figure
fig = go.Figure()

# Define colors from the brand palette
colors = ['#1FB8CD', '#FFC185', '#ECEBD5']

# Add Total Platform Value line
fig.add_trace(go.Scatter(
    x=data['users'],
    y=data['total_platform_value'],
    mode='lines',
    name='Total Platform',
    line=dict(color=colors[0], width=3),
    cliponaxis=False
))

# Add Organization Value from Agents line
fig.add_trace(go.Scatter(
    x=data['users'],
    y=data['org_value_from_agents'],
    mode='lines',
    name='Org from Agents',
    line=dict(color=colors[1], width=3, dash='dash'),
    cliponaxis=False
))

# Add Agent Value from Organizations line
fig.add_trace(go.Scatter(
    x=data['users'],
    y=data['agent_value_from_orgs'],
    mode='lines',
    name='Agent from Orgs',
    line=dict(color=colors[2], width=3, dash='dot'),
    cliponaxis=False
))

# Update layout
fig.update_layout(
    title='Network Effects in Two-Sided Markets',
    legend=dict(orientation='h', yanchor='bottom', y=1.05, xanchor='center', x=0.5)
)

# Update axes
fig.update_xaxes(title='Users')
fig.update_yaxes(title='Value')

# Save the figure
fig.write_image('network_effects_chart.png')