import plotly.graph_objects as go

# Data from the provided JSON
phases = ["Current", "Phase 1 (1-3 months)", "Phase 2 (3-6 months)", "Phase 3 (6-12 months)", "Phase 4 (12+ months)"]
efficiency = [42, 62, 82, 90, 90]
key_implementations = ["Price-only auctions", "Multi-attribute scoring + Reputation + Fees", "Advanced reputation + Cross-org matching + Dynamic pricing", "Continuous markets + Collusion detection + Learning", "Regulatory compliance"]
complexity = ["Current", "Medium", "High", "Very High", "Medium"]

# Abbreviate phase names for x-axis (15 char limit)
phase_short = ["Current", "Phase 1", "Phase 2", "Phase 3", "Phase 4"]

# Create color mapping for complexity levels using brand colors in order
complexity_colors = {
    "Current": "#1FB8CD",
    "Medium": "#FFC185", 
    "High": "#ECEBD5",
    "Very High": "#5D878F"
}

# Map colors to each phase based on complexity
colors = [complexity_colors[comp] for comp in complexity]

# Create the line chart with markers
fig = go.Figure()

# Add line with markers
fig.add_trace(go.Scatter(
    x=phase_short,
    y=efficiency,
    mode='lines+markers',
    line=dict(width=3, color='#1FB8CD'),
    marker=dict(
        size=12,
        color=colors,
        line=dict(width=2, color='white')
    ),
    hovertemplate='<b>%{x}</b><br>Efficiency: %{y}%<br>Complexity: %{customdata}<extra></extra>',
    customdata=complexity,
    showlegend=False,
    cliponaxis=False
))

# Update layout
fig.update_layout(
    title="Efficiency Roadmap",
    xaxis_title="Phase",
    yaxis_title="Efficiency %"
)

# Update axes
fig.update_xaxes(tickangle=0)
fig.update_yaxes(range=[35, 95], ticksuffix="%")

# Save the chart
fig.write_image("implementation_roadmap.png")