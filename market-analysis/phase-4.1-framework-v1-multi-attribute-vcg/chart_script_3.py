import pandas as pd
import plotly.graph_objects as go
import json

# Parse the data from the provided JSON
data_json = {"quality_level": [0.1, 0.109091, 0.118182, 0.127273, 0.136364, 0.145455, 0.154545, 0.163636, 0.172727, 0.181818, 0.190909, 0.2, 0.209091, 0.218182, 0.227273, 0.236364, 0.245455, 0.254545, 0.263636, 0.272727, 0.281818, 0.290909, 0.3, 0.309091, 0.318182, 0.327273, 0.336364, 0.345455, 0.354545, 0.363636, 0.372727, 0.381818, 0.390909, 0.4, 0.409091, 0.418182, 0.427273, 0.436364, 0.445455, 0.454545, 0.463636, 0.472727, 0.481818, 0.490909, 0.5, 0.509091, 0.518182, 0.527273, 0.536364, 0.545455, 0.554545, 0.563636, 0.572727, 0.581818, 0.590909, 0.6, 0.609091, 0.618182, 0.627273, 0.636364, 0.645455, 0.654545, 0.663636, 0.672727, 0.681818, 0.690909, 0.7, 0.709091, 0.718182, 0.727273, 0.736364, 0.745455, 0.754545, 0.763636, 0.772727, 0.781818, 0.790909, 0.8, 0.809091, 0.818182, 0.827273, 0.836364, 0.845455, 0.854545, 0.863636, 0.872727, 0.881818, 0.890909, 0.9, 0.909091, 0.918182, 0.927273, 0.936364, 0.945455, 0.954545, 0.963636, 0.972727, 0.981818, 0.990909, 1.0], "current_welfare": [0.08, 0.087273, 0.094545, 0.101818, 0.109091, 0.116364, 0.123636, 0.130909, 0.138182, 0.145455, 0.152727, 0.16, 0.167273, 0.174545, 0.181818, 0.189091, 0.196364, 0.203636, 0.210909, 0.218182, 0.225455, 0.232727, 0.24, 0.247273, 0.254545, 0.261818, 0.269091, 0.276364, 0.283636, 0.290909, 0.298182, 0.305455, 0.312727, 0.32, 0.327273, 0.334545, 0.341818, 0.349091, 0.356364, 0.363636, 0.370909, 0.378182, 0.385455, 0.392727, 0.4, 0.407273, 0.414545, 0.421818, 0.429091, 0.436364, 0.443636, 0.450909, 0.458182, 0.465455, 0.472727, 0.12, 0.124364, 0.128727, 0.133091, 0.137455, 0.141818, 0.146182, 0.150545, 0.154909, 0.159273, 0.163636, 0.168, 0.172364, 0.176727, 0.181091, 0.185455, 0.189818, 0.194182, 0.198545, 0.202909, 0.207273, 0.211636, 0.216, 0.220364, 0.224727, 0.229091, 0.233455, 0.237818, 0.242182, 0.246545, 0.250909, 0.255273, 0.259636, 0.264, 0.268364, 0.272727, 0.277091, 0.281455, 0.285818, 0.290182, 0.294545, 0.298909, 0.303273, 0.307636, 0.312], "reputation_welfare": [0.0725, 0.079339, 0.086219, 0.093140, 0.100103, 0.107107, 0.114152, 0.121239, 0.128366, 0.135535, 0.142744, 0.149995, 0.157286, 0.164619, 0.171992, 0.179407, 0.186862, 0.194357, 0.201893, 0.209470, 0.217087, 0.224744, 0.232442, 0.240181, 0.247959, 0.255778, 0.263637, 0.271537, 0.279476, 0.287456, 0.295476, 0.303537, 0.311637, 0.319778, 0.327959, 0.336181, 0.344442, 0.352744, 0.361087, 0.369470, 0.377893, 0.386357, 0.394862, 0.403407, 0.411992, 0.420619, 0.429286, 0.437993, 0.446740, 0.455529, 0.464357, 0.473226, 0.482136, 0.491086, 0.500076, 0.509107, 0.518178, 0.527289, 0.536442, 0.545634, 0.554867, 0.564140, 0.573454, 0.582808, 0.592202, 0.601637, 0.611112, 0.620628, 0.630184, 0.639780, 0.649417, 0.659094, 0.668812, 0.678570, 0.688368, 0.698206, 0.708085, 0.718005, 0.727965, 0.737965, 0.748005, 0.758086, 0.768207, 0.778368, 0.788570, 0.798812, 0.809094, 0.819417, 0.829780, 0.840184, 0.850628, 0.861112, 0.871637, 0.882202, 0.892808, 0.903454, 0.914140, 0.924867, 0.935634, 0.946442, 0.957289, 0.968178], "scoring_welfare": [0.0635, 0.06962, 0.075798, 0.082033, 0.088326, 0.094676, 0.101083, 0.107548, 0.11407, 0.120649, 0.127286, 0.13398, 0.140731, 0.14754, 0.154406, 0.161329, 0.168309, 0.175347, 0.182442, 0.189594, 0.196804, 0.204071, 0.211395, 0.218777, 0.226216, 0.233712, 0.241266, 0.248877, 0.256545, 0.264271, 0.272054, 0.279895, 0.287793, 0.295748, 0.303761, 0.311831, 0.319959, 0.328144, 0.336386, 0.344686, 0.353043, 0.361458, 0.36993, 0.378459, 0.387046, 0.39569, 0.404392, 0.413151, 0.421967, 0.430841, 0.439772, 0.448761, 0.457807, 0.466911, 0.476072, 0.485291, 0.494567, 0.503901, 0.513292, 0.522741, 0.532247, 0.541811, 0.551432, 0.56111, 0.570846, 0.580639, 0.59049, 0.600398, 0.610364, 0.620387, 0.630467, 0.640605, 0.6508, 0.661053, 0.671363, 0.681731, 0.692156, 0.702638, 0.713178, 0.723775, 0.734429, 0.745141, 0.75591, 0.766737, 0.777621, 0.788562, 0.799561, 0.810617, 0.821731, 0.832902, 0.844131, 0.855417, 0.866761, 0.878162, 0.889621, 0.901137, 0.912711, 0.924342, 0.936031, 0.947777, 0.95958, 0.971441]}

# Check lengths and find minimum length to use consistent data
print(f"Quality level length: {len(data_json['quality_level'])}")
print(f"Current welfare length: {len(data_json['current_welfare'])}")
print(f"Reputation welfare length: {len(data_json['reputation_welfare'])}")
print(f"Scoring welfare length: {len(data_json['scoring_welfare'])}")

# All arrays are 100 elements except current_welfare which is 101
# Let's use the first 100 elements for all to be consistent
min_length = 100

quality_level = data_json['quality_level'][:min_length]
current_welfare = data_json['current_welfare'][:min_length]
reputation_welfare = data_json['reputation_welfare'][:min_length]
scoring_welfare = data_json['scoring_welfare'][:min_length]

# Create the figure
fig = go.Figure()

# Add the current welfare line with shaded area
fig.add_trace(go.Scatter(
    x=quality_level,
    y=current_welfare,
    mode='lines',
    name='Current',
    line=dict(color='#1FB8CD', width=2),
    fill='tozeroy',
    fillcolor='rgba(31, 184, 205, 0.2)',
    hovertemplate='Quality: %{x:.2f}<br>Welfare: %{y:.2f}',
    cliponaxis=False
))

# Add the reputation welfare line with shaded area
fig.add_trace(go.Scatter(
    x=quality_level,
    y=reputation_welfare,
    mode='lines',
    name='Reputation',
    line=dict(color='#FFC185', width=2),
    fill='tozeroy',
    fillcolor='rgba(255, 193, 133, 0.2)',
    hovertemplate='Quality: %{x:.2f}<br>Welfare: %{y:.2f}',
    cliponaxis=False
))

# Add the scoring welfare line with shaded area
fig.add_trace(go.Scatter(
    x=quality_level,
    y=scoring_welfare,
    mode='lines',
    name='Scoring',
    line=dict(color='#ECEBD5', width=2),
    fill='tozeroy',
    fillcolor='rgba(236, 235, 213, 0.2)',
    hovertemplate='Quality: %{x:.2f}<br>Welfare: %{y:.2f}',
    cliponaxis=False
))

# Update layout
fig.update_layout(
    title='Info Reveal Effects on Market Welfare',
    legend=dict(orientation='h', yanchor='bottom', y=1.05, xanchor='center', x=0.5),
    hovermode='x unified'
)

# Update axes (without cliponaxis as it's not a valid axis property)
fig.update_xaxes(
    title='Quality Level',
    tickformat='.1f'
)

fig.update_yaxes(
    title='Welfare',
    tickformat='.2f'
)

# Save the figure
fig.write_image("info_welfare_chart.png")

fig.show()