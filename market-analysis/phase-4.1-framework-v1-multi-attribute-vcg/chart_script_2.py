import plotly.graph_objects as go
import plotly.io as pio
import pandas as pd
import numpy as np

# Create DataFrame from the provided data
data = {
    "commission_rate": [0.0, 0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09, 0.1, 0.11, 0.12, 0.13, 0.14, 0.15, 0.16, 0.17, 0.18, 0.19, 0.2, 0.21, 0.22, 0.23, 0.24, 0.25, 0.26, 0.27, 0.28, 0.29, 0.3, 0.31, 0.32, 0.33, 0.34],
    "transaction_volume": [1000.0, 980.0, 960.0, 940.0, 920.0, 900.0, 880.0, 860.0, 840.0, 820.0, 800.0, 780.0, 760.0, 740.0, 720.0, 700.0, 680.0, 660.0, 640.0, 620.0, 600.0, 580.0, 560.0, 540.0, 520.0, 500.0, 480.0, 460.0, 440.0, 420.0, 400.0, 380.0, 360.0, 340.0, 320.0],
    "platform_revenue": [0.0, 9.8, 19.2, 28.2, 36.8, 45.0, 52.8, 60.2, 67.2, 73.8, 80.0, 85.8, 91.2, 96.2, 100.8, 105.0, 108.8, 112.2, 115.2, 117.8, 120.0, 121.8, 123.2, 124.2, 124.8, 125.0, 124.8, 124.2, 123.2, 121.8, 120.0, 117.8, 115.2, 112.2, 108.8]
}

df = pd.DataFrame(data)

# Find optimal commission rate (where platform revenue is maximized)
optimal_idx = df['platform_revenue'].idxmax()
optimal_rate = df.loc[optimal_idx, 'commission_rate']
optimal_revenue = df.loc[optimal_idx, 'platform_revenue']

# Normalize both metrics to percentage of their max values for better comparison
df['volume_pct'] = (df['transaction_volume'] / df['transaction_volume'].max()) * 100
df['revenue_pct'] = (df['platform_revenue'] / df['platform_revenue'].max()) * 100

# Create the figure
fig = go.Figure()

# Add transaction volume line (normalized)
fig.add_trace(go.Scatter(
    x=df['commission_rate'],
    y=df['volume_pct'],
    mode='lines',
    name='Trans Volume',
    line=dict(color='#FFC185', width=3),
    cliponaxis=False,
    hovertemplate='Rate: %{x:.0%}<br>Volume: %{customdata:.0f}<extra></extra>',
    customdata=df['transaction_volume']
))

# Add platform revenue line (normalized)
fig.add_trace(go.Scatter(
    x=df['commission_rate'],
    y=df['revenue_pct'],
    mode='lines',
    name='Platform Rev',
    line=dict(color='#1FB8CD', width=3),
    cliponaxis=False,
    hovertemplate='Rate: %{x:.0%}<br>Revenue: $%{customdata:.1f}<extra></extra>',
    customdata=df['platform_revenue']
))

# Mark the optimal point on revenue line
optimal_revenue_pct = (optimal_revenue / df['platform_revenue'].max()) * 100
fig.add_trace(go.Scatter(
    x=[optimal_rate],
    y=[optimal_revenue_pct],
    mode='markers',
    name='Optimal Point',
    marker=dict(
        color='#ECEBD5',
        size=15,
        symbol='circle',
        line=dict(color='#13343B', width=3)
    ),
    cliponaxis=False,
    hovertemplate='Optimal Rate: %{x:.0%}<br>Max Revenue: $%{customdata:.1f}<extra></extra>',
    customdata=[optimal_revenue]
))

# Update layout
fig.update_layout(
    title='Platform Revenue Optimization',
    xaxis_title='Commission Rate',
    yaxis_title='% of Maximum',
    legend=dict(orientation='h', yanchor='bottom', y=1.05, xanchor='center', x=0.5)
)

# Format axes
fig.update_xaxes(
    tickformat='.0%',
    range=[0, 0.34]
)

fig.update_yaxes(
    tickformat='.0f',
    ticksuffix='%',
    range=[0, 105]
)

# Save the chart
fig.write_image('platform_revenue_optimization.png')