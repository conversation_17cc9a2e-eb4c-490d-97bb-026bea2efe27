import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression

# Create the data
data = {
    "auction_types": ["First-Price Sealed-Bid (Current)", "Second-Price (<PERSON><PERSON><PERSON>)", "Multi-Attribute Scoring", "VCG Mechanism", "Dynamic Auction with Learning", "Combinatorial Auction"],
    "implementation_difficulty": [1, 2, 5, 7, 8, 9],
    "allocative_efficiency": [42, 78, 85, 90, 88, 83],
    "revenue_efficiency": [35, 82, 88, 85, 91, 86],
    "strategic_proofness": [2, 9, 7, 9, 6, 8]
}

df = pd.DataFrame(data)

# Create abbreviated labels for the mechanism names
df['short_names'] = ['First-Price', 'Vickrey', 'Multi-Attr', 'VCG', 'Dynamic', 'Combinatorial']

# Create the scatter plot
fig = px.scatter(df, 
                x='implementation_difficulty', 
                y='allocative_efficiency',
                size='revenue_efficiency',
                color='strategic_proofness',
                hover_name='short_names',
                title='Efficiency vs Complexity Trade-offs',
                labels={
                    'implementation_difficulty': 'Impl Difficulty',
                    'allocative_efficiency': 'Alloc Effic',
                    'strategic_proofness': 'Strategic Proof'
                },
                color_continuous_scale='Viridis')

# Update traces to set cliponaxis=False
fig.update_traces(cliponaxis=False)

# Add text labels to points
fig.add_trace(go.Scatter(
    x=df['implementation_difficulty'],
    y=df['allocative_efficiency'],
    mode='text',
    text=df['short_names'],
    textposition='top center',
    showlegend=False,
    textfont=dict(size=10),
    cliponaxis=False
))

# Add trend line
X = df['implementation_difficulty'].values.reshape(-1, 1)
y = df['allocative_efficiency'].values
reg = LinearRegression().fit(X, y)
trend_x = np.linspace(df['implementation_difficulty'].min(), df['implementation_difficulty'].max(), 100)
trend_y = reg.predict(trend_x.reshape(-1, 1))

fig.add_trace(go.Scatter(
    x=trend_x,
    y=trend_y,
    mode='lines',
    name='Trend',
    line=dict(dash='dash', color='gray'),
    showlegend=False,
    cliponaxis=False
))

# Save the chart
fig.write_image('auction_efficiency_chart.png')