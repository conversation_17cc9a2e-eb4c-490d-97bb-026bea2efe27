# Implementation Specifications for Multi-Agent Collaboration

## Executive Summary

This document provides detailed technical specifications for implementing multi-agent collaboration in VibeLaunch. It includes database schemas, API contracts, code implementations, and integration patterns necessary to support the CC-VCG framework.

## Database Schema Extensions

### Core Coalition Tables

```sql
-- Coalition formation and management
CREATE TABLE coalitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    formation_type VARCHAR(20) NOT NULL CHECK (formation_type IN ('sequential', 'parallel', 'hybrid')),
    status VARCHAR(50) NOT NULL DEFAULT 'forming',
    contract_id UUID REFERENCES contracts(id),
    total_bid DECIMAL(10,2),
    synergy_score DECIMAL(3,2) DEFAULT 1.0,
    coordination_cost DECIMAL(10,2) DEFAULT 0,
    formation_deadline TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Coalition membership with roles and contributions
CREATE TABLE coalition_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    coalition_id UUID REFERENCES coalitions(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agent_registry(id),
    role VARCHAR(100) NOT NULL,
    contribution_percentage DECIMAL(5,2) NOT NULL CHECK (contribution_percentage > 0 AND contribution_percentage <= 100),
    skill_match_score DECIMAL(3,2),
    accepted_invitation BOOLEAN DEFAULT FALSE,
    accepted_at TIMESTAMP,
    proposed_payment DECIMAL(10,2),
    final_payment DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(coalition_id, agent_id)
);

-- Task decomposition for complex contracts
CREATE TABLE task_decompositions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id UUID REFERENCES contracts(id),
    parent_task_id UUID REFERENCES task_decompositions(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    task_type VARCHAR(50) NOT NULL,
    required_skills TEXT[] NOT NULL,
    estimated_hours INTEGER,
    complexity_score INTEGER CHECK (complexity_score >= 1 AND complexity_score <= 10),
    budget_allocation DECIMAL(10,2),
    status VARCHAR(50) DEFAULT 'pending',
    assigned_agent_id UUID REFERENCES agent_registry(id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Dependencies between tasks
CREATE TABLE task_dependencies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    predecessor_task_id UUID REFERENCES task_decompositions(id),
    successor_task_id UUID REFERENCES task_decompositions(id),
    dependency_type VARCHAR(20) NOT NULL CHECK (dependency_type IN ('finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish')),
    lag_time INTEGER DEFAULT 0, -- in minutes
    handoff_criteria JSONB NOT NULL DEFAULT '{}',
    UNIQUE(predecessor_task_id, successor_task_id)
);

-- Coalition bids with internal structure
CREATE TABLE coalition_bids (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    coalition_id UUID REFERENCES coalitions(id),
    contract_id UUID REFERENCES contracts(id),
    total_bid_amount DECIMAL(10,2) NOT NULL,
    execution_plan JSONB NOT NULL,
    synergy_justification TEXT,
    estimated_completion_time INTEGER, -- in hours
    quality_guarantee_score DECIMAL(3,2),
    submitted_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(coalition_id, contract_id)
);

-- Payment distribution using Shapley values
CREATE TABLE payment_distributions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    coalition_id UUID REFERENCES coalitions(id),
    contract_id UUID REFERENCES contracts(id),
    total_payment DECIMAL(10,2) NOT NULL,
    vcg_payment DECIMAL(10,2) NOT NULL,
    platform_fee DECIMAL(10,2) NOT NULL,
    distribution_method VARCHAR(50) DEFAULT 'shapley',
    calculated_at TIMESTAMP DEFAULT NOW()
);

-- Individual payment allocations
CREATE TABLE agent_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_distribution_id UUID REFERENCES payment_distributions(id),
    agent_id UUID REFERENCES agent_registry(id),
    base_amount DECIMAL(10,2) NOT NULL,
    synergy_bonus DECIMAL(10,2) DEFAULT 0,
    performance_adjustment DECIMAL(10,2) DEFAULT 0,
    final_amount DECIMAL(10,2) NOT NULL,
    shapley_value DECIMAL(5,4), -- Normalized Shapley value
    payment_status VARCHAR(50) DEFAULT 'pending',
    paid_at TIMESTAMP
);

-- Collaboration execution tracking
CREATE TABLE collaboration_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    coalition_id UUID REFERENCES coalitions(id),
    contract_id UUID REFERENCES contracts(id),
    workflow_type VARCHAR(20) NOT NULL CHECK (workflow_type IN ('sequential', 'parallel', 'hybrid')),
    current_stage VARCHAR(100),
    started_at TIMESTAMP DEFAULT NOW(),
    estimated_completion TIMESTAMP,
    actual_completion TIMESTAMP,
    status VARCHAR(50) DEFAULT 'in_progress',
    checkpoint_data JSONB DEFAULT '{}'
);

-- Work handoffs between agents
CREATE TABLE work_handoffs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    execution_id UUID REFERENCES collaboration_executions(id),
    from_agent_id UUID REFERENCES agent_registry(id),
    to_agent_id UUID REFERENCES agent_registry(id),
    task_id UUID REFERENCES task_decompositions(id),
    handoff_package JSONB NOT NULL,
    quality_metrics JSONB DEFAULT '{}',
    validation_status VARCHAR(50) DEFAULT 'pending',
    validated_by UUID REFERENCES agent_registry(id),
    handoff_at TIMESTAMP DEFAULT NOW(),
    accepted_at TIMESTAMP
);

-- Synergy tracking for future predictions
CREATE TABLE synergy_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    coalition_members UUID[] NOT NULL, -- Array of agent IDs
    contract_type VARCHAR(100),
    measured_synergy DECIMAL(3,2) NOT NULL,
    coordination_efficiency DECIMAL(3,2),
    quality_score DECIMAL(3,2),
    completion_time_ratio DECIMAL(3,2), -- actual/estimated
    recorded_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_coalitions_contract ON coalitions(contract_id);
CREATE INDEX idx_coalitions_status ON coalitions(status);
CREATE INDEX idx_coalition_members_agent ON coalition_members(agent_id);
CREATE INDEX idx_task_decompositions_contract ON task_decompositions(contract_id);
CREATE INDEX idx_task_dependencies_successor ON task_dependencies(successor_task_id);
CREATE INDEX idx_collaboration_executions_status ON collaboration_executions(status);
CREATE INDEX idx_synergy_history_members ON synergy_history USING GIN(coalition_members);
```

## API Specifications

### Coalition Formation Endpoints

```typescript
// POST /api/v2/coalitions/propose
interface ProposeCoalitionRequest {
  contractId: string;
  proposedMembers: Array<{
    agentId: string;
    proposedRole: string;
    estimatedContribution: number; // percentage
  }>;
  formationType: 'sequential' | 'parallel' | 'hybrid';
  name: string;
  synergyJustification?: string;
}

interface ProposeCoalitionResponse {
  coalitionId: string;
  invitationsSent: number;
  formationDeadline: string; // ISO timestamp
}

// POST /api/v2/coalitions/:coalitionId/accept
interface AcceptCoalitionInviteRequest {
  agentId: string;
  agreedContribution?: number; // Can negotiate contribution
}

// GET /api/v2/coalitions/:coalitionId/status
interface CoalitionStatusResponse {
  coalition: {
    id: string;
    name: string;
    status: 'forming' | 'ready' | 'bidding' | 'executing' | 'completed';
    members: Array<{
      agentId: string;
      name: string;
      role: string;
      accepted: boolean;
      contribution: number;
    }>;
    contract: {
      id: string;
      title: string;
      budget: number;
    };
  };
}

// POST /api/v2/coalitions/:coalitionId/finalize
interface FinalizeCoalitionRequest {
  confirmedMembers: string[]; // Agent IDs
  executionPlan: {
    tasks: TaskDecomposition[];
    dependencies: TaskDependency[];
    timeline: Timeline;
  };
}
```

### Task Decomposition Endpoints

```typescript
// POST /api/v2/contracts/:contractId/decompose
interface DecomposeContractRequest {
  decompositionStrategy: 'automatic' | 'manual' | 'hybrid';
  targetGranularity: 'coarse' | 'medium' | 'fine';
  skillPriorities?: string[]; // Preferred skill types
}

interface DecomposeContractResponse {
  decomposition: {
    tasks: Array<{
      id: string;
      name: string;
      requiredSkills: string[];
      estimatedHours: number;
      budgetAllocation: number;
      dependencies: string[]; // Task IDs
    }>;
    totalEstimatedHours: number;
    criticalPath: string[]; // Task IDs in order
    parallelizableGroups: string[][]; // Groups of task IDs
  };
}

// PUT /api/v2/tasks/:taskId/dependencies
interface UpdateTaskDependenciesRequest {
  dependencies: Array<{
    predecessorId: string;
    type: 'finish_to_start' | 'start_to_start' | 'finish_to_finish' | 'start_to_finish';
    lagTime?: number; // minutes
    handoffCriteria: {
      requiredArtifacts: string[];
      qualityChecks: QualityCheck[];
    };
  }>;
}
```

### Coalition Bidding Endpoints

```typescript
// POST /api/v2/coalitions/:coalitionId/bid
interface SubmitCoalitionBidRequest {
  contractId: string;
  totalBid: number;
  executionPlan: {
    workflowType: 'sequential' | 'parallel' | 'hybrid';
    taskAssignments: Array<{
      taskId: string;
      assignedAgentId: string;
      estimatedHours: number;
    }>;
    timeline: {
      startDate: string;
      milestones: Array<{
        name: string;
        date: string;
        deliverables: string[];
      }>;
    };
  };
  qualityGuarantees: {
    minimumQualityScore: number;
    revisionPolicy: string;
    slaTerms: string[];
  };
}

interface SubmitCoalitionBidResponse {
  bidId: string;
  competitivenessScore: number; // How competitive vs other bids
  estimatedWinProbability: number;
}

// GET /api/v2/contracts/:contractId/coalition-bids
interface GetCoalitionBidsResponse {
  bids: Array<{
    bidId: string;
    coalitionId: string;
    coalitionName: string;
    memberCount: number;
    totalBid: number;
    estimatedCompletion: number; // hours
    qualityScore: number;
    synergyScore: number;
    submittedAt: string;
  }>;
  recommendedBid?: string; // Platform recommendation
}
```

### VCG Allocation Endpoints

```typescript
// POST /api/v2/contracts/:contractId/allocate-vcg
interface AllocateContractVCGRequest {
  allocationMethod: 'pure_vcg' | 'cc_vcg' | 'hybrid';
  considerationSet?: string[]; // Limit to specific coalition IDs
  qualityWeight: number; // 0-1, weight for quality vs price
}

interface AllocateContractVCGResponse {
  allocation: {
    winningCoalitionId: string;
    vcgPayment: number;
    efficiency: number; // 0-1
    socialWelfare: number;
  };
  paymentDistribution: Array<{
    agentId: string;
    shapleyValue: number;
    basePayment: number;
    synergyBonus: number;
    totalPayment: number;
  }>;
  runnerUp?: {
    coalitionId: string;
    bid: number;
    gap: number; // How close to winning
  };
}

// POST /api/v2/payments/calculate-shapley
interface CalculateShapleyRequest {
  coalitionId: string;
  totalPayment: number;
  contributions: Array<{
    agentId: string;
    taskIds: string[];
    performanceMetrics: {
      quality: number;
      timeliness: number;
      collaboration: number;
    };
  }>;
}

interface CalculateShapleyResponse {
  distributions: Array<{
    agentId: string;
    shapleyValue: number; // Normalized 0-1
    marginalContribution: number;
    fairShare: number; // Actual payment amount
  }>;
  calculations: {
    method: 'exact' | 'approximate';
    coalitionPermutations: number;
    computationTime: number; // ms
  };
}
```

### Collaboration Execution Endpoints

```typescript
// POST /api/v2/executions/start
interface StartExecutionRequest {
  coalitionId: string;
  contractId: string;
  workflowConfig: {
    type: 'sequential' | 'parallel' | 'hybrid';
    checkpoints: string[]; // Checkpoint names
    qualityGates: Array<{
      afterTask: string;
      criteria: QualityCriteria;
    }>;
  };
}

// POST /api/v2/executions/:executionId/handoff
interface CreateHandoffRequest {
  fromAgentId: string;
  toAgentId: string;
  taskId: string;
  artifacts: {
    location: string; // S3/storage URL
    type: string;
    metadata: Record<string, any>;
  }[];
  completionReport: {
    actualHours: number;
    challengesEncountered: string[];
    recommendationsForNext: string[];
  };
}

interface CreateHandoffResponse {
  handoffId: string;
  validationRequired: boolean;
  nextAgentNotified: boolean;
}

// GET /api/v2/executions/:executionId/progress
interface GetExecutionProgressResponse {
  execution: {
    id: string;
    status: 'in_progress' | 'paused' | 'completed' | 'failed';
    currentStage: string;
    percentComplete: number;
    tasksCompleted: number;
    totalTasks: number;
    timeline: {
      started: string;
      estimatedCompletion: string;
      currentDelay: number; // minutes behind/ahead
    };
  };
  activeAgents: Array<{
    agentId: string;
    currentTask: string;
    status: 'working' | 'waiting' | 'blocked';
    percentComplete: number;
  }>;
  upcomingHandoffs: Array<{
    from: string;
    to: string;
    estimatedTime: string;
    dependencies: string[];
  }>;
}
```

## Core Implementation Components

### Task Decomposition Engine

```typescript
import { Contract, Task, Skill, TaskGraph } from '@vibelaunch/types';

export class TaskDecompositionEngine {
  private skillMatcher: SkillMatcher;
  private complexityAnalyzer: ComplexityAnalyzer;
  
  async decomposeContract(
    contract: Contract,
    strategy: DecompositionStrategy
  ): Promise<TaskGraph> {
    // Step 1: Analyze contract requirements
    const requirements = await this.analyzeRequirements(contract);
    
    // Step 2: Identify major components
    const components = this.identifyComponents(requirements);
    
    // Step 3: Break down into tasks
    const tasks = await this.generateTasks(components, strategy);
    
    // Step 4: Identify dependencies
    const dependencies = this.identifyDependencies(tasks);
    
    // Step 5: Optimize task structure
    const optimizedGraph = this.optimizeTaskGraph(tasks, dependencies);
    
    // Step 6: Assign skill requirements
    const finalGraph = await this.assignSkillRequirements(optimizedGraph);
    
    return finalGraph;
  }
  
  private async analyzeRequirements(contract: Contract): Promise<Requirements> {
    // Use NLP to extract key requirements
    const extracted = await this.nlpService.extractRequirements(contract.description);
    
    // Categorize by type
    const categorized = {
      functional: extracted.filter(r => r.type === 'functional'),
      quality: extracted.filter(r => r.type === 'quality'),
      constraints: extracted.filter(r => r.type === 'constraint'),
      deliverables: extracted.filter(r => r.type === 'deliverable')
    };
    
    // Identify implicit requirements
    const implicit = this.inferImplicitRequirements(categorized, contract.vertical);
    
    return { ...categorized, implicit };
  }
  
  private identifyComponents(requirements: Requirements): Component[] {
    const components: Component[] = [];
    
    // Group related requirements
    const groups = this.clusterRequirements(requirements);
    
    for (const group of groups) {
      components.push({
        id: generateId(),
        name: this.generateComponentName(group),
        requirements: group,
        estimatedComplexity: this.estimateComplexity(group),
        suggestedSkills: this.suggestSkills(group)
      });
    }
    
    return components;
  }
  
  private generateTasks(
    components: Component[],
    strategy: DecompositionStrategy
  ): Task[] {
    const tasks: Task[] = [];
    
    for (const component of components) {
      const componentTasks = this.decomposeComponent(component, strategy);
      
      // Apply granularity settings
      const granularTasks = this.applyGranularity(
        componentTasks,
        strategy.granularity
      );
      
      tasks.push(...granularTasks);
    }
    
    // Ensure tasks are right-sized
    return this.balanceTasks(tasks);
  }
  
  private identifyDependencies(tasks: Task[]): Dependency[] {
    const dependencies: Dependency[] = [];
    
    // Identify logical dependencies
    for (const task of tasks) {
      const logicalDeps = this.findLogicalDependencies(task, tasks);
      dependencies.push(...logicalDeps);
    }
    
    // Identify resource dependencies
    const resourceDeps = this.findResourceDependencies(tasks);
    dependencies.push(...resourceDeps);
    
    // Identify data dependencies
    const dataDeps = this.findDataDependencies(tasks);
    dependencies.push(...dataDeps);
    
    // Remove cycles
    return this.removeCycles(dependencies);
  }
  
  private optimizeTaskGraph(
    tasks: Task[],
    dependencies: Dependency[]
  ): TaskGraph {
    // Build initial graph
    const graph = new TaskGraph(tasks, dependencies);
    
    // Identify critical path
    graph.calculateCriticalPath();
    
    // Find parallelization opportunities
    const parallelGroups = graph.findParallelizableGroups();
    
    // Balance workload
    const balanced = this.balanceWorkload(graph, parallelGroups);
    
    // Minimize coordination points
    const optimized = this.minimizeCoordination(balanced);
    
    return optimized;
  }
}
```

### Coalition Formation Service

```typescript
export class CoalitionFormationService {
  private agentMatcher: AgentMatcher;
  private synergyCalculator: SynergyCalculator;
  private db: Database;
  
  async proposeCoalition(
    contract: Contract,
    initiatorAgent: Agent,
    strategy?: FormationStrategy
  ): Promise<Coalition> {
    // Step 1: Analyze contract needs
    const needs = await this.analyzeContractNeeds(contract);
    
    // Step 2: Find compatible agents
    const candidates = await this.findCompatibleAgents(needs, initiatorAgent);
    
    // Step 3: Calculate optimal team composition
    const optimalTeam = this.calculateOptimalComposition(
      needs,
      candidates,
      strategy
    );
    
    // Step 4: Create coalition proposal
    const coalition = await this.createCoalitionProposal(
      contract,
      initiatorAgent,
      optimalTeam
    );
    
    // Step 5: Send invitations
    await this.sendInvitations(coalition, optimalTeam);
    
    return coalition;
  }
  
  async acceptInvitation(
    agentId: string,
    coalitionId: string,
    negotiatedTerms?: NegotiatedTerms
  ): Promise<AcceptanceResult> {
    // Validate invitation exists
    const invitation = await this.getInvitation(agentId, coalitionId);
    if (!invitation) {
      throw new Error('Invitation not found');
    }
    
    // Check if terms are acceptable
    const acceptable = this.validateTerms(invitation, negotiatedTerms);
    if (!acceptable) {
      return { accepted: false, reason: 'Terms not acceptable' };
    }
    
    // Update coalition membership
    await this.db.transaction(async (trx) => {
      await trx('coalition_members')
        .where({ coalition_id: coalitionId, agent_id: agentId })
        .update({
          accepted_invitation: true,
          accepted_at: new Date(),
          contribution_percentage: negotiatedTerms?.contribution || invitation.proposed_contribution
        });
      
      // Check if coalition is ready
      const ready = await this.checkCoalitionReady(coalitionId, trx);
      if (ready) {
        await trx('coalitions')
          .where({ id: coalitionId })
          .update({ status: 'ready' });
      }
    });
    
    return { accepted: true };
  }
  
  private async findCompatibleAgents(
    needs: ContractNeeds,
    initiator: Agent
  ): Promise<Agent[]> {
    // Get agents with required skills
    const skilledAgents = await this.agentMatcher.findBySkills(needs.requiredSkills);
    
    // Filter by availability
    const available = await this.filterByAvailability(
      skilledAgents,
      needs.timeline
    );
    
    // Calculate compatibility scores
    const scored = await Promise.all(
      available.map(async (agent) => ({
        agent,
        compatibility: await this.calculateCompatibility(initiator, agent, needs)
      }))
    );
    
    // Return top matches
    return scored
      .sort((a, b) => b.compatibility - a.compatibility)
      .slice(0, 20)
      .map(s => s.agent);
  }
  
  private calculateOptimalComposition(
    needs: ContractNeeds,
    candidates: Agent[],
    strategy?: FormationStrategy
  ): OptimalTeam {
    // Define objective function
    const objective = (team: Agent[]) => {
      const skillCoverage = this.calculateSkillCoverage(team, needs.requiredSkills);
      const synergy = this.synergyCalculator.calculate(team);
      const cost = this.estimateTeamCost(team, needs);
      const coordinationOverhead = this.estimateCoordination(team);
      
      // Weighted combination
      return (
        skillCoverage * 0.3 +
        synergy * 0.3 +
        (1 - cost / needs.budget) * 0.2 +
        (1 - coordinationOverhead) * 0.2
      );
    };
    
    // Use genetic algorithm for larger candidate sets
    if (candidates.length > 10) {
      return this.geneticOptimization(candidates, objective, strategy);
    }
    
    // Exhaustive search for smaller sets
    return this.exhaustiveSearch(candidates, objective, strategy);
  }
}
```

### CC-VCG Allocator Implementation

```typescript
export class CCVCGAllocator {
  private valuationEngine: ValuationEngine;
  private paymentCalculator: PaymentCalculator;
  
  async allocateContract(
    contract: Contract,
    coalitionBids: CoalitionBid[]
  ): Promise<AllocationResult> {
    // Step 1: Calculate social value for each coalition
    const valuations = await this.calculateValuations(contract, coalitionBids);
    
    // Step 2: Find welfare-maximizing allocation
    const optimalAllocation = this.findOptimalAllocation(valuations);
    
    // Step 3: Calculate VCG payment
    const vcgPayment = this.calculateVCGPayment(
      optimalAllocation,
      valuations
    );
    
    // Step 4: Calculate Shapley distribution
    const shapleyDistribution = await this.calculateShapleyDistribution(
      optimalAllocation.coalition,
      vcgPayment
    );
    
    // Step 5: Apply budget constraints
    const finalAllocation = this.applyBudgetConstraints(
      optimalAllocation,
      vcgPayment,
      contract.budget
    );
    
    return {
      winningCoalition: finalAllocation.coalition,
      totalPayment: finalAllocation.payment,
      individualPayments: shapleyDistribution,
      efficiency: finalAllocation.value / contract.budget,
      socialWelfare: finalAllocation.welfare
    };
  }
  
  private async calculateValuations(
    contract: Contract,
    bids: CoalitionBid[]
  ): Promise<CoalitionValuation[]> {
    const valuations: CoalitionValuation[] = [];
    
    for (const bid of bids) {
      const coalition = await this.getCoalition(bid.coalitionId);
      
      // Base value from quality and efficiency
      const baseValue = await this.valuationEngine.calculateBaseValue(
        contract,
        coalition,
        bid
      );
      
      // Synergy multiplier
      const synergy = await this.calculateSynergy(coalition, contract);
      
      // Coordination cost
      const coordinationCost = this.estimateCoordinationCost(
        coalition,
        bid.executionPlan
      );
      
      // Total value
      const totalValue = baseValue * synergy - coordinationCost;
      
      valuations.push({
        coalitionId: bid.coalitionId,
        bid: bid.totalBid,
        value: totalValue,
        synergy,
        coordinationCost
      });
    }
    
    return valuations;
  }
  
  private calculateVCGPayment(
    allocation: Allocation,
    valuations: CoalitionValuation[]
  ): number {
    // Calculate social welfare with winner
    const welfareWithWinner = valuations.reduce(
      (sum, v) => sum + Math.max(0, v.value - v.bid),
      0
    );
    
    // Calculate social welfare without winner
    const valuationsWithoutWinner = valuations.filter(
      v => v.coalitionId !== allocation.coalitionId
    );
    
    const welfareWithoutWinner = valuationsWithoutWinner.reduce(
      (sum, v) => sum + Math.max(0, v.value - v.bid),
      0
    );
    
    // VCG payment ensures truthfulness
    const winnerValue = allocation.value;
    const winnerExternality = welfareWithWinner - winnerValue - welfareWithoutWinner;
    
    return allocation.bid + winnerExternality;
  }
  
  private async calculateShapleyDistribution(
    coalition: Coalition,
    totalPayment: number
  ): Promise<Map<string, number>> {
    const members = coalition.members;
    const n = members.length;
    
    // Initialize Shapley values
    const shapleyValues = new Map<string, number>();
    members.forEach(m => shapleyValues.set(m.agentId, 0));
    
    // For small coalitions, exact calculation
    if (n <= 7) {
      return this.exactShapleyCalculation(coalition, totalPayment);
    }
    
    // For larger coalitions, use sampling approximation
    return this.approximateShapleyCalculation(coalition, totalPayment, 10000);
  }
  
  private exactShapleyCalculation(
    coalition: Coalition,
    totalPayment: number
  ): Map<string, number> {
    const members = coalition.members;
    const n = members.length;
    const shapleyValues = new Map<string, number>();
    
    // Generate all permutations
    const permutations = this.generatePermutations(members);
    
    for (const member of members) {
      let totalMarginalContribution = 0;
      
      for (const permutation of permutations) {
        const position = permutation.indexOf(member);
        const predecessors = permutation.slice(0, position);
        
        // Calculate marginal contribution
        const withMember = this.coalitionValue([...predecessors, member]);
        const withoutMember = this.coalitionValue(predecessors);
        const marginal = withMember - withoutMember;
        
        totalMarginalContribution += marginal;
      }
      
      const shapleyValue = totalMarginalContribution / permutations.length;
      shapleyValues.set(member.agentId, shapleyValue);
    }
    
    // Normalize to sum to total payment
    return this.normalizePayments(shapleyValues, totalPayment);
  }
}
```

### Workflow Orchestration Engine

```typescript
export class WorkflowOrchestrationEngine {
  private executionQueue: Queue;
  private stateManager: StateManager;
  private qualityGates: QualityGateService;
  
  async orchestrateExecution(
    coalition: Coalition,
    contract: Contract,
    plan: ExecutionPlan
  ): Promise<ExecutionResult> {
    // Initialize workflow
    const workflow = await this.initializeWorkflow(coalition, contract, plan);
    
    // Start execution based on type
    switch (workflow.type) {
      case 'sequential':
        return this.executeSequential(workflow);
      case 'parallel':
        return this.executeParallel(workflow);
      case 'hybrid':
        return this.executeHybrid(workflow);
    }
  }
  
  private async executeSequential(workflow: Workflow): Promise<ExecutionResult> {
    const tasks = workflow.tasks;
    const results: TaskResult[] = [];
    
    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i];
      const previousResult = i > 0 ? results[i - 1] : null;
      
      // Execute task
      const result = await this.executeTask(task, previousResult);
      results.push(result);
      
      // Quality gate check
      if (workflow.qualityGates[task.id]) {
        const passed = await this.qualityGates.evaluate(
          result,
          workflow.qualityGates[task.id]
        );
        
        if (!passed) {
          return this.handleQualityFailure(workflow, task, result);
        }
      }
      
      // Prepare handoff if not last task
      if (i < tasks.length - 1) {
        await this.prepareHandoff(
          task,
          tasks[i + 1],
          result
        );
      }
      
      // Update progress
      await this.updateProgress(workflow, (i + 1) / tasks.length);
    }
    
    return {
      status: 'completed',
      results,
      efficiency: this.calculateEfficiency(results, workflow),
      qualityScore: this.calculateQualityScore(results)
    };
  }
  
  private async executeParallel(workflow: Workflow): Promise<ExecutionResult> {
    const parallelGroups = workflow.parallelGroups;
    const allResults: TaskResult[] = [];
    
    for (const group of parallelGroups) {
      // Execute all tasks in group concurrently
      const groupPromises = group.map(task => this.executeTask(task, null));
      const groupResults = await Promise.all(groupPromises);
      
      // Quality checks for all group results
      for (let i = 0; i < group.length; i++) {
        const task = group[i];
        const result = groupResults[i];
        
        if (workflow.qualityGates[task.id]) {
          const passed = await this.qualityGates.evaluate(
            result,
            workflow.qualityGates[task.id]
          );
          
          if (!passed) {
            return this.handleQualityFailure(workflow, task, result);
          }
        }
      }
      
      allResults.push(...groupResults);
      
      // Synchronization point
      await this.synchronizeResults(groupResults, workflow);
    }
    
    return {
      status: 'completed',
      results: allResults,
      efficiency: this.calculateEfficiency(allResults, workflow),
      qualityScore: this.calculateQualityScore(allResults)
    };
  }
  
  private async executeHybrid(workflow: Workflow): Promise<ExecutionResult> {
    // Hybrid execution combines sequential stages with parallel tasks
    const stages = workflow.hybridStages;
    const allResults: TaskResult[] = [];
    
    for (const stage of stages) {
      let stageResults: TaskResult[];
      
      if (stage.type === 'sequential') {
        stageResults = await this.executeSequentialStage(stage, allResults);
      } else {
        stageResults = await this.executeParallelStage(stage, allResults);
      }
      
      allResults.push(...stageResults);
      
      // Stage-level quality gate
      if (stage.qualityGate) {
        const passed = await this.qualityGates.evaluateStage(
          stageResults,
          stage.qualityGate
        );
        
        if (!passed) {
          return this.handleStageFailure(workflow, stage, stageResults);
        }
      }
    }
    
    return {
      status: 'completed',
      results: allResults,
      efficiency: this.calculateEfficiency(allResults, workflow),
      qualityScore: this.calculateQualityScore(allResults)
    };
  }
  
  private async executeTask(
    task: Task,
    previousResult: TaskResult | null
  ): Promise<TaskResult> {
    // Get assigned agent
    const agent = await this.getAssignedAgent(task);
    
    // Prepare execution context
    const context: ExecutionContext = {
      task,
      contract: task.contract,
      previousResult,
      dependencies: await this.resolveDependencies(task),
      deadline: task.deadline
    };
    
    // Execute with monitoring
    const startTime = Date.now();
    const output = await this.monitoredExecution(agent, context);
    const endTime = Date.now();
    
    // Create result
    const result: TaskResult = {
      taskId: task.id,
      agentId: agent.id,
      status: 'completed',
      output,
      executionTime: endTime - startTime,
      quality: await this.assessQuality(output, task),
      metadata: {
        startTime,
        endTime,
        resourcesUsed: output.resourcesUsed
      }
    };
    
    // Store result
    await this.stateManager.saveTaskResult(result);
    
    return result;
  }
}
```

### Integration Patterns

```typescript
// Event-driven integration with existing system
export class MultiAgentEventHandler {
  constructor(
    private eventBus: EventBus,
    private coalitionService: CoalitionFormationService,
    private allocator: CCVCGAllocator,
    private orchestrator: WorkflowOrchestrationEngine
  ) {
    this.registerEventHandlers();
  }
  
  private registerEventHandlers() {
    // Contract created - trigger coalition formation suggestions
    this.eventBus.on('contract.created', async (event) => {
      const contract = event.data.contract;
      
      // Only suggest coalitions for complex contracts
      if (this.isComplexContract(contract)) {
        await this.suggestCoalitions(contract);
      }
    });
    
    // Coalition formed - enable bidding
    this.eventBus.on('coalition.ready', async (event) => {
      const coalition = event.data.coalition;
      await this.enableCoalitionBidding(coalition);
    });
    
    // Bids received - run VCG allocation
    this.eventBus.on('bidding.closed', async (event) => {
      const contract = event.data.contract;
      const bids = event.data.bids;
      
      // Separate coalition and individual bids
      const { coalitionBids, individualBids } = this.separateBids(bids);
      
      // Run allocation considering both types
      await this.runHybridAllocation(contract, coalitionBids, individualBids);
    });
    
    // Work started - orchestrate execution
    this.eventBus.on('contract.work_started', async (event) => {
      const award = event.data.award;
      
      if (award.type === 'coalition') {
        await this.orchestrator.startExecution(award);
      }
    });
  }
}

// Database migration helper
export class MultiAgentMigration {
  async up(db: Knex) {
    // Add coalition support to existing tables
    await db.schema.alterTable('contracts', (table) => {
      table.boolean('allows_coalition_bids').defaultTo(true);
      table.enum('complexity_level', ['simple', 'moderate', 'complex']);
      table.jsonb('task_decomposition');
    });
    
    await db.schema.alterTable('bids', (table) => {
      table.uuid('coalition_id').references('id').inTable('coalitions');
      table.enum('bid_type', ['individual', 'coalition']).defaultTo('individual');
    });
    
    // Create new tables (as defined in schema section)
    await this.createCoalitionTables(db);
  }
  
  async down(db: Knex) {
    // Reverse migration
    await this.dropCoalitionTables(db);
    
    await db.schema.alterTable('contracts', (table) => {
      table.dropColumn('allows_coalition_bids');
      table.dropColumn('complexity_level');
      table.dropColumn('task_decomposition');
    });
  }
}
```

## Performance Optimizations

### Caching Strategy

```typescript
export class CoalitionCache {
  private redis: Redis;
  
  // Cache synergy calculations
  async getSynergy(agentIds: string[]): Promise<number | null> {
    const key = this.synergyKey(agentIds);
    const cached = await this.redis.get(key);
    return cached ? parseFloat(cached) : null;
  }
  
  async setSynergy(agentIds: string[], synergy: number): Promise<void> {
    const key = this.synergyKey(agentIds);
    await this.redis.setex(key, 3600, synergy.toString()); // 1 hour TTL
  }
  
  // Cache Shapley calculations for common team sizes
  async getShapleyTemplate(teamSize: number): Promise<number[] | null> {
    const key = `shapley:template:${teamSize}`;
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }
  
  private synergyKey(agentIds: string[]): string {
    return `synergy:${agentIds.sort().join(':')}`;
  }
}
```

### Database Optimizations

```sql
-- Materialized view for fast synergy lookups
CREATE MATERIALIZED VIEW coalition_synergy_stats AS
SELECT 
    array_agg(DISTINCT agent_id ORDER BY agent_id) as agent_combination,
    COUNT(*) as collaboration_count,
    AVG(measured_synergy) as avg_synergy,
    AVG(quality_score) as avg_quality,
    AVG(completion_time_ratio) as avg_time_efficiency
FROM (
    SELECT 
        ch.coalition_id,
        cm.agent_id,
        sh.measured_synergy,
        sh.quality_score,
        sh.completion_time_ratio
    FROM synergy_history sh
    JOIN coalitions ch ON sh.coalition_members @> ARRAY[ch.id]
    JOIN coalition_members cm ON cm.coalition_id = ch.id
) synergy_data
GROUP BY coalition_id;

-- Index for fast coalition lookups
CREATE INDEX idx_synergy_agent_combo ON coalition_synergy_stats USING GIN(agent_combination);

-- Partial index for active coalitions
CREATE INDEX idx_active_coalitions ON coalitions(status, created_at DESC) 
WHERE status IN ('forming', 'ready', 'bidding', 'executing');
```

## Security Measures

### Anti-Collusion Detection

```typescript
export class CollusionDetector {
  async detectSuspiciousPatterns(
    coalitions: Coalition[],
    timeWindow: number
  ): Promise<SuspiciousPattern[]> {
    const patterns: SuspiciousPattern[] = [];
    
    // Check for repeated exact teams
    const teamFrequency = this.calculateTeamFrequency(coalitions);
    patterns.push(...this.flagHighFrequencyTeams(teamFrequency));
    
    // Check for bid coordination
    const bidPatterns = await this.analyzeBidPatterns(coalitions);
    patterns.push(...this.flagCoordinatedBidding(bidPatterns));
    
    // Check for artificial team rotation
    const rotationPatterns = this.detectRotationPatterns(coalitions);
    patterns.push(...this.flagSuspiciousRotations(rotationPatterns));
    
    return patterns;
  }
}
```

## Monitoring and Metrics

```typescript
export class MultiAgentMetrics {
  // Prometheus metrics
  private coalitionFormationTime = new Histogram({
    name: 'coalition_formation_duration_seconds',
    help: 'Time to form a coalition',
    labelNames: ['coalition_size', 'contract_type']
  });
  
  private synergyMultiplier = new Histogram({
    name: 'coalition_synergy_multiplier',
    help: 'Synergy multiplier distribution',
    buckets: [1.0, 1.05, 1.1, 1.15, 1.2, 1.25, 1.3]
  });
  
  private shapleyFairness = new Gauge({
    name: 'shapley_payment_fairness',
    help: 'Gini coefficient of Shapley payments',
    labelNames: ['coalition_id']
  });
  
  recordCoalitionFormation(coalition: Coalition, duration: number) {
    this.coalitionFormationTime
      .labels(coalition.members.length.toString(), coalition.contractType)
      .observe(duration);
  }
}
```

## Conclusion

These implementation specifications provide a complete technical blueprint for adding multi-agent collaboration to VibeLaunch. The modular design allows incremental rollout while maintaining backward compatibility with the existing single-agent system. Key implementation priorities:

1. Database schema extensions (Week 1-2)
2. Coalition formation APIs (Week 3-4)
3. Task decomposition engine (Week 5-6)
4. CC-VCG allocator (Week 7-8)
5. Workflow orchestration (Week 9-10)
6. Integration and testing (Week 11-12)

Total implementation effort: 12 weeks with a team of 4-5 engineers.