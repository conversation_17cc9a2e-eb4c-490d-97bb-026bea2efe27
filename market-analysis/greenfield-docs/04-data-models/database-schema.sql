-- VibeLaunch Database Schema
-- PostgreSQL 15+ with TimescaleDB extension
-- Supports multi-dimensional currency system and 10K+ TPS

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "timescaledb";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For text search
CREATE EXTENSION IF NOT EXISTS "btree_gin"; -- For composite indexes

-- Create schemas
CREATE SCHEMA IF NOT EXISTS core;
CREATE SCHEMA IF NOT EXISTS market;
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS audit;

-- Set default search path
SET search_path TO core, public;

-- =====================================================
-- CORE SCHEMA: Organizations and Users
-- =====================================================

-- Organizations (Multi-tenancy root)
CREATE TABLE core.organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('business', 'agency', 'individual')),
    
    -- Organization wallet
    wallet_id UUID UNIQUE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    settings JSONB DEFAULT '{}',
    
    -- Status
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'inactive')),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_org_status (status),
    INDEX idx_org_created (created_at DESC)
);

-- Users
CREATE TABLE core.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES core.organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    
    -- Profile
    name VARCHAR(255),
    role VARCHAR(50) DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member', 'viewer')),
    
    -- Authentication
    auth_id VARCHAR(255) UNIQUE, -- External auth provider ID
    last_login TIMESTAMPTZ,
    
    -- Status
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'invited', 'suspended', 'inactive')),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(organization_id, email),
    
    -- Indexes
    INDEX idx_user_org (organization_id),
    INDEX idx_user_email (email),
    INDEX idx_user_status (status)
);

-- =====================================================
-- CURRENCY SYSTEM: Multi-dimensional wallets
-- =====================================================

-- Wallets (Holds all 5 currencies)
CREATE TABLE core.wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    owner_id UUID NOT NULL,
    owner_type VARCHAR(50) NOT NULL CHECK (owner_type IN ('user', 'organization', 'agent', 'contract', 'protocol')),
    
    -- Five currency balances with precision
    economic_balance DECIMAL(20,6) DEFAULT 0 CHECK (economic_balance >= 0),
    quality_balance DECIMAL(10,3) DEFAULT 1.0 CHECK (quality_balance BETWEEN 0 AND 2),
    temporal_balance DECIMAL(15,3) DEFAULT 0 CHECK (temporal_balance >= 0),
    reliability_score DECIMAL(5,5) DEFAULT 0.50000 CHECK (reliability_score BETWEEN 0 AND 1),
    innovation_balance DECIMAL(15,3) DEFAULT 0 CHECK (innovation_balance >= 0),
    
    -- Temporal currency tracking
    temporal_updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Locked amounts for pending transactions
    locked_balances JSONB DEFAULT '{
        "economic": 0,
        "quality": 0,
        "temporal": 0,
        "innovation": 0
    }'::jsonb,
    
    -- Yield tracking for reliability
    last_yield_claim TIMESTAMPTZ DEFAULT NOW(),
    
    -- Optimistic locking
    version INTEGER DEFAULT 0,
    
    -- Status
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'frozen', 'closed')),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes for high-performance queries
    INDEX idx_wallet_owner (owner_id, owner_type),
    INDEX idx_wallet_balances_gin (
        economic_balance,
        quality_balance,
        temporal_balance,
        reliability_score,
        innovation_balance
    ),
    INDEX idx_wallet_status (status) WHERE status != 'active'
);

-- Transactions (High-frequency, partitioned by month)
CREATE TABLE core.transactions (
    id UUID DEFAULT uuid_generate_v4(),
    
    -- Wallet references
    from_wallet UUID REFERENCES core.wallets(id),
    to_wallet UUID REFERENCES core.wallets(id),
    
    -- Transaction details
    currency_type VARCHAR(20) NOT NULL CHECK (
        currency_type IN ('economic', 'quality', 'temporal', 'reliability', 'innovation')
    ),
    amount DECIMAL(20,6) NOT NULL CHECK (amount > 0),
    
    -- Transaction metadata
    type VARCHAR(50) NOT NULL CHECK (
        type IN ('transfer', 'exchange', 'fee', 'reward', 'penalty', 'escrow', 'yield')
    ),
    status VARCHAR(50) DEFAULT 'pending' CHECK (
        status IN ('pending', 'processing', 'completed', 'failed', 'reversed')
    ),
    
    -- For grouped transactions (atomic multi-currency)
    group_id UUID,
    
    -- Exchange details (if applicable)
    exchange_rate DECIMAL(20,8),
    exchange_fee DECIMAL(20,6),
    
    -- Additional context
    metadata JSONB DEFAULT '{}',
    error_message TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    
    -- Partition key
    PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (created_at);

-- Create monthly partitions (example for 2024)
CREATE TABLE core.transactions_2024_01 PARTITION OF core.transactions
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
CREATE TABLE core.transactions_2024_02 PARTITION OF core.transactions
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
-- Continue for all months...

-- Indexes on base table (inherited by partitions)
CREATE INDEX idx_tx_wallets ON core.transactions (from_wallet, to_wallet, created_at DESC);
CREATE INDEX idx_tx_status ON core.transactions (status, created_at DESC) WHERE status != 'completed';
CREATE INDEX idx_tx_group ON core.transactions (group_id) WHERE group_id IS NOT NULL;
CREATE INDEX idx_tx_currency ON core.transactions (currency_type, created_at DESC);

-- =====================================================
-- AGENT SYSTEM: AI agents and their capabilities
-- =====================================================

-- Agent Registry
CREATE TABLE core.agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    type VARCHAR(50) NOT NULL CHECK (
        type IN ('master', 'content_creator', 'seo_specialist', 'social_media', 
                'data_analyst', 'visual_designer', 'email_marketer', 'creative_director')
    ),
    
    -- Agent wallet
    wallet_id UUID UNIQUE REFERENCES core.wallets(id),
    
    -- Capabilities and configuration
    capabilities TEXT[] NOT NULL,
    llm_provider VARCHAR(50) CHECK (llm_provider IN ('openai', 'anthropic', 'google', 'local')),
    llm_config JSONB DEFAULT '{}',
    
    -- Performance metrics (denormalized for speed)
    total_contracts INTEGER DEFAULT 0,
    successful_contracts INTEGER DEFAULT 0,
    average_quality DECIMAL(3,3) DEFAULT 0,
    total_earnings JSONB DEFAULT '{
        "economic": 0,
        "temporal": 0,
        "innovation": 0
    }'::jsonb,
    
    -- Status
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'training')),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_active_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_agent_type (type),
    INDEX idx_agent_status (status),
    INDEX idx_agent_capabilities (capabilities) USING GIN,
    INDEX idx_agent_performance (average_quality DESC, successful_contracts DESC)
);

-- Agent Performance History (Time-series)
CREATE TABLE core.agent_performance (
    agent_id UUID NOT NULL REFERENCES core.agents(id) ON DELETE CASCADE,
    recorded_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Performance metrics
    contracts_completed INTEGER DEFAULT 0,
    contracts_failed INTEGER DEFAULT 0,
    average_delivery_time DECIMAL(10,2), -- hours
    quality_scores DECIMAL(3,3)[], -- Array of recent scores
    reliability_delta DECIMAL(5,5), -- Change in reliability
    
    -- Efficiency contribution
    efficiency_contribution DECIMAL(5,4),
    
    -- Currency flows
    earnings JSONB,
    costs JSONB,
    
    PRIMARY KEY (agent_id, recorded_at)
);

-- Convert to TimescaleDB hypertable
SELECT create_hypertable('core.agent_performance', 'recorded_at');

-- =====================================================
-- CONTRACT SYSTEM: Marketing work contracts
-- =====================================================

-- Contracts
CREATE TABLE core.contracts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES core.organizations(id),
    
    -- Contract details
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT[],
    deliverables JSONB DEFAULT '[]',
    
    -- Multi-currency budget
    budget_economic DECIMAL(20,6) DEFAULT 0,
    budget_quality DECIMAL(3,3) DEFAULT 1.0,
    budget_temporal DECIMAL(15,3) DEFAULT 168, -- Default 1 week
    budget_innovation DECIMAL(15,3) DEFAULT 0,
    required_reliability DECIMAL(5,5) DEFAULT 0.8,
    
    -- Escrow wallet for funds
    escrow_wallet_id UUID REFERENCES core.wallets(id),
    
    -- Timeline
    deadline TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    
    -- Status
    status VARCHAR(50) DEFAULT 'draft' CHECK (
        status IN ('draft', 'open', 'bidding', 'in_progress', 'review', 'completed', 'cancelled', 'disputed')
    ),
    
    -- Selected bid
    selected_bid_id UUID,
    
    -- Performance tracking
    actual_quality DECIMAL(3,3),
    actual_delivery_time DECIMAL(10,2), -- hours
    efficiency_impact DECIMAL(5,4),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    tags TEXT[],
    
    -- Indexes
    INDEX idx_contract_org (organization_id),
    INDEX idx_contract_status (status),
    INDEX idx_contract_deadline (deadline),
    INDEX idx_contract_created (created_at DESC),
    INDEX idx_contract_tags (tags) USING GIN,
    
    -- Full text search
    search_vector tsvector GENERATED ALWAYS AS (
        setweight(to_tsvector('english', coalesce(title, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(description, '')), 'B')
    ) STORED,
    INDEX idx_contract_search (search_vector) USING GIN
);

-- Bids
CREATE TABLE core.bids (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    contract_id UUID NOT NULL REFERENCES core.contracts(id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES core.agents(id),
    
    -- Bid pricing (multi-currency)
    price_economic DECIMAL(20,6) NOT NULL,
    price_quality DECIMAL(3,3) NOT NULL,
    price_temporal DECIMAL(15,3) NOT NULL, -- Delivery time in hours
    price_innovation DECIMAL(15,3) DEFAULT 0,
    
    -- Bid details
    proposal TEXT NOT NULL,
    approach JSONB DEFAULT '{}',
    
    -- Team composition (for collaborative bids)
    team_agent_ids UUID[],
    synergy_score DECIMAL(5,3) DEFAULT 1.0,
    
    -- Scoring
    total_score DECIMAL(10,6), -- Calculated from multiple factors
    efficiency_score DECIMAL(5,4),
    
    -- Status
    status VARCHAR(50) DEFAULT 'pending' CHECK (
        status IN ('pending', 'accepted', 'rejected', 'withdrawn', 'expired')
    ),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(contract_id, agent_id),
    
    -- Indexes
    INDEX idx_bid_contract (contract_id),
    INDEX idx_bid_agent (agent_id),
    INDEX idx_bid_status (status),
    INDEX idx_bid_score (total_score DESC) WHERE status = 'pending'
);

-- =====================================================
-- MARKET SYSTEM: Exchange and price discovery
-- =====================================================

-- Currency Pairs
CREATE TABLE market.currency_pairs (
    id VARCHAR(20) PRIMARY KEY, -- e.g., "ECO_QUA"
    base_currency VARCHAR(20) NOT NULL,
    quote_currency VARCHAR(20) NOT NULL,
    
    -- Market parameters
    tick_size DECIMAL(10,8) DEFAULT 0.0001,
    min_order_size DECIMAL(20,8) DEFAULT 0.01,
    maker_fee DECIMAL(6,6) DEFAULT 0.001,
    taker_fee DECIMAL(6,6) DEFAULT 0.002,
    
    -- Status
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'delisted')),
    
    -- Statistics (denormalized)
    last_price DECIMAL(20,8),
    volume_24h DECIMAL(20,8) DEFAULT 0,
    high_24h DECIMAL(20,8),
    low_24h DECIMAL(20,8),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(base_currency, quote_currency),
    CHECK (base_currency != quote_currency)
);

-- Order Book (Active orders)
CREATE TABLE market.orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES core.wallets(id),
    pair_id VARCHAR(20) NOT NULL REFERENCES market.currency_pairs(id),
    
    -- Order details
    side VARCHAR(10) NOT NULL CHECK (side IN ('buy', 'sell')),
    type VARCHAR(20) NOT NULL CHECK (type IN ('market', 'limit', 'stop', 'stop_limit')),
    price DECIMAL(20,8),
    quantity DECIMAL(20,8) NOT NULL,
    filled_quantity DECIMAL(20,8) DEFAULT 0,
    
    -- Order status
    status VARCHAR(20) DEFAULT 'open' CHECK (
        status IN ('open', 'partial', 'filled', 'cancelled', 'expired', 'rejected')
    ),
    
    -- Stop order parameters
    stop_price DECIMAL(20,8),
    
    -- Time in force
    time_in_force VARCHAR(20) DEFAULT 'GTC' CHECK (
        time_in_force IN ('GTC', 'IOC', 'FOK', 'GTD')
    ),
    expire_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes for order matching
    INDEX idx_order_pair_status (pair_id, status) WHERE status IN ('open', 'partial'),
    INDEX idx_order_price (pair_id, side, price) WHERE status IN ('open', 'partial')
);

-- Trade History
CREATE TABLE market.trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pair_id VARCHAR(20) NOT NULL REFERENCES market.currency_pairs(id),
    
    -- Order references
    maker_order_id UUID NOT NULL REFERENCES market.orders(id),
    taker_order_id UUID NOT NULL REFERENCES market.orders(id),
    
    -- Trade details
    price DECIMAL(20,8) NOT NULL,
    quantity DECIMAL(20,8) NOT NULL,
    maker_fee DECIMAL(20,8),
    taker_fee DECIMAL(20,8),
    
    -- Timestamps
    executed_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_trade_pair_time (pair_id, executed_at DESC),
    INDEX idx_trade_orders (maker_order_id, taker_order_id)
);

-- Market Ticks (High-frequency price data)
CREATE TABLE market.ticks (
    pair_id VARCHAR(20) NOT NULL,
    time TIMESTAMPTZ NOT NULL,
    
    -- OHLCV data
    open DECIMAL(20,8),
    high DECIMAL(20,8),
    low DECIMAL(20,8),
    close DECIMAL(20,8),
    volume DECIMAL(20,8),
    
    -- Order book snapshot
    best_bid DECIMAL(20,8),
    best_ask DECIMAL(20,8),
    bid_volume DECIMAL(20,8),
    ask_volume DECIMAL(20,8),
    
    PRIMARY KEY (pair_id, time)
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('market.ticks', 'time');

-- Add compression
ALTER TABLE market.ticks SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'pair_id',
    timescaledb.compress_orderby = 'time DESC'
);

-- =====================================================
-- ANALYTICS SCHEMA: Metrics and reporting
-- =====================================================

-- System Efficiency Metrics
CREATE TABLE analytics.efficiency_metrics (
    time TIMESTAMPTZ NOT NULL,
    
    -- Overall efficiency
    overall_efficiency DECIMAL(5,4),
    
    -- Component efficiencies
    price_discovery_efficiency DECIMAL(5,4),
    allocation_efficiency DECIMAL(5,4),
    execution_efficiency DECIMAL(5,4),
    information_efficiency DECIMAL(5,4),
    innovation_efficiency DECIMAL(5,4),
    
    -- Volume metrics
    contracts_created INTEGER,
    contracts_completed INTEGER,
    total_transaction_volume JSONB,
    
    -- Market depth
    average_bid_count DECIMAL(10,2),
    average_spread DECIMAL(10,6),
    
    -- System health
    active_agents INTEGER,
    active_users INTEGER,
    avg_response_time_ms DECIMAL(10,2),
    
    PRIMARY KEY (time)
);

-- Convert to hypertable
SELECT create_hypertable('analytics.efficiency_metrics', 'time');

-- Team Synergy Analytics
CREATE TABLE analytics.team_performance (
    team_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_ids UUID[] NOT NULL,
    
    -- Performance metrics
    contracts_completed INTEGER DEFAULT 0,
    average_synergy DECIMAL(5,3),
    peak_synergy DECIMAL(5,3),
    
    -- Efficiency contribution
    efficiency_impact DECIMAL(5,4),
    
    -- Timestamps
    formed_at TIMESTAMPTZ DEFAULT NOW(),
    last_active_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- AUDIT SCHEMA: Compliance and tracking
-- =====================================================

-- Audit Log (Immutable)
CREATE TABLE audit.activity_log (
    id BIGSERIAL PRIMARY KEY,
    
    -- Actor
    user_id UUID,
    organization_id UUID,
    ip_address INET,
    user_agent TEXT,
    
    -- Action
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id UUID,
    
    -- Details
    changes JSONB,
    metadata JSONB,
    
    -- Result
    status VARCHAR(20) CHECK (status IN ('success', 'failure', 'error')),
    error_message TEXT,
    
    -- Timestamp
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_audit_user (user_id, created_at DESC),
    INDEX idx_audit_org (organization_id, created_at DESC),
    INDEX idx_audit_resource (resource_type, resource_id, created_at DESC),
    INDEX idx_audit_action (action, created_at DESC)
);

-- Make audit log append-only
CREATE RULE audit_log_no_update AS ON UPDATE TO audit.activity_log DO INSTEAD NOTHING;
CREATE RULE audit_log_no_delete AS ON DELETE TO audit.activity_log DO INSTEAD NOTHING;

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Update timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update trigger to all tables with updated_at
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON core.organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON core.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wallets_updated_at BEFORE UPDATE ON core.wallets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- Continue for all tables...

-- Temporal currency decay function
CREATE OR REPLACE FUNCTION calculate_temporal_decay(
    original_amount DECIMAL,
    last_update TIMESTAMPTZ
) RETURNS DECIMAL AS $$
DECLARE
    hours_elapsed DECIMAL;
    lambda CONSTANT DECIMAL := 0.00413; -- Decay constant for 1-week half-life
    decayed_amount DECIMAL;
BEGIN
    hours_elapsed := EXTRACT(EPOCH FROM (NOW() - last_update)) / 3600;
    decayed_amount := original_amount * exp(-lambda * hours_elapsed);
    
    -- Apply floor value
    IF decayed_amount < 0.01 THEN
        decayed_amount := 0.01;
    END IF;
    
    RETURN decayed_amount;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Multi-currency transfer function
CREATE OR REPLACE FUNCTION transfer_currency(
    p_from_wallet UUID,
    p_to_wallet UUID,
    p_currency VARCHAR,
    p_amount DECIMAL
) RETURNS UUID AS $$
DECLARE
    v_transaction_id UUID;
    v_from_balance DECIMAL;
BEGIN
    -- Start transaction
    v_transaction_id := uuid_generate_v4();
    
    -- Lock wallets
    PERFORM 1 FROM core.wallets WHERE id = p_from_wallet FOR UPDATE;
    PERFORM 1 FROM core.wallets WHERE id = p_to_wallet FOR UPDATE;
    
    -- Get current balance
    EXECUTE format('SELECT %I FROM core.wallets WHERE id = $1', p_currency || '_balance')
    INTO v_from_balance
    USING p_from_wallet;
    
    -- Apply decay for temporal currency
    IF p_currency = 'temporal' THEN
        SELECT calculate_temporal_decay(v_from_balance, temporal_updated_at)
        INTO v_from_balance
        FROM core.wallets
        WHERE id = p_from_wallet;
    END IF;
    
    -- Check sufficient balance
    IF v_from_balance < p_amount THEN
        RAISE EXCEPTION 'Insufficient funds: % available, % requested', 
            v_from_balance, p_amount;
    END IF;
    
    -- Update balances
    EXECUTE format('UPDATE core.wallets SET %I = %I - $1 WHERE id = $2', 
        p_currency || '_balance', p_currency || '_balance')
    USING p_amount, p_from_wallet;
    
    EXECUTE format('UPDATE core.wallets SET %I = %I + $1 WHERE id = $2',
        p_currency || '_balance', p_currency || '_balance')
    USING p_amount, p_to_wallet;
    
    -- Update temporal timestamp if needed
    IF p_currency = 'temporal' THEN
        UPDATE core.wallets 
        SET temporal_updated_at = NOW() 
        WHERE id IN (p_from_wallet, p_to_wallet);
    END IF;
    
    -- Record transaction
    INSERT INTO core.transactions (
        id, from_wallet, to_wallet, currency_type, 
        amount, type, status, completed_at
    ) VALUES (
        v_transaction_id, p_from_wallet, p_to_wallet, p_currency,
        p_amount, 'transfer', 'completed', NOW()
    );
    
    RETURN v_transaction_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Partial indexes for common queries
CREATE INDEX idx_active_contracts ON core.contracts (organization_id, created_at DESC) 
    WHERE status IN ('open', 'bidding');
    
CREATE INDEX idx_pending_bids ON core.bids (contract_id, total_score DESC) 
    WHERE status = 'pending';
    
CREATE INDEX idx_active_agents ON core.agents (type, average_quality DESC) 
    WHERE status = 'active';

-- Composite indexes for join operations
CREATE INDEX idx_wallet_transactions ON core.transactions 
    (from_wallet, created_at DESC) 
    INCLUDE (to_wallet, currency_type, amount);

-- BRIN indexes for time-series data
CREATE INDEX idx_ticks_time_brin ON market.ticks 
    USING BRIN (time) WITH (pages_per_range = 128);

-- =====================================================
-- MATERIALIZED VIEWS FOR ANALYTICS
-- =====================================================

-- Agent leaderboard
CREATE MATERIALIZED VIEW analytics.agent_leaderboard AS
SELECT 
    a.id,
    a.name,
    a.type,
    w.reliability_score,
    a.average_quality,
    a.successful_contracts,
    a.total_earnings,
    RANK() OVER (PARTITION BY a.type ORDER BY w.reliability_score DESC) as reliability_rank,
    RANK() OVER (PARTITION BY a.type ORDER BY a.average_quality DESC) as quality_rank
FROM core.agents a
JOIN core.wallets w ON a.wallet_id = w.id
WHERE a.status = 'active';

CREATE UNIQUE INDEX ON analytics.agent_leaderboard (id);

-- Refresh schedule
CREATE OR REPLACE FUNCTION refresh_materialized_views() RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY analytics.agent_leaderboard;
    -- Add other materialized views here
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SECURITY: Row-Level Security Policies
-- =====================================================

-- Enable RLS on core tables
ALTER TABLE core.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.bids ENABLE ROW LEVEL SECURITY;

-- Organization isolation policy
CREATE POLICY org_isolation ON core.organizations
    FOR ALL
    USING (id = current_setting('app.current_organization_id')::uuid);

-- User access policy
CREATE POLICY user_org_access ON core.users
    FOR ALL
    USING (organization_id = current_setting('app.current_organization_id')::uuid);

-- Contract visibility policy
CREATE POLICY contract_visibility ON core.contracts
    FOR SELECT
    USING (
        organization_id = current_setting('app.current_organization_id')::uuid
        OR status IN ('open', 'bidding') -- Public contracts
    );

-- =====================================================
-- PERFORMANCE MONITORING
-- =====================================================

-- Query performance tracking
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Table size monitoring
CREATE VIEW analytics.table_sizes AS
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size,
    pg_total_relation_size(schemaname||'.'||tablename) AS size_bytes
FROM pg_tables
WHERE schemaname IN ('core', 'market', 'analytics', 'audit')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Connection monitoring
CREATE VIEW analytics.connection_stats AS
SELECT
    datname,
    usename,
    application_name,
    client_addr,
    state,
    COUNT(*) as connection_count
FROM pg_stat_activity
GROUP BY datname, usename, application_name, client_addr, state;

-- =====================================================
-- MAINTENANCE PROCEDURES
-- =====================================================

-- Partition maintenance
CREATE OR REPLACE FUNCTION create_monthly_partitions()
RETURNS void AS $$
DECLARE
    start_date date;
    end_date date;
    partition_name text;
BEGIN
    -- Create partitions for next 3 months
    FOR i IN 0..2 LOOP
        start_date := date_trunc('month', CURRENT_DATE + (i || ' months')::interval);
        end_date := start_date + '1 month'::interval;
        partition_name := 'transactions_' || to_char(start_date, 'YYYY_MM');
        
        -- Check if partition exists
        IF NOT EXISTS (
            SELECT 1 FROM pg_tables 
            WHERE schemaname = 'core' 
            AND tablename = partition_name
        ) THEN
            EXECUTE format(
                'CREATE TABLE core.%I PARTITION OF core.transactions
                FOR VALUES FROM (%L) TO (%L)',
                partition_name, start_date, end_date
            );
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Schedule partition creation (requires pg_cron)
-- SELECT cron.schedule('create-partitions', '0 0 1 * *', 'SELECT create_monthly_partitions()');

-- =====================================================
-- SAMPLE DATA FOR DEVELOPMENT
-- =====================================================

-- Note: Only run in development environments
/*
-- Create test organization
INSERT INTO core.organizations (id, name, type) VALUES 
    ('00000000-0000-0000-0000-000000000000', 'Test Organization', 'business');

-- Create test wallets
INSERT INTO core.wallets (owner_id, owner_type, economic_balance) VALUES
    ('00000000-0000-0000-0000-000000000000', 'organization', 10000);

-- Create test agents
INSERT INTO core.agents (name, type, capabilities) VALUES
    ('Content Creator Pro', 'content_creator', ARRAY['blog', 'article', 'newsletter']),
    ('SEO Master', 'seo_specialist', ARRAY['keyword_research', 'optimization']),
    ('Social Expert', 'social_media', ARRAY['facebook', 'twitter', 'linkedin']);
*/