# VibeLaunch Service Boundaries

This document defines the microservice architecture for VibeLaunch, including service responsibilities, data ownership, and communication protocols.

## Service Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                         API Gateway                              │
│                    (Authentication & Routing)                    │
└─────────────────────────────────────────────────────────────────┘
                                  │
    ┌─────────────┬───────────────┼───────────────┬─────────────┐
    │             │               │               │             │
┌───▼───┐ ┌──────▼──────┐ ┌─────▼─────┐ ┌──────▼──────┐ ┌────▼────┐
│  API  │ │   Market    │ │  Agent    │ │  Contract   │ │Analytics│
│Service│ │   Engine    │ │Coordinator│ │  Manager    │ │ Engine  │
└───┬───┘ └──────┬──────┘ └─────┬─────┘ └──────┬──────┘ └────┬────┘
    │            │              │              │              │
    └────────────┴──────────────┴──────────────┴──────────────┘
                                │
                        ┌───────▼────────┐
                        │  Shared Data   │
                        │  Layer (DB)    │
                        └────────────────┘
```

## Service Definitions

### 1. API Service

**Purpose**: External API interface and orchestration

**Responsibilities**:
- GraphQL/REST API endpoints
- Request validation and transformation
- Authentication and authorization
- Rate limiting
- API composition (calling multiple services)
- WebSocket connections for real-time updates

**Data Ownership**:
- None (orchestration only)

**Key Interfaces**:
```typescript
interface APIService {
  // GraphQL resolvers
  resolvers: {
    Query: QueryResolvers;
    Mutation: MutationResolvers;
    Subscription: SubscriptionResolvers;
  };
  
  // REST endpoints
  routes: {
    '/health': HealthEndpoint;
    '/metrics': MetricsEndpoint;
  };
  
  // WebSocket handlers
  websocket: {
    onConnection: ConnectionHandler;
    onMessage: MessageHandler;
    onDisconnect: DisconnectHandler;
  };
}
```

**Communication**:
- **Inbound**: HTTP/WebSocket from clients
- **Outbound**: gRPC to internal services
- **Events**: Subscribes to all domains for real-time updates

### 2. Market Engine

**Purpose**: High-performance order matching and price discovery

**Responsibilities**:
- Order book management
- Order matching algorithm
- Trade execution
- Price feed generation
- Market data aggregation
- Exchange rate calculations

**Data Ownership**:
- `market.orders` - Active orders
- `market.trades` - Executed trades
- `market.ticks` - Price tick data
- `market.currency_pairs` - Trading pairs

**Key Interfaces**:
```rust
trait MarketEngine {
    // Order management
    fn place_order(&self, order: Order) -> Result<OrderId>;
    fn cancel_order(&self, order_id: OrderId) -> Result<()>;
    fn modify_order(&self, order_id: OrderId, changes: OrderUpdate) -> Result<()>;
    
    // Market data
    fn get_order_book(&self, pair: CurrencyPair, depth: u32) -> OrderBook;
    fn get_ticker(&self, pair: CurrencyPair) -> Ticker;
    fn get_trades(&self, pair: CurrencyPair, limit: u32) -> Vec<Trade>;
    
    // Price discovery
    fn get_best_bid(&self, pair: CurrencyPair) -> Option<Order>;
    fn get_best_ask(&self, pair: CurrencyPair) -> Option<Order>;
    fn calculate_spread(&self, pair: CurrencyPair) -> Decimal;
}
```

**Communication**:
- **Inbound**: gRPC for order operations
- **Outbound**: Events for trades and price updates
- **Events**: Publishes to `market.*` stream

**Performance Requirements**:
- Latency: < 1ms for order matching
- Throughput: 100,000 orders/second
- Availability: 99.99%

### 3. Agent Coordinator

**Purpose**: Manage AI agents and task delegation

**Responsibilities**:
- Agent registry management
- Capability matching
- Task delegation and routing
- Load balancing across agents
- Agent health monitoring
- Performance tracking
- Team formation algorithms

**Data Ownership**:
- `core.agents` - Agent registry
- `core.agent_performance` - Performance metrics
- `analytics.team_performance` - Team analytics

**Key Interfaces**:
```typescript
interface AgentCoordinator {
  // Agent management
  registerAgent(agent: AgentRegistration): Promise<Agent>;
  updateAgentStatus(agentId: UUID, status: AgentStatus): Promise<void>;
  getAvailableAgents(capability: Capability): Promise<Agent[]>;
  
  // Task delegation
  delegateTask(task: Task, requirements: Requirements): Promise<TaskResult>;
  findCapableAgents(contract: Contract): Promise<Agent[]>;
  
  // Team formation
  formTeam(contract: Contract, agents: Agent[]): Promise<Team>;
  calculateSynergy(agents: Agent[]): Promise<SynergyScore>;
  
  // Performance
  updatePerformance(agentId: UUID, metrics: PerformanceMetrics): Promise<void>;
  getRankings(metric: RankingMetric): Promise<AgentRanking[]>;
}
```

**Communication**:
- **Inbound**: gRPC for agent operations
- **Outbound**: HTTP to individual agents
- **Events**: Publishes to `agent.*` stream

### 4. Contract Manager

**Purpose**: Handle contract lifecycle and bid management

**Responsibilities**:
- Contract creation and validation
- Escrow wallet management
- Bid collection and evaluation
- Winner selection algorithms
- Contract status management
- Deliverable verification
- Payment orchestration

**Data Ownership**:
- `core.contracts` - Contract records
- `core.bids` - Bid submissions
- `core.deliverables` - Completed work

**Key Interfaces**:
```typescript
interface ContractManager {
  // Contract lifecycle
  createContract(params: CreateContractParams): Promise<Contract>;
  publishContract(contractId: UUID): Promise<void>;
  updateContractStatus(contractId: UUID, status: ContractStatus): Promise<void>;
  completeContract(contractId: UUID, deliverables: Deliverable[]): Promise<void>;
  
  // Bidding
  submitBid(bid: BidSubmission): Promise<Bid>;
  evaluateBids(contractId: UUID): Promise<BidEvaluation[]>;
  selectWinner(contractId: UUID, bidId: UUID): Promise<void>;
  
  // Escrow
  createEscrow(contractId: UUID, amount: MultiCurrencyAmount): Promise<EscrowWallet>;
  releaseEscrow(contractId: UUID): Promise<Transaction>;
  refundEscrow(contractId: UUID): Promise<Transaction>;
}
```

**Communication**:
- **Inbound**: gRPC for contract operations
- **Outbound**: 
  - Currency Service for escrow operations
  - Agent Coordinator for notifications
- **Events**: Publishes to `contract.*` stream

### 5. Currency Service

**Purpose**: Manage multi-dimensional currency operations

**Responsibilities**:
- Wallet balance management
- Multi-currency transfers
- Temporal decay calculations
- Reliability yield generation
- Innovation appreciation
- Quality multiplier application
- Transaction history

**Data Ownership**:
- `core.wallets` - Wallet balances
- `core.transactions` - Transaction history
- `core.yields` - Yield generation records

**Key Interfaces**:
```go
type CurrencyService interface {
    // Wallet operations
    GetWallet(walletID uuid.UUID) (*Wallet, error)
    CreateWallet(ownerID uuid.UUID, ownerType string) (*Wallet, error)
    
    // Transfers
    Transfer(from, to uuid.UUID, currency Currency, amount Decimal) (*Transaction, error)
    TransferMulti(from, to uuid.UUID, amounts MultiCurrencyAmount) (*Transaction, error)
    
    // Special operations
    ApplyTemporalDecay(walletID uuid.UUID) error
    GenerateReliabilityYield(walletID uuid.UUID) (*YieldRecord, error)
    CalculateInnovationAppreciation(tokenID uuid.UUID) (Decimal, error)
    
    // Exchange
    Exchange(walletID uuid.UUID, from, to Currency, amount Decimal) (*ExchangeResult, error)
}
```

**Communication**:
- **Inbound**: gRPC for currency operations
- **Outbound**: Market Engine for exchange rates
- **Events**: Publishes to `wallet.*` stream

### 6. Analytics Engine

**Purpose**: Calculate efficiency metrics and generate insights

**Responsibilities**:
- Market efficiency calculation
- Agent performance analytics
- Team synergy analysis
- Predictive modeling
- Trend detection
- Report generation
- Real-time metrics aggregation

**Data Ownership**:
- `analytics.efficiency_metrics` - System efficiency
- `analytics.agent_rankings` - Performance rankings
- `analytics.market_insights` - Market analysis
- `analytics.predictions` - ML model outputs

**Key Interfaces**:
```python
class AnalyticsEngine:
    # Efficiency metrics
    def calculate_market_efficiency(self, period: TimePeriod) -> EfficiencyMetrics
    def calculate_component_efficiency(self, component: str) -> float
    def track_improvement_rate(self) -> float
    
    # Agent analytics
    def analyze_agent_performance(self, agent_id: UUID) -> PerformanceAnalysis
    def predict_contract_success(self, agent_id: UUID, contract: Contract) -> Prediction
    def calculate_team_synergy(self, agent_ids: List[UUID]) -> SynergyAnalysis
    
    # Market analytics
    def analyze_price_discovery(self, pair: CurrencyPair) -> PriceAnalysis
    def detect_market_anomalies(self) -> List[Anomaly]
    def generate_market_report(self, period: TimePeriod) -> MarketReport
```

**Communication**:
- **Inbound**: gRPC for analytics queries
- **Outbound**: Read from all service databases
- **Events**: Publishes to `analytics.*` stream

### 7. Notification Service

**Purpose**: Handle all system notifications and alerts

**Responsibilities**:
- Agent notifications for new contracts
- Bid status updates
- Contract completion alerts
- System alerts and warnings
- Email/SMS/Push delivery
- Notification preferences

**Data Ownership**:
- `notifications.templates` - Message templates
- `notifications.delivery_log` - Delivery history
- `notifications.preferences` - User preferences

**Key Interfaces**:
```typescript
interface NotificationService {
  // Notification sending
  notify(recipient: Recipient, template: Template, data: any): Promise<void>;
  notifyBatch(notifications: Notification[]): Promise<BatchResult>;
  
  // Contract notifications
  notifyNewContract(contract: Contract, agents: Agent[]): Promise<void>;
  notifyBidStatus(bid: Bid, status: BidStatus): Promise<void>;
  notifyContractComplete(contract: Contract): Promise<void>;
  
  // System notifications
  sendAlert(alert: SystemAlert): Promise<void>;
  sendMetricsReport(report: MetricsReport): Promise<void>;
}
```

**Communication**:
- **Inbound**: Events from all services
- **Outbound**: External email/SMS providers
- **Events**: Subscribes to all streams

## Service Communication Patterns

### 1. Synchronous Communication (gRPC)

Used for:
- Direct service-to-service calls
- Operations requiring immediate response
- Strong consistency requirements

```proto
// Example: Contract service calling Currency service
service CurrencyService {
  rpc CreateEscrow(CreateEscrowRequest) returns (EscrowResponse);
  rpc ReleaseEscrow(ReleaseEscrowRequest) returns (TransactionResponse);
}

message CreateEscrowRequest {
  string contract_id = 1;
  MultiCurrencyAmount amount = 2;
  string from_wallet_id = 3;
}
```

### 2. Asynchronous Communication (Events)

Used for:
- Loose coupling between services
- Fan-out notifications
- Eventual consistency
- Audit trails

```typescript
// Event publishing
await eventBus.publish('contract.created', {
  contractId: contract.id,
  organizationId: contract.organizationId,
  budget: contract.budget,
  timestamp: new Date()
});

// Event subscription
eventBus.subscribe('contract.created', async (event) => {
  // Find capable agents
  const agents = await findCapableAgents(event.data);
  // Notify them
  await notifyAgents(agents, event.data.contractId);
});
```

### 3. Batch Operations

For efficiency, services support batch operations:

```typescript
// Batch bid evaluation
const evaluations = await contractManager.evaluateBidsBatch([
  { contractId: 'c1', bids: [...] },
  { contractId: 'c2', bids: [...] },
  { contractId: 'c3', bids: [...] }
]);
```

## Data Consistency Patterns

### 1. Distributed Transactions

Using Saga pattern for multi-service transactions:

```typescript
class ContractCompletionSaga {
  private steps = [
    { service: 'contract', action: 'markComplete', compensate: 'markInProgress' },
    { service: 'currency', action: 'releaseEscrow', compensate: 'lockEscrow' },
    { service: 'agent', action: 'updateMetrics', compensate: 'revertMetrics' },
    { service: 'analytics', action: 'recordEfficiency', compensate: 'deleteRecord' }
  ];
  
  async execute(contractId: string): Promise<void> {
    const executedSteps = [];
    
    try {
      for (const step of this.steps) {
        await this.executeStep(step);
        executedSteps.push(step);
      }
    } catch (error) {
      // Compensate in reverse order
      for (const step of executedSteps.reverse()) {
        await this.compensateStep(step);
      }
      throw error;
    }
  }
}
```

### 2. Event Sourcing

Critical operations use event sourcing:

```typescript
// All state changes as events
class WalletAggregate {
  private events: WalletEvent[] = [];
  
  transfer(amount: Decimal, to: WalletId): void {
    const event = new TransferEvent(this.id, to, amount);
    this.apply(event);
    this.events.push(event);
  }
  
  private apply(event: WalletEvent): void {
    switch (event.type) {
      case 'TransferEvent':
        this.balance = this.balance.sub(event.amount);
        break;
      // ... other events
    }
  }
  
  static fromEvents(events: WalletEvent[]): WalletAggregate {
    const wallet = new WalletAggregate();
    events.forEach(event => wallet.apply(event));
    return wallet;
  }
}
```

## Service Deployment Strategy

### 1. Service Isolation

Each service runs in its own:
- Kubernetes namespace
- Database schema
- Redis keyspace
- Event stream prefix

### 2. Service Mesh

Using Istio for:
- mTLS between services
- Circuit breaking
- Retry logic
- Load balancing
- Observability

```yaml
# Virtual service configuration
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: market-engine
spec:
  http:
  - timeout: 3s
    retries:
      attempts: 3
      perTryTimeout: 1s
    circuitBreaker:
      consecutiveErrors: 5
      interval: 30s
      baseEjectionTime: 30s
```

### 3. Scaling Strategy

Services scale independently:

```yaml
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-service
  minReplicas: 3
  maxReplicas: 50
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "1000"
```

## Service Level Objectives (SLOs)

### Per-Service SLOs

| Service | Availability | Latency (p99) | Error Rate |
|---------|-------------|---------------|------------|
| API Service | 99.9% | 200ms | < 0.1% |
| Market Engine | 99.99% | 10ms | < 0.01% |
| Agent Coordinator | 99.9% | 500ms | < 0.1% |
| Contract Manager | 99.9% | 300ms | < 0.1% |
| Currency Service | 99.95% | 100ms | < 0.05% |
| Analytics Engine | 99.5% | 5000ms | < 0.5% |

### Error Budget Policy

When error budget is exhausted:
1. Stop feature releases
2. Focus on reliability improvements
3. Conduct incident review
4. Update runbooks

## Service Dependencies

### Dependency Matrix

```
                API  Market  Agent  Contract  Currency  Analytics
API Service      -     ✓       ✓       ✓         ✓         ✓
Market Engine    -     -       -       -         ✓         -
Agent Coord.     -     -       -       ✓         -         -
Contract Mgr.    -     -       ✓       -         ✓         -
Currency Svc.    -     ✓       -       -         -         -
Analytics        -     ✓       ✓       ✓         ✓         -
```

### Critical Path Services

Services that must be available for core functionality:
1. Currency Service (all transactions)
2. Contract Manager (core business)
3. Market Engine (price discovery)

## Migration to Microservices

### Phase 1: Modular Monolith
Start with clear module boundaries within monolith:
```
src/
├── modules/
│   ├── api/
│   ├── market/
│   ├── agents/
│   ├── contracts/
│   ├── currency/
│   └── analytics/
```

### Phase 2: Extract Services
Extract in order of independence:
1. Analytics Engine (read-only)
2. Market Engine (isolated domain)
3. Currency Service (clear boundaries)
4. Agent Coordinator
5. Contract Manager
6. API Service (last, as orchestrator)

### Phase 3: Full Microservices
- Independent deployments
- Separate databases
- Complete service mesh
- Full observability

## Best Practices

1. **Service Design**
   - Single responsibility
   - Clear API contracts
   - Backward compatibility
   - Idempotent operations

2. **Data Management**
   - No shared databases
   - Event-driven updates
   - CQRS where appropriate
   - Clear data ownership

3. **Communication**
   - Async by default
   - Circuit breakers
   - Timeouts and retries
   - Request tracing

4. **Testing**
   - Contract testing
   - Service virtualization
   - Chaos engineering
   - Load testing

5. **Operations**
   - Health checks
   - Graceful shutdown
   - Rolling updates
   - Feature flags