# Multi-Currency Transaction Examples

This directory contains examples demonstrating VibeLaunch's revolutionary five-currency system in action. Each currency has unique properties that create a multi-dimensional value system.

## The Five Currencies

1. **Economic (₥)** - Traditional monetary value
2. **Quality (◈)** - Excellence that multiplies other values
3. **Temporal (⧗)** - Time-based value that decays
4. **Reliability (☆)** - Trust that generates yields
5. **Innovation (◊)** - Creativity that appreciates

## Currency Properties

| Currency | Transferable | Divisible | Fungible | Special Property |
|----------|-------------|-----------|----------|-----------------|
| Economic | ✓ | ✓ | ✓ | Base value |
| Quality | ✗ | ✓ | ✗ | Multiplicative effects |
| Temporal | ✓ | ✓ | ✓ | Exponential decay |
| Reliability | ✗ | ✓ | ✗ | Generates yields |
| Innovation | ✓ | ✓ | ✗ | Appreciates over time |

## Example Files

1. **[simple-transfer.ts](./simple-transfer.ts)** - Basic economic currency transfer
2. **[temporal-decay.ts](./temporal-decay.ts)** - Time-based value calculations
3. **[quality-multiplier.ts](./quality-multiplier.ts)** - How quality affects other currencies
4. **[reliability-yield.ts](./reliability-yield.ts)** - Trust score yield generation
5. **[innovation-appreciation.ts](./innovation-appreciation.ts)** - Innovation value growth
6. **[currency-exchange.ts](./currency-exchange.ts)** - Multi-currency exchange example
7. **[complex-transaction.ts](./complex-transaction.ts)** - Complete multi-currency flow

## Key Concepts

### Temporal Decay

Temporal currency loses value over time to incentivize quick action:

```typescript
// Half-life of 1 week
const currentValue = originalValue * Math.exp(-0.00413 * hoursElapsed);
```

### Quality Multipliers

Quality affects the value of other currencies:

```typescript
const qualityMultipliers = {
  economic: (q) => 1.0 + (q * 0.5),      // Up to 2x at max quality
  temporal: (q) => 1.0 / (2.0 - q),      // Quality speeds delivery
  innovation: (q) => Math.pow(q, 1.5)     // Exponential innovation boost
};
```

### Reliability Yields

High reliability scores generate passive income:

```typescript
// Daily yield based on reliability score
const dailyYield = principal * reliabilityScore * 0.001; // 0.1% daily
```

### Innovation Appreciation

Innovation tokens gain value based on adoption:

```typescript
// Network effect appreciation
const appreciationRate = 0.02 + Math.log10(adopters + 1) * 0.01;
```

## Transaction Flow

```
┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│   Wallet A   │────▶│ Transaction  │────▶│   Wallet B   │
│              │     │   Engine     │     │              │
│ ₥ 1000       │     │              │     │ ₥ 0          │
│ ⧗ 168        │     │ • Validate   │     │ ⧗ 0          │
│ ◊ 100        │     │ • Apply Decay│     │ ◊ 0          │
└──────────────┘     │ • Transfer   │     └──────────────┘
                     │ • Emit Events│
                     └──────────────┘
```

## Running Examples

```bash
# Install dependencies
pnpm install

# Run individual examples
pnpm tsx examples/multi-currency-transaction/simple-transfer.ts
pnpm tsx examples/multi-currency-transaction/temporal-decay.ts
# etc...

# Run all examples
pnpm tsx examples/multi-currency-transaction/run-all.ts
```

## Common Patterns

### Pattern 1: Time-Sensitive Payment
```typescript
// Urgent task with temporal bonus
const payment = {
  economic: 500,
  temporal: 24, // 24-hour bonus
  quality: 1.5  // 50% quality bonus
};
```

### Pattern 2: Trust-Building Transaction
```typescript
// First-time agent needs to build trust
const escrow = {
  economic: 1000,
  reliability: 0.5, // Minimum threshold
  temporal: 168    // 1 week to complete
};
```

### Pattern 3: Innovation Reward
```typescript
// Reward creative solution
const innovationBonus = {
  economic: 200,
  innovation: 500,  // High innovation reward
  quality: 1.8      // Must be high quality
};
```

## Currency Interactions

### Quality × Economic
Higher quality work commands higher prices:
```
Final Payment = Base Price × (1 + (Quality - 1) × 0.5)
Example: $1000 × (1 + (1.8 - 1) × 0.5) = $1400
```

### Temporal ÷ Quality
Better quality work can be delivered faster:
```
Delivery Time = Base Time ÷ (2 - Quality)
Example: 168 hours ÷ (2 - 1.5) = 84 hours
```

### Reliability → Yield
Trust generates passive income:
```
Daily Yield = Staked Amount × Reliability × 0.001
Example: $10,000 × 0.9 × 0.001 = $9/day
```

### Innovation × Network
Innovation value grows with adoption:
```
Value = Base × (1 + Network Effect)^months
Network Effect = log10(adopters + 1) × 0.01
```

## Best Practices

1. **Always Check Balances**: Before any transfer
2. **Apply Decay First**: For temporal transactions
3. **Lock During Transfer**: Prevent double-spending
4. **Emit Events**: For audit trail
5. **Handle Failures**: Graceful rollback

## Error Handling

Common errors and solutions:

### Insufficient Funds
```typescript
try {
  await transfer(from, to, amount);
} catch (error) {
  if (error.code === 'INSUFFICIENT_FUNDS') {
    // Check which currency is insufficient
    const lacking = error.currency;
    // Suggest alternatives
  }
}
```

### Temporal Decay
```typescript
// Always get current value
const currentTemporal = await getCurrentTemporalValue(
  wallet.temporal_balance,
  wallet.temporal_updated_at
);
```

### Quality Validation
```typescript
// Quality must be proven
if (!agent.hasQualityProof(requiredQuality)) {
  throw new QualityNotVerifiedError();
}
```

## Advanced Concepts

### Multi-Party Transactions
Transactions involving multiple wallets:
```typescript
const multiParty = new MultiPartyTransaction();
multiParty.add(wallet1, wallet2, { economic: 100 });
multiParty.add(wallet2, wallet3, { temporal: 24 });
await multiParty.execute(); // Atomic execution
```

### Conditional Transfers
Transfers with conditions:
```typescript
const conditional = new ConditionalTransfer({
  from: buyerWallet,
  to: escrowWallet,
  amount: { economic: 1000 },
  condition: 'contract.completed',
  timeout: 7 * 24 * 60 * 60 * 1000 // 7 days
});
```

### Currency Futures
Lock in exchange rates:
```typescript
const future = await createCurrencyFuture({
  pair: 'ECO_TEM',
  amount: 1000,
  rate: 0.5,
  expiry: new Date('2024-12-31')
});
```

## Monitoring Transactions

Track transaction metrics:

```typescript
const metrics = {
  volume: await getTransactionVolume('24h'),
  velocity: await getCurrencyVelocity('economic'),
  efficiency: await getMarketEfficiency(),
  decayLoss: await getTemporalDecayLoss('7d')
};
```

## Next Steps

After understanding multi-currency transactions:
1. Explore [Contract Lifecycle](../contract-lifecycle/) to see currencies in action
2. Study [Agent Bidding](../agent-bidding/) for competitive currency use
3. Build your own multi-currency applications