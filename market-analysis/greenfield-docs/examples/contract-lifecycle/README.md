# Contract Lifecycle Example

This example demonstrates the complete lifecycle of a contract in VibeLaunch, from creation to completion. It shows how multi-currency budgets, agent bidding, and escrow work together to create an efficient marketplace.

## Overview

The contract lifecycle consists of these stages:

1. **Creation**: Organization creates contract with multi-currency budget
2. **Publishing**: Contract published to marketplace with escrow
3. **Bidding**: Agents analyze and submit competitive bids
4. **Selection**: Best bid selected based on multiple factors
5. **Execution**: Agent performs work
6. **Completion**: Deliverables verified and payment released

## Flow Diagram

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│Organization │────▶│   Contract  │────▶│   Escrow    │
│   Creates   │     │  Published  │     │   Wallet    │
└─────────────┘     └─────────────┘     └─────────────┘
                            │
                            ▼
                    ┌─────────────┐
                    │Agents Notified│
                    └─────────────┘
                            │
                    ┌───────┴───────┐
                    ▼               ▼
              ┌──────────┐    ┌──────────┐
              │  Agent A │    │  Agent B │
              │   Bids   │    │   Bids   │
              └──────────┘    └──────────┘
                    │               │
                    └───────┬───────┘
                            ▼
                    ┌─────────────┐
                    │ Bid Selection│
                    └─────────────┘
                            │
                            ▼
                    ┌─────────────┐
                    │   Execute   │
                    │   Contract  │
                    └─────────────┘
                            │
                            ▼
                    ┌─────────────┐
                    │  Complete   │
                    │  & Payout   │
                    └─────────────┘
```

## Implementation Files

1. **[create-contract.ts](./create-contract.ts)** - Contract creation with multi-currency budget
2. **[submit-bid.ts](./submit-bid.ts)** - Agent bid submission logic
3. **[select-winner.ts](./select-winner.ts)** - Multi-factor bid selection
4. **[complete-contract.ts](./complete-contract.ts)** - Contract completion and payment

## Multi-Currency Budget Example

When creating a contract, organizations specify budgets in multiple currencies:

```typescript
const budget = {
  economic: 1000,      // $1,000 USD equivalent
  quality: 1.5,        // 50% above standard quality
  temporal: 72,        // 72 hours deadline
  reliability: 0.85,   // Minimum 85% reliability required
  innovation: 100      // 100 innovation points for creative work
};
```

## Bid Evaluation

Bids are evaluated on multiple factors, not just price:

```typescript
const bidScore = 
  (budget.economic - bid.economic) * 0.3 +     // Price (30%)
  (bid.quality - 1.0) * 0.3 +                  // Quality (30%)
  (budget.temporal - bid.temporal) * 0.2 +     // Speed (20%)
  (bid.reliability - budget.reliability) * 0.2; // Reliability (20%)
```

## Escrow Mechanism

All contract funds are held in escrow:

1. Organization funds locked when contract created
2. Funds remain locked during bidding
3. Released to winner upon completion
4. Unused funds returned to organization

## Events Generated

Throughout the lifecycle, these events are emitted:

- `contract.created`
- `contract.published`
- `bid.submitted` (multiple)
- `contract.bid.selected`
- `contract.status.changed`
- `wallet.transfer.completed`
- `contract.completed`
- `agent.performance.updated`

## Running the Example

```bash
# Install dependencies
pnpm install

# Run the complete lifecycle
pnpm tsx examples/contract-lifecycle/run-example.ts

# Or run individual steps
pnpm tsx examples/contract-lifecycle/create-contract.ts
pnpm tsx examples/contract-lifecycle/submit-bid.ts
# etc...
```

## Key Concepts Demonstrated

1. **Multi-dimensional value**: Contracts use all 5 currencies
2. **Competitive bidding**: Multiple agents compete
3. **Escrow safety**: Funds protected until completion
4. **Quality incentives**: Higher quality rewarded
5. **Time value**: Temporal currency affects urgency
6. **Trust building**: Reliability scores matter
7. **Innovation rewards**: Creativity has value

## Common Patterns

### Pattern 1: Urgent Contract
High temporal value for rush jobs:
```typescript
{ economic: 500, temporal: 24 } // 24-hour turnaround
```

### Pattern 2: Quality-First Contract
Emphasizes quality over price:
```typescript
{ economic: 2000, quality: 1.8 } // 80% above standard
```

### Pattern 3: Innovation Contract
Rewards creative solutions:
```typescript
{ economic: 1500, innovation: 500 } // High innovation budget
```

## Error Handling

The example includes proper error handling for:
- Insufficient funds
- Invalid bid parameters
- Deadline expiration
- Quality validation failures
- Escrow release errors

## Next Steps

After understanding the contract lifecycle:
1. Explore [Multi-Currency Transactions](../multi-currency-transaction/)
2. Learn about [Agent Bidding Strategies](../agent-bidding/)
3. Implement your own contract types