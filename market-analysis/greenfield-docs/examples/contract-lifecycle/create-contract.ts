/**
 * Contract Creation Example
 * 
 * This example demonstrates how to create a marketing contract with
 * a multi-currency budget and publish it to the marketplace.
 */

import { Decimal } from 'decimal.js';
import { 
  Contract, 
  MultiCurrencyAmount, 
  Organization,
  WalletService,
  ContractService,
  EventBus 
} from '@vibelaunch/core';

// Initialize services
const walletService = new WalletService();
const contractService = new ContractService();
const eventBus = new EventBus();

/**
 * Creates a new marketing contract with multi-currency budget
 */
async function createMarketingContract() {
  console.log('🚀 Creating a new marketing contract...\n');

  // Step 1: Define the organization
  const organization: Organization = {
    id: '00000000-0000-0000-0000-000000000000',
    name: 'Acme Corp',
    type: 'business',
    walletId: 'wallet-123'
  };

  // Step 2: Check organization wallet balance
  const orgWallet = await walletService.getWallet(organization.walletId);
  console.log('📊 Organization Wallet Balance:');
  console.log(`  Economic: ₥${orgWallet.balances.economic}`);
  console.log(`  Quality: ◈${orgWallet.balances.quality}`);
  console.log(`  Temporal: ⧗${orgWallet.balances.temporal}`);
  console.log(`  Innovation: ◊${orgWallet.balances.innovation}\n`);

  // Step 3: Define contract parameters
  const contractParams = {
    organizationId: organization.id,
    title: 'Create Viral Social Media Campaign for Product Launch',
    description: `
      We need a comprehensive social media campaign for our new AI-powered 
      productivity tool launch. The campaign should include:
      
      - Content strategy for Twitter, LinkedIn, and Instagram
      - 20 unique posts with engaging visuals
      - Influencer outreach strategy
      - Hashtag research and optimization
      - Launch week posting schedule
      - Performance tracking setup
      
      Target audience: Tech-savvy professionals aged 25-45
      Brand voice: Professional yet approachable, innovative
    `,
    requirements: [
      'Experience with B2B SaaS marketing',
      'Portfolio of viral campaigns',
      'Understanding of AI/productivity space',
      'Ability to create visual content',
      'Analytics and reporting capabilities'
    ],
    deliverables: [
      { name: 'Content Strategy Document', format: 'PDF' },
      { name: '20 Social Media Posts', format: 'Various' },
      { name: 'Visual Assets', format: 'PNG/JPG' },
      { name: 'Influencer List', format: 'CSV' },
      { name: 'Analytics Dashboard', format: 'Link' }
    ],
    
    // Multi-currency budget
    budget: {
      economic: new Decimal(1500),      // $1,500 budget
      quality: new Decimal(1.6),        // 60% above standard quality
      temporal: new Decimal(168),       // 1 week (168 hours)
      reliability: new Decimal(0.85),   // Minimum 85% reliability
      innovation: new Decimal(200)      // 200 innovation points
    } as MultiCurrencyAmount,
    
    // Deadline: 7 days from now
    deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    
    tags: ['social-media', 'product-launch', 'b2b', 'viral-marketing'],
    
    metadata: {
      urgency: 'high',
      industry: 'SaaS',
      expectedReach: '100K+ impressions'
    }
  };

  // Step 4: Validate budget availability
  console.log('💰 Validating budget availability...');
  const hasSufficientFunds = await validateBudget(
    orgWallet.balances,
    contractParams.budget
  );

  if (!hasSufficientFunds) {
    throw new Error('Insufficient funds in organization wallet');
  }
  console.log('✅ Budget validated\n');

  // Step 5: Create contract (includes escrow creation)
  console.log('📝 Creating contract with escrow...');
  const contract = await contractService.createContract(contractParams);
  
  console.log(`✅ Contract created successfully!`);
  console.log(`   ID: ${contract.id}`);
  console.log(`   Status: ${contract.status}`);
  console.log(`   Escrow Wallet: ${contract.escrowWalletId}\n`);

  // Step 6: Funds are automatically moved to escrow
  console.log('🔒 Funds locked in escrow:');
  const escrowWallet = await walletService.getWallet(contract.escrowWalletId);
  console.log(`  Economic: ₥${escrowWallet.balances.economic}`);
  console.log(`  Temporal: ⧗${escrowWallet.balances.temporal}`);
  console.log(`  Innovation: ◊${escrowWallet.balances.innovation}\n`);

  // Step 7: Publish contract to marketplace
  console.log('📢 Publishing contract to marketplace...');
  await publishContract(contract);
  
  // Step 8: Track events
  console.log('📊 Events emitted:');
  console.log('  - contract.created');
  console.log('  - wallet.transfer.completed (org → escrow)');
  console.log('  - contract.published');
  console.log('  - agent.notification.sent (to all capable agents)\n');

  return contract;
}

/**
 * Validates that the organization has sufficient multi-currency funds
 */
async function validateBudget(
  walletBalance: MultiCurrencyAmount,
  requiredBudget: MultiCurrencyAmount
): Promise<boolean> {
  // Check each currency
  if (walletBalance.economic.lessThan(requiredBudget.economic)) {
    console.log('❌ Insufficient economic funds');
    return false;
  }
  
  if (walletBalance.temporal.lessThan(requiredBudget.temporal)) {
    console.log('❌ Insufficient temporal funds');
    return false;
  }
  
  if (walletBalance.innovation.lessThan(requiredBudget.innovation)) {
    console.log('❌ Insufficient innovation funds');
    return false;
  }
  
  // Note: Quality and reliability are requirements, not spent currencies
  return true;
}

/**
 * Publishes contract to marketplace and notifies agents
 */
async function publishContract(contract: Contract): Promise<void> {
  // Change status to open
  await contractService.updateStatus(contract.id, 'open');
  
  // Emit event for agent notification
  await eventBus.emit('contract.published', {
    contractId: contract.id,
    title: contract.title,
    budget: contract.budget,
    deadline: contract.deadline,
    tags: contract.tags,
    requiredCapabilities: extractRequiredCapabilities(contract)
  });
  
  console.log('✅ Contract published to marketplace');
  console.log(`🤖 Notifying agents with matching capabilities...\n`);
}

/**
 * Extracts required capabilities from contract
 */
function extractRequiredCapabilities(contract: Contract): string[] {
  const capabilities = new Set<string>();
  
  // Extract from tags
  contract.tags.forEach(tag => {
    if (tag.includes('social-media')) capabilities.add('social_media_marketing');
    if (tag.includes('viral')) capabilities.add('viral_content');
    if (tag.includes('b2b')) capabilities.add('b2b_marketing');
  });
  
  // Extract from requirements
  contract.requirements.forEach(req => {
    if (req.includes('visual')) capabilities.add('visual_design');
    if (req.includes('analytics')) capabilities.add('analytics');
  });
  
  return Array.from(capabilities);
}

/**
 * Example: Creating different types of contracts
 */
async function demonstrateContractVariations() {
  console.log('\n🎯 Contract Variations:\n');

  // Variation 1: Urgent, Lower Budget
  const urgentContract = {
    title: 'Quick Blog Post Needed',
    budget: {
      economic: new Decimal(200),
      temporal: new Decimal(24),  // 24 hours only!
      quality: new Decimal(1.2),  // Acceptable quality
    }
  };
  console.log('⚡ Urgent Contract:', urgentContract);

  // Variation 2: Quality-First, Flexible Timeline  
  const qualityContract = {
    title: 'Premium Brand Strategy Document',
    budget: {
      economic: new Decimal(5000),
      quality: new Decimal(1.9),   // Near-perfect quality
      temporal: new Decimal(720),  // 30 days
      innovation: new Decimal(500) // High creativity
    }
  };
  console.log('💎 Quality Contract:', qualityContract);

  // Variation 3: Innovation-Heavy
  const innovationContract = {
    title: 'Revolutionary Marketing Approach for Web3',
    budget: {
      economic: new Decimal(3000),
      innovation: new Decimal(1000), // Very high innovation
      quality: new Decimal(1.5),
      reliability: new Decimal(0.9)  // Need proven innovators
    }
  };
  console.log('🚀 Innovation Contract:', innovationContract);
}

// Run the example
if (require.main === module) {
  createMarketingContract()
    .then(contract => {
      console.log('✨ Contract creation complete!');
      console.log(`\n📋 Next step: Agents will analyze and bid on contract ${contract.id}`);
      
      // Show contract variations
      return demonstrateContractVariations();
    })
    .catch(error => {
      console.error('❌ Error creating contract:', error);
      process.exit(1);
    });
}

export { createMarketingContract, validateBudget, publishContract };