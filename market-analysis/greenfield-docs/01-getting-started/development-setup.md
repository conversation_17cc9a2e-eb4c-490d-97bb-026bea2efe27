# VibeLaunch Development Environment Setup

This guide provides detailed instructions for setting up a complete VibeLaunch development environment from scratch.

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Core Dependencies](#core-dependencies)
3. [Database Setup](#database-setup)
4. [Redis Configuration](#redis-configuration)
5. [Development Tools](#development-tools)
6. [Environment Configuration](#environment-configuration)
7. [IDE Setup](#ide-setup)
8. [Verification](#verification)
9. [Troubleshooting](#troubleshooting)

## System Requirements

### Minimum Requirements
- **CPU**: 4 cores (8 recommended)
- **RAM**: 8GB (16GB recommended)
- **Storage**: 20GB free space
- **OS**: macOS 12+, Ubuntu 20.04+, Windows 11 with WSL2

### Software Requirements
- Node.js 20.0.0 or higher
- PostgreSQL 15.0 or higher
- Redis 7.0 or higher
- Docker Desktop (optional but recommended)
- Git 2.39 or higher

## Core Dependencies

### 1. Install Node.js and pnpm

#### macOS
```bash
# Using Homebrew
brew install node@20
brew install pnpm

# Or using Node Version Manager (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 20
nvm use 20
npm install -g pnpm
```

#### Ubuntu/Debian
```bash
# Using NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs
npm install -g pnpm
```

#### Windows (WSL2)
```bash
# In WSL2 terminal
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 20
nvm use 20
npm install -g pnpm
```

### 2. Verify Installation
```bash
node --version  # Should show v20.x.x
pnpm --version  # Should show 8.x.x
```

## Database Setup

### 1. Install PostgreSQL with TimescaleDB

#### macOS
```bash
# Install PostgreSQL
brew install postgresql@15
brew services start postgresql@15

# Install TimescaleDB
brew tap timescale/tap
brew install timescaledb

# Configure PostgreSQL
timescaledb-tune --quiet --yes
```

#### Ubuntu/Debian
```bash
# Add PostgreSQL repository
sudo sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
sudo apt-get update

# Install PostgreSQL
sudo apt-get install postgresql-15 postgresql-client-15

# Install TimescaleDB
sudo add-apt-repository ppa:timescale/timescaledb-ppa
sudo apt-get update
sudo apt install timescaledb-2-postgresql-15

# Configure
sudo timescaledb-tune --quiet --yes
sudo systemctl restart postgresql
```

#### Using Docker (All Platforms)
```bash
# Create docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'
services:
  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: vibelaunch-postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: vibelaunch
      POSTGRES_USER: vibelaunch
      POSTGRES_PASSWORD: vibelaunch123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    
  redis:
    image: redis:7-alpine
    container_name: vibelaunch-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
EOF

# Start services
docker-compose up -d
```

### 2. Create Database and User

```bash
# Connect to PostgreSQL
psql -U postgres

# Create database and user
CREATE USER vibelaunch WITH PASSWORD 'vibelaunch123';
CREATE DATABASE vibelaunch OWNER vibelaunch;
GRANT ALL PRIVILEGES ON DATABASE vibelaunch TO vibelaunch;

# Enable extensions
\c vibelaunch
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "timescaledb";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

\q
```

### 3. Initialize Schema

```bash
# Create initial schema
psql -U vibelaunch -d vibelaunch -f market-analysis/greenfield-docs/04-data-models/database-schema.sql
```

## Redis Configuration

### 1. Install Redis

#### macOS
```bash
brew install redis
brew services start redis
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

### 2. Configure Redis for Development

```bash
# Edit Redis configuration
# macOS: /usr/local/etc/redis.conf
# Linux: /etc/redis/redis.conf

# Add these settings:
cat >> redis.conf << 'EOF'
# Enable Redis Streams
stream-node-max-bytes 4096
stream-node-max-entries 100

# Development settings
save ""
stop-writes-on-bgsave-error no
rdbcompression no

# Performance tuning
maxmemory 2gb
maxmemory-policy allkeys-lru
EOF

# Restart Redis
# macOS: brew services restart redis
# Linux: sudo systemctl restart redis
```

### 3. Verify Redis
```bash
redis-cli ping  # Should return PONG
```

## Development Tools

### 1. Install Essential Tools

```bash
# Database GUI (optional)
# macOS: brew install --cask tableplus
# Linux: Download from https://tableplus.com/linux

# API Testing
# macOS: brew install --cask postman
# Linux: snap install postman

# Redis GUI (optional)
# macOS: brew install --cask another-redis-desktop-manager
# Linux: snap install another-redis-desktop-manager
```

### 2. Install Global npm Packages

```bash
# TypeScript and development tools
pnpm add -g typescript ts-node nodemon concurrently

# Code quality tools
pnpm add -g eslint prettier

# Database tools
pnpm add -g prisma
```

## Environment Configuration

### 1. Create Project Structure

```bash
# Create project directory
mkdir -p ~/projects/vibe-match
cd ~/projects/vibe-match

# Initialize git repository
git init
git remote add origin https://github.com/star-boy-95/vibe-match.git

# Create directory structure
mkdir -p {packages,services,docs,scripts,tests,infrastructure}
```

### 2. Create Root Configuration Files

```bash
# Create .gitignore
cat > .gitignore << 'EOF'
# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
*.lcov
.nyc_output

# Production
build/
dist/
*.tsbuildinfo

# Environment
.env
.env.local
.env.*.local

# Logs
logs/
*.log
npm-debug.log*
pnpm-debug.log*

# OS
.DS_Store
*.pem

# IDE
.idea/
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.swp
*.swo
EOF

# Create .nvmrc
echo "20" > .nvmrc

# Create pnpm-workspace.yaml
cat > pnpm-workspace.yaml << 'EOF'
packages:
  - 'packages/*'
  - 'services/*'
EOF

# Create tsconfig.base.json
cat > tsconfig.base.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "commonjs",
    "lib": ["ES2022"],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "removeComments": true,
    "noEmitOnError": true,
    "types": ["node"],
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true
  },
  "exclude": ["node_modules", "dist", "coverage"]
}
EOF
```

### 3. Create Environment File

```bash
# Create .env.example
cat > .env.example << 'EOF'
# Node Environment
NODE_ENV=development
PORT=3000
LOG_LEVEL=debug

# Database
DATABASE_URL=postgresql://vibelaunch:vibelaunch123@localhost:5432/vibelaunch
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10

# Redis
REDIS_URL=redis://localhost:6379
REDIS_STREAMS_BLOCK_TIME=5000

# Authentication
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters!!
JWT_EXPIRY=1h
REFRESH_TOKEN_EXPIRY=7d

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key-here!!

# Default Organization (Development)
DEFAULT_ORG_ID=00000000-0000-0000-0000-000000000000

# LLM Providers (at least one required)
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
GOOGLE_AI_API_KEY=...

# API Configuration
API_RATE_LIMIT_WINDOW=60000
API_RATE_LIMIT_MAX=100

# Market Engine
MARKET_ENGINE_URL=http://localhost:8080
MARKET_ENGINE_TICK_INTERVAL=100

# Analytics
ANALYTICS_BATCH_SIZE=1000
ANALYTICS_FLUSH_INTERVAL=10000

# Monitoring
PROMETHEUS_PORT=9090
METRICS_ENABLED=true

# Feature Flags
ENABLE_MULTI_CURRENCY=true
ENABLE_TEAM_FORMATION=true
ENABLE_DERIVATIVES=false
EOF

# Copy to .env
cp .env.example .env
```

## IDE Setup

### 1. VS Code Configuration

```bash
# Create VS Code settings
mkdir -p .vscode
cat > .vscode/settings.json << 'EOF'
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.turbo": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/coverage": true
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
EOF

# Create launch configuration
cat > .vscode/launch.json << 'EOF'
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug API",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["run", "dev"],
      "cwd": "${workspaceFolder}/services/api",
      "console": "integratedTerminal",
      "env": {
        "NODE_ENV": "development",
        "DEBUG": "*"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Market Engine",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["run", "dev"],
      "cwd": "${workspaceFolder}/services/market-engine",
      "console": "integratedTerminal"
    }
  ]
}
EOF

# Create recommended extensions
cat > .vscode/extensions.json << 'EOF'
{
  "recommendations": [
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "prisma.prisma",
    "redhat.vscode-yaml",
    "streetsidesoftware.code-spell-checker",
    "usernamehw.errorlens",
    "yoavbls.pretty-ts-errors",
    "42crunch.vscode-openapi",
    "graphql.vscode-graphql"
  ]
}
EOF
```

### 2. Install VS Code Extensions

```bash
# Install recommended extensions
code --install-extension dbaeumer.vscode-eslint
code --install-extension esbenp.prettier-vscode
code --install-extension prisma.prisma
# ... install all recommended extensions
```

## Verification

### 1. Create Verification Script

```bash
# Create verify-setup.js
cat > scripts/verify-setup.js << 'EOF'
#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔍 Verifying VibeLaunch Development Setup...\n');

const checks = [
  {
    name: 'Node.js',
    command: 'node --version',
    minVersion: '20.0.0',
    extract: (output) => output.trim().substring(1)
  },
  {
    name: 'pnpm',
    command: 'pnpm --version',
    minVersion: '8.0.0',
    extract: (output) => output.trim()
  },
  {
    name: 'PostgreSQL',
    command: 'psql --version',
    minVersion: '15.0',
    extract: (output) => output.match(/\d+\.\d+/)[0]
  },
  {
    name: 'Redis',
    command: 'redis-cli --version',
    minVersion: '7.0.0',
    extract: (output) => output.match(/v=(\d+\.\d+\.\d+)/)[1]
  }
];

let allPassed = true;

// Check dependencies
checks.forEach(check => {
  try {
    const output = execSync(check.command, { encoding: 'utf8' });
    const version = check.extract(output);
    const passed = compareVersions(version, check.minVersion) >= 0;
    
    console.log(`${passed ? '✅' : '❌'} ${check.name}: ${version} ${passed ? '' : `(required: ${check.minVersion})`}`);
    
    if (!passed) allPassed = false;
  } catch (error) {
    console.log(`❌ ${check.name}: Not installed`);
    allPassed = false;
  }
});

// Check database connection
console.log('\n🔗 Checking Database Connection...');
try {
  execSync('psql -U vibelaunch -d vibelaunch -c "SELECT 1"', { stdio: 'ignore' });
  console.log('✅ PostgreSQL connection successful');
} catch (error) {
  console.log('❌ PostgreSQL connection failed');
  allPassed = false;
}

// Check Redis connection
try {
  execSync('redis-cli ping', { stdio: 'ignore' });
  console.log('✅ Redis connection successful');
} catch (error) {
  console.log('❌ Redis connection failed');
  allPassed = false;
}

// Check environment file
console.log('\n📄 Checking Environment Configuration...');
if (fs.existsSync('.env')) {
  console.log('✅ .env file exists');
} else {
  console.log('❌ .env file missing');
  allPassed = false;
}

// Summary
console.log('\n' + '='.repeat(50));
if (allPassed) {
  console.log('✅ All checks passed! Your development environment is ready.');
  console.log('\nNext steps:');
  console.log('1. Run: pnpm install');
  console.log('2. Run: pnpm dev');
  console.log('3. Open: http://localhost:3000');
} else {
  console.log('❌ Some checks failed. Please fix the issues above.');
}

function compareVersions(v1, v2) {
  const parts1 = v1.split('.').map(Number);
  const parts2 = v2.split('.').map(Number);
  
  for (let i = 0; i < 3; i++) {
    if (parts1[i] > parts2[i]) return 1;
    if (parts1[i] < parts2[i]) return -1;
  }
  return 0;
}
EOF

chmod +x scripts/verify-setup.js
```

### 2. Run Verification

```bash
node scripts/verify-setup.js
```

## Troubleshooting

### Common Issues

#### PostgreSQL Connection Failed
```bash
# Check if PostgreSQL is running
# macOS: brew services list
# Linux: sudo systemctl status postgresql

# Check PostgreSQL logs
# macOS: tail -f /usr/local/var/log/<EMAIL>
# Linux: sudo tail -f /var/log/postgresql/postgresql-15-main.log

# Reset password if needed
psql -U postgres -c "ALTER USER vibelaunch PASSWORD 'vibelaunch123';"
```

#### Port Already in Use
```bash
# Find process using port
lsof -i :3000  # or :5432, :6379

# Kill process
kill -9 <PID>
```

#### Node Version Issues
```bash
# Switch Node version
nvm use 20

# Set default
nvm alias default 20
```

#### Permission Errors
```bash
# Fix npm permissions
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### Getting Help

If you encounter issues:

1. Check the error logs in detail
2. Verify all dependencies are correctly installed
3. Ensure all services are running
4. Check environment variables are set correctly
5. Try the Docker approach if native installation fails

## Next Steps

With your development environment set up:

1. 📖 Read the [Quick Start Guide](./quick-start.md)
2. 🏗️ Explore the [System Architecture](../02-architecture/system-overview.md)
3. 💰 Understand the [Five-Currency System](../05-implementation/five-currency-system.md)
4. 🚀 Start building your first contract!

---

*Environment setup complete! You're ready to build the future of AI-powered marketing.*