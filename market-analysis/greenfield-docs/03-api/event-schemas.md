# VibeLaunch Event Schemas

This document defines all event schemas for the VibeLaunch event-driven architecture. Events are the primary communication mechanism between services and enable real-time updates throughout the system.

## Event Architecture Overview

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Service   │────▶│ Event Bus   │────▶│  Consumers  │
│  Producer   │     │Redis Streams│     │  Services   │
└─────────────┘     └─────────────┘     └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │  PostgreSQL │
                    │   NOTIFY    │
                    └─────────────┘
```

## Event Naming Convention

Events follow a hierarchical naming pattern:
```
{domain}.{entity}.{action}
```

Examples:
- `contract.created`
- `bid.submitted`
- `wallet.balance.updated`
- `market.order.filled`

## Core Event Schemas

### 1. Contract Events

#### contract.created
Emitted when a new contract is created and published to the marketplace.

```typescript
interface ContractCreatedEvent {
  type: 'contract.created';
  version: '1.0';
  timestamp: string; // ISO 8601
  data: {
    contractId: string;
    organizationId: string;
    title: string;
    budget: {
      economic: number;
      quality: number;
      temporal: number;
      innovation: number;
    };
    requiredReliability: number;
    deadline: string;
    tags: string[];
  };
  metadata: {
    correlationId: string;
    causationId?: string;
    userId?: string;
  };
}
```

#### contract.updated
Emitted when contract details are modified.

```typescript
interface ContractUpdatedEvent {
  type: 'contract.updated';
  version: '1.0';
  timestamp: string;
  data: {
    contractId: string;
    changes: {
      field: string;
      oldValue: any;
      newValue: any;
    }[];
  };
  metadata: EventMetadata;
}
```

#### contract.status.changed
Emitted when contract status changes.

```typescript
interface ContractStatusChangedEvent {
  type: 'contract.status.changed';
  version: '1.0';
  timestamp: string;
  data: {
    contractId: string;
    previousStatus: ContractStatus;
    newStatus: ContractStatus;
    reason?: string;
  };
  metadata: EventMetadata;
}
```

#### contract.bid.selected
Emitted when a bid is selected as the winner.

```typescript
interface BidSelectedEvent {
  type: 'contract.bid.selected';
  version: '1.0';
  timestamp: string;
  data: {
    contractId: string;
    bidId: string;
    agentId: string;
    finalPrice: MultiCurrencyAmount;
    escrowReleased: boolean;
  };
  metadata: EventMetadata;
}
```

#### contract.completed
Emitted when a contract is successfully completed.

```typescript
interface ContractCompletedEvent {
  type: 'contract.completed';
  version: '1.0';
  timestamp: string;
  data: {
    contractId: string;
    agentId: string;
    actualQuality: number;
    deliveryTime: number; // hours
    efficiencyImpact: number;
    paymentProcessed: boolean;
  };
  metadata: EventMetadata;
}
```

### 2. Bid Events

#### bid.submitted
Emitted when an agent submits a bid.

```typescript
interface BidSubmittedEvent {
  type: 'bid.submitted';
  version: '1.0';
  timestamp: string;
  data: {
    bidId: string;
    contractId: string;
    agentId: string;
    pricing: MultiCurrencyAmount;
    deliveryTime: number;
    teamAgentIds?: string[];
    synergyScore?: number;
  };
  metadata: EventMetadata;
}
```

#### bid.withdrawn
Emitted when a bid is withdrawn.

```typescript
interface BidWithdrawnEvent {
  type: 'bid.withdrawn';
  version: '1.0';
  timestamp: string;
  data: {
    bidId: string;
    contractId: string;
    agentId: string;
    reason: string;
  };
  metadata: EventMetadata;
}
```

### 3. Wallet Events

#### wallet.balance.updated
Emitted when any wallet balance changes.

```typescript
interface WalletBalanceUpdatedEvent {
  type: 'wallet.balance.updated';
  version: '1.0';
  timestamp: string;
  data: {
    walletId: string;
    currency: CurrencyType;
    previousBalance: string; // Decimal string
    newBalance: string;
    change: string;
    operation: 'credit' | 'debit';
    transactionId?: string;
  };
  metadata: EventMetadata;
}
```

#### wallet.transfer.completed
Emitted when a currency transfer completes.

```typescript
interface TransferCompletedEvent {
  type: 'wallet.transfer.completed';
  version: '1.0';
  timestamp: string;
  data: {
    transactionId: string;
    fromWallet: string;
    toWallet: string;
    currency: CurrencyType;
    amount: string;
    fee?: string;
  };
  metadata: EventMetadata;
}
```

#### wallet.yield.generated
Emitted when reliability yields are generated.

```typescript
interface YieldGeneratedEvent {
  type: 'wallet.yield.generated';
  version: '1.0';
  timestamp: string;
  data: {
    walletId: string;
    principal: string;
    yield: string;
    reliabilityScore: number;
    period: number; // days
  };
  metadata: EventMetadata;
}
```

### 4. Market Events

#### market.order.placed
Emitted when a new order is placed.

```typescript
interface OrderPlacedEvent {
  type: 'market.order.placed';
  version: '1.0';
  timestamp: string;
  data: {
    orderId: string;
    walletId: string;
    pair: string;
    side: 'buy' | 'sell';
    type: OrderType;
    price?: string;
    quantity: string;
  };
  metadata: EventMetadata;
}
```

#### market.order.filled
Emitted when an order is filled (partially or fully).

```typescript
interface OrderFilledEvent {
  type: 'market.order.filled';
  version: '1.0';
  timestamp: string;
  data: {
    orderId: string;
    tradeId: string;
    pair: string;
    price: string;
    quantity: string;
    remainingQuantity: string;
    status: 'partial' | 'filled';
  };
  metadata: EventMetadata;
}
```

#### market.price.updated
Emitted when market prices change significantly.

```typescript
interface PriceUpdatedEvent {
  type: 'market.price.updated';
  version: '1.0';
  timestamp: string;
  data: {
    pair: string;
    previousPrice: string;
    newPrice: string;
    changePercent: number;
    volume24h: string;
  };
  metadata: EventMetadata;
}
```

### 5. Agent Events

#### agent.registered
Emitted when a new agent joins the platform.

```typescript
interface AgentRegisteredEvent {
  type: 'agent.registered';
  version: '1.0';
  timestamp: string;
  data: {
    agentId: string;
    name: string;
    type: AgentType;
    capabilities: string[];
    walletId: string;
  };
  metadata: EventMetadata;
}
```

#### agent.performance.updated
Emitted when agent performance metrics change.

```typescript
interface AgentPerformanceUpdatedEvent {
  type: 'agent.performance.updated';
  version: '1.0';
  timestamp: string;
  data: {
    agentId: string;
    metrics: {
      contractsCompleted: number;
      successRate: number;
      averageQuality: number;
      reliabilityScore: number;
      reliabilityChange: number;
    };
    rankings: {
      reliability: number;
      quality: number;
      earnings: number;
    };
  };
  metadata: EventMetadata;
}
```

#### agent.team.formed
Emitted when agents form a team for a contract.

```typescript
interface TeamFormedEvent {
  type: 'agent.team.formed';
  version: '1.0';
  timestamp: string;
  data: {
    teamId: string;
    contractId: string;
    leadAgentId: string;
    memberAgentIds: string[];
    synergyScore: number;
    expectedPerformance: {
      quality: number;
      deliveryTime: number;
    };
  };
  metadata: EventMetadata;
}
```

### 6. System Events

#### system.efficiency.calculated
Emitted when system efficiency is recalculated.

```typescript
interface EfficiencyCalculatedEvent {
  type: 'system.efficiency.calculated';
  version: '1.0';
  timestamp: string;
  data: {
    overall: number;
    components: {
      priceDiscovery: number;
      allocation: number;
      execution: number;
      information: number;
      innovation: number;
    };
    improvementRate: number;
    period: string;
  };
  metadata: EventMetadata;
}
```

#### system.alert
Emitted when system issues are detected.

```typescript
interface SystemAlertEvent {
  type: 'system.alert';
  version: '1.0';
  timestamp: string;
  data: {
    alertId: string;
    severity: 'info' | 'warning' | 'critical';
    component: string;
    message: string;
    metrics?: Record<string, any>;
    autoResolve?: boolean;
  };
  metadata: EventMetadata;
}
```

## Event Bus Implementation

### Redis Streams Configuration

```typescript
interface StreamConfig {
  // Stream names by domain
  streams: {
    contracts: 'vibelaunch:contracts';
    bids: 'vibelaunch:bids';
    wallets: 'vibelaunch:wallets';
    markets: 'vibelaunch:markets';
    agents: 'vibelaunch:agents';
    system: 'vibelaunch:system';
  };
  
  // Consumer groups
  consumerGroups: {
    api: 'api-service';
    analytics: 'analytics-service';
    notifications: 'notification-service';
    audit: 'audit-service';
  };
  
  // Retention policy
  retention: {
    maxLength: 1000000; // 1M events per stream
    maxAge: 30 * 24 * 60 * 60 * 1000; // 30 days
  };
}
```

### PostgreSQL NOTIFY Configuration

```sql
-- Channel configuration
CREATE OR REPLACE FUNCTION notify_event()
RETURNS trigger AS $$
DECLARE
  channel TEXT;
  payload JSON;
BEGIN
  -- Determine channel based on table
  channel := TG_TABLE_SCHEMA || ':' || TG_TABLE_NAME;
  
  -- Build payload
  payload := json_build_object(
    'operation', TG_OP,
    'schema', TG_TABLE_SCHEMA,
    'table', TG_TABLE_NAME,
    'data', row_to_json(NEW),
    'old_data', CASE WHEN TG_OP = 'UPDATE' THEN row_to_json(OLD) ELSE NULL END,
    'timestamp', NOW()
  );
  
  -- Send notification
  PERFORM pg_notify(channel, payload::text);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply to tables
CREATE TRIGGER contracts_notify AFTER INSERT OR UPDATE ON contracts
  FOR EACH ROW EXECUTE FUNCTION notify_event();
```

## Event Processing Patterns

### 1. Event Publishing

```typescript
class EventPublisher {
  async publish<T extends BaseEvent>(event: T): Promise<void> {
    // Validate event schema
    await this.validateSchema(event);
    
    // Add metadata
    const enrichedEvent = {
      ...event,
      metadata: {
        ...event.metadata,
        publishedAt: new Date().toISOString(),
        publisherId: this.serviceId,
      }
    };
    
    // Publish to Redis Stream
    const stream = this.getStreamName(event.type);
    await this.redis.xadd(
      stream,
      '*',
      'event', JSON.stringify(enrichedEvent)
    );
    
    // Also publish to PostgreSQL for critical events
    if (this.isCriticalEvent(event)) {
      await this.publishToPostgres(enrichedEvent);
    }
  }
}
```

### 2. Event Consumption

```typescript
class EventConsumer {
  async consume(
    stream: string,
    group: string,
    handler: EventHandler
  ): Promise<void> {
    while (this.running) {
      try {
        // Read from stream
        const messages = await this.redis.xreadgroup(
          'GROUP', group, this.consumerId,
          'BLOCK', 5000,
          'COUNT', 10,
          'STREAMS', stream, '>'
        );
        
        // Process messages
        for (const message of messages) {
          await this.processMessage(message, handler);
        }
      } catch (error) {
        await this.handleError(error);
      }
    }
  }
  
  private async processMessage(
    message: StreamMessage,
    handler: EventHandler
  ): Promise<void> {
    const event = JSON.parse(message.data.event);
    
    try {
      await handler(event);
      await this.redis.xack(message.stream, this.group, message.id);
    } catch (error) {
      await this.handleProcessingError(error, message);
    }
  }
}
```

### 3. Event Replay

```typescript
class EventReplay {
  async replayEvents(
    stream: string,
    fromTime: Date,
    toTime: Date,
    handler: EventHandler
  ): Promise<void> {
    const startId = `${fromTime.getTime()}-0`;
    const endId = `${toTime.getTime()}-999`;
    
    const messages = await this.redis.xrange(
      stream,
      startId,
      endId
    );
    
    for (const message of messages) {
      const event = JSON.parse(message.data.event);
      await handler(event);
    }
  }
}
```

## Event Guarantees

### Delivery Guarantees
- **At-least-once delivery**: Events may be delivered multiple times
- **Ordering**: Events are ordered within a stream but not across streams
- **Durability**: Events are persisted to disk in Redis
- **Retention**: Events are kept for 30 days or 1M events per stream

### Idempotency
All event handlers must be idempotent:

```typescript
class IdempotentHandler {
  private processedEvents = new Set<string>();
  
  async handle(event: BaseEvent): Promise<void> {
    const eventKey = `${event.type}:${event.metadata.correlationId}`;
    
    if (this.processedEvents.has(eventKey)) {
      return; // Already processed
    }
    
    try {
      await this.processEvent(event);
      this.processedEvents.add(eventKey);
    } catch (error) {
      // Handle error without adding to processed set
      throw error;
    }
  }
}
```

## Monitoring Events

### Event Metrics

```typescript
interface EventMetrics {
  // Publishing metrics
  eventsPublished: Counter;
  publishLatency: Histogram;
  publishErrors: Counter;
  
  // Consumption metrics
  eventsConsumed: Counter;
  consumptionLag: Gauge;
  processingDuration: Histogram;
  processingErrors: Counter;
  
  // Stream metrics
  streamLength: Gauge;
  oldestEventAge: Gauge;
}
```

### Event Tracing

```typescript
interface EventTrace {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  service: string;
  operation: string;
  timestamp: string;
  duration: number;
  tags: Record<string, any>;
}
```

## Best Practices

1. **Event Design**
   - Keep events small and focused
   - Include all necessary data (events should be self-contained)
   - Version events from the start
   - Use past tense for event names

2. **Error Handling**
   - Implement retry logic with exponential backoff
   - Use dead letter queues for failed events
   - Log all errors with context
   - Monitor processing lag

3. **Performance**
   - Batch event publishing when possible
   - Use consumer groups for parallel processing
   - Implement backpressure mechanisms
   - Monitor memory usage

4. **Testing**
   - Test event handlers in isolation
   - Use event replay for integration testing
   - Simulate failures and edge cases
   - Verify idempotency

## Event Flow Examples

### Contract Creation Flow
```
1. User creates contract via API
2. API publishes contract.created event
3. Consumers:
   - Agent service: Notifies capable agents
   - Analytics service: Updates efficiency metrics
   - Audit service: Logs activity
   - Notification service: Sends alerts
```

### Bid Selection Flow
```
1. Organization selects winning bid
2. API publishes contract.bid.selected event
3. Consumers:
   - Wallet service: Releases escrow
   - Agent service: Updates agent metrics
   - Contract service: Updates contract status
   - Analytics service: Calculates efficiency impact
```

### Multi-Currency Transfer Flow
```
1. Transfer initiated
2. Wallet service publishes wallet.transfer.initiated
3. If temporal currency: Apply decay calculation
4. Update balances
5. Publish wallet.balance.updated for both wallets
6. Publish wallet.transfer.completed
7. Consumers update UI, analytics, and audit logs
```