# VibeLaunch GraphQL Schema
# Multi-dimensional marketplace for AI-powered marketing services

scalar DateTime
scalar Decimal
scalar UUID
scalar JSON

# =====================================================
# ENUMS
# =====================================================

enum OrganizationType {
  BUSINESS
  AGENCY
  INDIVIDUAL
}

enum UserRole {
  OWNER
  ADMIN
  MEMBER
  VIEWER
}

enum CurrencyType {
  ECONOMIC
  QUALITY
  TEMPORAL
  RELIABILITY
  INNOVATION
}

enum ContractStatus {
  DRAFT
  OPEN
  BIDDING
  IN_PROGRESS
  REVIEW
  COMPLETED
  CANCELLED
  DISPUTED
}

enum BidStatus {
  PENDING
  ACCEPTED
  REJECTED
  WITHDRAWN
  EXPIRED
}

enum AgentType {
  MASTER
  CONTENT_CREATOR
  SEO_SPECIALIST
  SOCIAL_MEDIA
  DATA_ANALYST
  VISUAL_DESIGNER
  EMAIL_MARKETER
  CREATIVE_DIRECTOR
}

enum TransactionType {
  TRANSFER
  EXCHANGE
  FEE
  REWARD
  PENALTY
  ESCROW
  YIELD
}

enum OrderSide {
  BUY
  SELL
}

enum OrderType {
  MARKET
  LIMIT
  STOP
  STOP_LIMIT
}

# =====================================================
# INPUT TYPES
# =====================================================

input CreateOrganizationInput {
  name: String!
  type: OrganizationType!
  metadata: JSON
}

input CreateContractInput {
  title: String!
  description: String!
  requirements: [String!]!
  budget: MultiCurrencyAmountInput!
  deadline: DateTime!
  tags: [String!]
  metadata: JSON
}

input MultiCurrencyAmountInput {
  economic: Decimal
  quality: Decimal
  temporal: Decimal
  reliability: Decimal
  innovation: Decimal
}

input SubmitBidInput {
  contractId: UUID!
  pricing: MultiCurrencyAmountInput!
  proposal: String!
  deliveryTime: Int! # hours
  teamAgentIds: [UUID!]
  approach: JSON
}

input TransferCurrencyInput {
  fromWallet: UUID!
  toWallet: UUID!
  currency: CurrencyType!
  amount: Decimal!
  metadata: JSON
}

input ExchangeCurrencyInput {
  walletId: UUID!
  fromCurrency: CurrencyType!
  toCurrency: CurrencyType!
  amount: Decimal!
  slippageTolerance: Decimal
}

input CreateOrderInput {
  pair: String! # e.g., "ECO_QUA"
  side: OrderSide!
  type: OrderType!
  price: Decimal
  quantity: Decimal!
  stopPrice: Decimal
  timeInForce: String
}

input TimeRangeInput {
  start: DateTime!
  end: DateTime!
}

input PaginationInput {
  limit: Int = 20
  offset: Int = 0
}

# =====================================================
# OBJECT TYPES
# =====================================================

type Organization {
  id: UUID!
  name: String!
  type: OrganizationType!
  wallet: Wallet!
  users: [User!]!
  contracts(
    status: ContractStatus
    pagination: PaginationInput
  ): ContractConnection!
  metadata: JSON
  settings: JSON
  createdAt: DateTime!
  updatedAt: DateTime!
}

type User {
  id: UUID!
  organization: Organization!
  email: String!
  name: String
  role: UserRole!
  lastLogin: DateTime
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Wallet {
  id: UUID!
  balances: MultiCurrencyAmount!
  locked: MultiCurrencyAmount!
  yields: YieldInfo
  transactions(
    currency: CurrencyType
    timeRange: TimeRangeInput
    pagination: PaginationInput
  ): TransactionConnection!
  history(period: String!): [WalletSnapshot!]!
  updatedAt: DateTime!
}

type MultiCurrencyAmount {
  economic: Decimal!
  quality: Decimal!
  temporal: Decimal!
  reliability: Decimal!
  innovation: Decimal!
  # Computed fields
  totalValueUSD: Decimal!
  temporalDecayRate: Decimal!
}

type YieldInfo {
  reliabilityYield: Decimal!
  nextYieldDate: DateTime!
  historicalYields: Decimal!
}

type Contract {
  id: UUID!
  organization: Organization!
  title: String!
  description: String!
  requirements: [String!]!
  deliverables: JSON!
  budget: MultiCurrencyAmount!
  deadline: DateTime!
  status: ContractStatus!
  
  # Bidding
  bids(
    status: BidStatus
    pagination: PaginationInput
  ): BidConnection!
  selectedBid: Bid
  
  # Performance
  actualQuality: Decimal
  actualDeliveryTime: Decimal
  efficiencyImpact: Decimal
  
  # Metadata
  tags: [String!]!
  metadata: JSON
  createdAt: DateTime!
  updatedAt: DateTime!
  completedAt: DateTime
  
  # Computed fields
  timeRemaining: String!
  bidCount: Int!
  averageBidPrice: MultiCurrencyAmount!
}

type Bid {
  id: UUID!
  contract: Contract!
  agent: Agent!
  pricing: MultiCurrencyAmount!
  proposal: String!
  deliveryTime: Int! # hours
  
  # Team bidding
  team: [Agent!]
  synergyScore: Decimal!
  
  # Scoring
  totalScore: Decimal!
  efficiencyScore: Decimal!
  
  # Status
  status: BidStatus!
  createdAt: DateTime!
  updatedAt: DateTime!
  
  # Computed fields
  savingsPercentage: Decimal!
  rankingPosition: Int!
}

type Agent {
  id: UUID!
  name: String!
  type: AgentType!
  capabilities: [String!]!
  wallet: Wallet!
  
  # Performance
  performance: AgentPerformance!
  
  # Configuration
  llmProvider: String!
  status: String!
  
  # History
  contracts(
    status: ContractStatus
    pagination: PaginationInput
  ): ContractConnection!
  bids(
    status: BidStatus
    pagination: PaginationInput
  ): BidConnection!
  
  # Timestamps
  createdAt: DateTime!
  lastActiveAt: DateTime!
}

type AgentPerformance {
  totalContracts: Int!
  successfulContracts: Int!
  averageQuality: Decimal!
  averageDeliveryTime: Decimal!
  reliabilityScore: Decimal!
  totalEarnings: MultiCurrencyAmount!
  
  # Rankings
  reliabilityRank: Int!
  qualityRank: Int!
  earningsRank: Int!
  
  # Trends
  monthlyImprovement: Decimal!
  successRate: Decimal!
}

type Transaction {
  id: UUID!
  fromWallet: Wallet
  toWallet: Wallet
  currencyType: CurrencyType!
  amount: Decimal!
  type: TransactionType!
  status: String!
  
  # Exchange details
  exchangeRate: Decimal
  exchangeFee: Decimal
  
  # Metadata
  metadata: JSON
  createdAt: DateTime!
  completedAt: DateTime
}

type MarketData {
  pair: String!
  lastPrice: Decimal!
  volume24h: Decimal!
  high24h: Decimal!
  low24h: Decimal!
  changePercent24h: Decimal!
  
  # Order book
  orderBook(depth: Int = 20): OrderBook!
  
  # Historical
  candlesticks(
    interval: String!
    limit: Int = 100
  ): [Candlestick!]!
  
  # Real-time
  timestamp: DateTime!
}

type OrderBook {
  pair: String!
  bids: [OrderBookEntry!]!
  asks: [OrderBookEntry!]!
  spread: Decimal!
  midPrice: Decimal!
  timestamp: DateTime!
}

type OrderBookEntry {
  price: Decimal!
  quantity: Decimal!
  total: Decimal!
}

type Candlestick {
  time: DateTime!
  open: Decimal!
  high: Decimal!
  low: Decimal!
  close: Decimal!
  volume: Decimal!
}

type ExchangeRates {
  base: CurrencyType!
  rates: JSON! # { "QUALITY": 1.5, "TEMPORAL": 0.8, ... }
  timestamp: DateTime!
}

type EfficiencyMetrics {
  overall: Decimal!
  components: EfficiencyComponents!
  trend: [EfficiencyPoint!]!
  improvementRate: Decimal!
  target: Decimal!
}

type EfficiencyComponents {
  priceDiscovery: Decimal!
  allocation: Decimal!
  execution: Decimal!
  information: Decimal!
  innovation: Decimal!
}

type EfficiencyPoint {
  timestamp: DateTime!
  value: Decimal!
}

type TeamAnalysis {
  agents: [Agent!]!
  synergyScore: Decimal!
  predictedPerformance: TeamPerformancePrediction!
  optimalComposition: Boolean!
}

type TeamPerformancePrediction {
  expectedQuality: Decimal!
  expectedDeliveryTime: Int!
  successProbability: Decimal!
  efficiencyGain: Decimal!
}

# =====================================================
# CONNECTIONS (Pagination)
# =====================================================

type ContractConnection {
  edges: [ContractEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
  aggregations: ContractAggregations
}

type ContractEdge {
  node: Contract!
  cursor: String!
}

type ContractAggregations {
  totalBudget: MultiCurrencyAmount!
  averageEfficiency: Decimal!
  statusCounts: JSON!
}

type BidConnection {
  edges: [BidEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type BidEdge {
  node: Bid!
  cursor: String!
}

type TransactionConnection {
  edges: [TransactionEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
  totalVolume: Decimal!
}

type TransactionEdge {
  node: Transaction!
  cursor: String!
}

type PageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
  endCursor: String
}

type WalletSnapshot {
  timestamp: DateTime!
  balances: MultiCurrencyAmount!
  totalValueUSD: Decimal!
}

# =====================================================
# QUERY ROOT
# =====================================================

type Query {
  # Organizations
  organization(id: UUID!): Organization
  organizations(
    type: OrganizationType
    pagination: PaginationInput
  ): [Organization!]!
  
  # Users
  me: User
  user(id: UUID!): User
  
  # Contracts
  contract(id: UUID!): Contract
  contracts(
    organizationId: UUID
    status: ContractStatus
    tags: [String!]
    minBudget: Decimal
    maxBudget: Decimal
    pagination: PaginationInput
  ): ContractConnection!
  
  # Agents
  agent(id: UUID!): Agent
  agents(
    type: AgentType
    capability: String
    minReliability: Decimal
    status: String
    pagination: PaginationInput
  ): [Agent!]!
  
  agentRankings(
    metric: String! # reliability, quality, earnings
    type: AgentType
    limit: Int = 10
  ): [Agent!]!
  
  # Wallets
  wallet(id: UUID!): Wallet
  
  # Markets
  marketData(pair: String!): MarketData
  exchangeRates(base: CurrencyType!): ExchangeRates
  
  # Analytics
  marketEfficiency(period: String = "day"): EfficiencyMetrics!
  
  teamAnalysis(agentIds: [UUID!]!): TeamAnalysis!
  
  # System
  systemHealth: SystemHealth!
}

# =====================================================
# MUTATION ROOT
# =====================================================

type Mutation {
  # Organizations
  createOrganization(input: CreateOrganizationInput!): Organization!
  updateOrganization(id: UUID!, input: JSON!): Organization!
  
  # Contracts
  createContract(input: CreateContractInput!): Contract!
  updateContractStatus(id: UUID!, status: ContractStatus!): Contract!
  selectBid(contractId: UUID!, bidId: UUID!): Contract!
  completeContract(id: UUID!, deliverables: JSON!): Contract!
  
  # Bids
  submitBid(input: SubmitBidInput!): Bid!
  withdrawBid(id: UUID!): Bid!
  
  # Wallets
  transferCurrency(input: TransferCurrencyInput!): Transaction!
  exchangeCurrency(input: ExchangeCurrencyInput!): Transaction!
  claimYield(walletId: UUID!): Transaction!
  
  # Markets
  createOrder(input: CreateOrderInput!): Order!
  cancelOrder(id: UUID!): Order!
  
  # Agents
  registerAgent(input: JSON!): Agent!
  updateAgentStatus(id: UUID!, status: String!): Agent!
}

# =====================================================
# SUBSCRIPTION ROOT
# =====================================================

type Subscription {
  # Contract updates
  contractUpdated(id: UUID!): Contract!
  contractBids(contractId: UUID!): Bid!
  
  # Market data
  priceUpdates(pair: String!): PriceUpdate!
  orderBookUpdates(pair: String!): OrderBook!
  trades(pair: String): Trade!
  
  # Wallet updates
  walletBalanceChanged(walletId: UUID!): WalletUpdate!
  
  # System events
  marketEfficiencyChanged: EfficiencyMetrics!
  systemAlert: SystemAlert!
}

# =====================================================
# SUBSCRIPTION TYPES
# =====================================================

type PriceUpdate {
  pair: String!
  price: Decimal!
  volume: Decimal!
  timestamp: DateTime!
}

type Trade {
  id: UUID!
  pair: String!
  price: Decimal!
  quantity: Decimal!
  side: OrderSide!
  timestamp: DateTime!
}

type WalletUpdate {
  walletId: UUID!
  currency: CurrencyType!
  previousBalance: Decimal!
  newBalance: Decimal!
  change: Decimal!
  transaction: Transaction
  timestamp: DateTime!
}

type SystemAlert {
  id: UUID!
  severity: String! # info, warning, critical
  message: String!
  component: String!
  timestamp: DateTime!
}

type SystemHealth {
  status: String! # healthy, degraded, unhealthy
  services: JSON!
  efficiency: Decimal!
  activeUsers: Int!
  activeAgents: Int!
  timestamp: DateTime!
}

type Order {
  id: UUID!
  wallet: Wallet!
  pair: String!
  side: OrderSide!
  type: OrderType!
  price: Decimal
  quantity: Decimal!
  filledQuantity: Decimal!
  status: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}