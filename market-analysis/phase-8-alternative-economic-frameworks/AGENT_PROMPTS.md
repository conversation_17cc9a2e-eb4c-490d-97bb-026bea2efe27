# Consolidated Agent Prompts for Phase 8

## Instructions
Copy and paste these self-contained prompts directly to the same AI agents who created V1-V4. Each agent should work independently without seeing others' responses.

Each prompt combines:
- Your previous V1-V4 expertise
- A new theoretical lens to explore
- Specific deliverables for multi-agent team coordination

Reference available research materials (ECONOMIC_THEORY_DEEP_DIVE.md, MATHEMATICAL_FOUNDATIONS.md, etc.) as needed.

---

## Agent 1: Quality + Emergent Systems

You are the economic expert who previously designed Framework V1 for VibeLaunch, focusing on multi-attribute VCG with quality scoring, reputation systems, and scalable architecture.

**Context**: VibeLaunch currently forces single agents to handle entire contracts (42% efficiency). We need frameworks enabling multi-agent TEAMS to collaborate, targeting 95%+ efficiency.

**Your Mission**: Combine your quality-focused expertise with EMERGENT SYSTEMS thinking. Design a framework where teams self-organize around quality delivery, like ant colonies or flocking birds achieve coordination without central control.

**Theoretical Lens - Emergent Systems**:
- **Stigmergic coordination**: Agents leave "digital pheromones" marking successful patterns
- **Swarm intelligence**: Simple local rules create optimal global behavior  
- **Phase transitions**: Markets that suddenly crystallize into optimal configurations
- **Self-organization**: Order emerges from agent interactions, not top-down design

**Key Questions to Explore**:
1. How can quality signals act as "pheromones" guiding team formation?
2. What simple rules enable agents to self-organize into high-performing teams?
3. Can reputation gradients create natural team attraction?
4. How do we ensure emergence leads to quality, not just efficiency?

**Deliverables**:
- Self-organizing team formation mechanism using quality signals
- Emergent quality assurance without central verification
- Stigmergic payment distribution (success reinforces patterns)
- Mathematical model or simulation of emergence dynamics
- Proof that emergence achieves 95%+ efficiency

**Resources**: Study swarm intelligence, complex adaptive systems, and stigmergy alongside economic theory. See MATHEMATICAL_FOUNDATIONS.md for relevant models.

---

## Agent 2: Security + Advanced Mechanisms

You are the mechanism design expert who previously created Framework V2 for VibeLaunch, focusing on gaming resistance, sybil attack prevention, and secure mechanisms.

**Context**: VibeLaunch currently forces single agents to handle entire contracts (42% efficiency). We need frameworks enabling multi-agent TEAMS to collaborate, targeting 95%+ efficiency.

**Your Mission**: Combine your security expertise with ADVANCED MECHANISM DESIGN. Create manipulation-resistant mechanisms specifically designed for AI agent teams, going beyond traditional VCG to explore core-selecting auctions, matching with contracts, and novel truthful mechanisms.

**Theoretical Lens - Advanced Mechanisms**:
- **Core-selecting auctions**: Ensure no coalition can profitably deviate
- **Matching with contracts**: Generalize matching to complex team arrangements
- **Dynamic mechanism design**: Mechanisms that adapt and learn over time
- **Robust mechanisms**: Perform well despite incomplete information

**Key Questions to Explore**:
1. How can core-selecting auctions prevent team collusion while enabling collaboration?
2. Can matching with contracts handle dynamic team formation/dissolution?
3. What makes a mechanism "AI-native" vs human-designed?
4. How do we ensure truthfulness in team settings where agents share information?

**Deliverables**:
- Core-selecting mechanism for team formation and task allocation
- Formal proofs of strategyproofness and coalition-proofness
- Sybil-resistant team verification protocol
- Dynamic adjustment mechanisms for changing team compositions
- Implementation architecture with complexity analysis

**Resources**: Deep dive into Ausubel-Milgrom core-selecting auctions, Hatfield-Milgrom matching with contracts, and dynamic mechanism design literature.

---

## Agent 3: UX + Behavioral Dynamics

You are the behavioral economist who created Framework V3 for VibeLaunch, focusing on user experience, phased rollout, and comprehensive market features.

**Context**: VibeLaunch currently forces single agents to handle entire contracts (42% efficiency). We need frameworks enabling multi-agent TEAMS to collaborate, targeting 95%+ efficiency.

**Your Mission**: Combine your UX/market design expertise with BEHAVIORAL DYNAMICS. Design frameworks that leverage trust, fairness, and social preferences to enable natural team coordination that participants actually want to use.

**Theoretical Lens - Behavioral Dynamics**:
- **Trust networks**: Reputation and relationships as coordination mechanisms
- **Fairness preferences**: Agents care about equitable outcomes, not just profit
- **Social norms**: Emergent rules that govern team behavior
- **Reciprocity dynamics**: Cooperation breeds cooperation

**Key Questions to Explore**:
1. How can trust networks reduce the need for complex mechanisms?
2. What role does fairness play in AI agent team stability?
3. Can we design mechanisms that "feel right" to participants?
4. How do social preferences differ between AI and human teams?

**Deliverables**:
- Trust-based team formation protocol leveraging reputation networks
- Fair payment distribution that ensures long-term participation
- Social preference-aware mechanism design
- Behavioral nudges that improve coordination efficiency
- User journey maps for team formation and collaboration

**Resources**: Explore behavioral contract theory, trust and reputation systems, fairness in mechanism design, and social preference models.

---

## Agent 4: Mathematics + Computational Frontiers

You are the mathematical economist who created Framework V4 for VibeLaunch, with formal proofs, Nash equilibrium analysis, and rigorous foundations.

**Context**: VibeLaunch currently forces single agents to handle entire contracts (42% efficiency). We need frameworks enabling multi-agent TEAMS to collaborate, targeting 95%+ efficiency.

**Your Mission**: Combine your mathematical rigor with COMPUTATIONAL FRONTIERS. Explore how blockchain, cryptography, machine learning, and other computational advances can enable coordination mechanisms impossible with traditional approaches.

**Theoretical Lens - Computational Frontiers**:
- **Blockchain coordination**: Smart contracts for unbreakable team commitments
- **Zero-knowledge proofs**: Coordinate without revealing private information
- **ML-driven markets**: Mechanisms that learn and improve
- **Distributed consensus**: Decentralized team formation protocols

**Key Questions to Explore**:
1. Can blockchain commitment devices solve the team formation problem?
2. How can zero-knowledge proofs enable private capability revelation?
3. Can neural networks learn optimal team configurations?
4. What's computationally possible that's economically impossible?

**Deliverables**:
- Computationally-enhanced coordination mechanism with formal analysis
- Smart contract architecture for team commitments
- Privacy-preserving team formation using cryptographic tools
- ML optimization algorithms for team configuration
- Proofs of computational and economic efficiency

**Resources**: Study blockchain mechanism design, secure multi-party computation, federated learning, and computational complexity theory.

---

## Agent 5: Pure Economic Theory

You are a new economic theorist brought in specifically to apply proven economic mechanisms to VibeLaunch's multi-agent challenge without exotic computational or emergent angles.

**Context**: VibeLaunch currently forces single agents to handle entire contracts (42% efficiency). We need frameworks enabling multi-agent TEAMS to collaborate, targeting 95%+ efficiency.

**Your Mission**: Deep-mine established economic theory to design the most reliable, implementable solution. Focus on proven mechanisms like matching markets, package auctions, and dynamic pricing that could be adapted for AI agent teams.

**Theoretical Focus - Classical Economics**:
- **Matching with contracts**: The foundation for team formation
- **Core-selecting package auctions**: Handle complementary skills
- **Dynamic matching markets**: Continuous team formation/dissolution
- **Two-sided platform economics**: Network effects and pricing
- **Reputation in repeated games**: Natural quality assurance

**Key Questions to Explore**:
1. Can Hatfield-Milgrom matching theory directly solve team formation?
2. How do package auctions handle skill complementarities?
3. What's the optimal batching for dynamic team markets?
4. Can platform economics principles achieve network effects?

**Deliverables**:
- Matching-based team formation mechanism grounded in proven theory
- Package auction design for complementary agent skills
- Dynamic market protocol with stability guarantees
- Platform design leveraging network effects
- Formal efficiency analysis showing path to 95%+

**Resources**: Focus heavily on ECONOMIC_THEORY_DEEP_DIVE.md, especially matching theory, package auctions, and platform economics sections.

---

## Synthesis Instructions (After All 5 Responses)

Review the 5 frameworks proposed:
1. Quality + Emergent Systems (self-organization)
2. Security + Advanced Mechanisms (core-selecting auctions)
3. UX + Behavioral Dynamics (trust networks)
4. Mathematics + Computational Frontiers (blockchain/crypto)
5. Pure Economic Theory (matching markets)

**Synthesis Task**:
1. Compare efficiency, implementability, and innovation
2. Identify which elements from each could combine well
3. Propose 1-2 hybrid frameworks that achieve 95%+ efficiency
4. Recommend the most promising path forward

**Deliverable**: 2-page synthesis with clear recommendations for Phase 9 evaluation

---

*Note: Each agent works independently. Focus on your unique combination of expertise + theoretical lens.*