# Create data for phase transition visualization
import numpy as np

# Simulate phase transition efficiency evolution
iterations = range(1, 81)

# Create realistic efficiency progression with phase transitions
efficiency_data = []
phase_data = []

# Exploration phase (iterations 1-10)
exploration_efficiency = np.linspace(70, 85, 10) + np.random.normal(0, 2, 10)
exploration_phases = ['exploration'] * 10

# Exploitation phase (iterations 11-25)  
exploitation_efficiency = np.linspace(85, 92, 15) + np.random.normal(0, 1.5, 15)
exploitation_phases = ['exploitation'] * 15

# Crystallization phase (iterations 26-80)
crystallization_base = np.linspace(92, 100, 55)
crystallization_noise = np.random.normal(0, 0.5, 55)
crystallization_efficiency = np.clip(crystallization_base + crystallization_noise, 92, 100)
crystallization_phases = ['crystallization'] * 55

# Combine all phases
all_efficiency = np.concatenate([exploration_efficiency, exploitation_efficiency, crystallization_efficiency])
all_phases = exploration_phases + exploitation_phases + crystallization_phases

# Create the data for charting
phase_transition_data = []
for i, (eff, phase) in enumerate(zip(all_efficiency, all_phases)):
    phase_transition_data.append({
        'iteration': i + 1,
        'efficiency': round(float(eff), 1),
        'phase': phase
    })

print("Phase transition data created for visualization")
print(f"Data points: {len(phase_transition_data)}")
print(f"Final efficiency: {phase_transition_data[-1]['efficiency']}%")

# Save as JSON for the chart
import json
with open('phase_transition_data.json', 'w') as f:
    json.dump(phase_transition_data, f)
    
print("Data saved to phase_transition_data.json")