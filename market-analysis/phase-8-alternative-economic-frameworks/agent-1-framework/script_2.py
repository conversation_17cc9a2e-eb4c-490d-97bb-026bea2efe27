class EfficiencyAnalyzer:
    """
    Analyzes allocative efficiency of stigmergic vs traditional mechanisms
    """
    def __init__(self):
        self.mechanisms = {}
        
    def calculate_optimal_allocation(self, agents: List[Agent], contracts: List[Contract]) -> float:
        """Calculate theoretical optimal value (upper bound)"""
        total_optimal_value = 0
        
        for contract in contracts:
            # Find best possible team for each contract
            max_quality = 0
            
            # Try all possible team combinations (exponential, but needed for optimum)
            from itertools import combinations
            
            for team_size in range(1, min(5, len(agents) + 1)):
                for team_combo in combinations(agents, team_size):
                    team_capability = {}
                    
                    # Aggregate team capabilities
                    for agent in team_combo:
                        for skill, level in agent.capabilities.items():
                            if skill not in team_capability:
                                team_capability[skill] = 0
                            team_capability[skill] += level * agent.reputation
                    
                    # Calculate quality score
                    quality_score = 0
                    for skill, required_level in contract.requirements.items():
                        team_level = team_capability.get(skill, 0)
                        quality_score += min(team_level, required_level) / required_level
                    
                    quality_score /= len(contract.requirements)
                    max_quality = max(max_quality, quality_score)
            
            # Value = quality * budget (simplified value function)
            total_optimal_value += max_quality * contract.budget
            
        return total_optimal_value
    
    def calculate_mechanism_efficiency(self, mechanism_name: str, 
                                     achieved_value: float, optimal_value: float) -> float:
        """Calculate allocative efficiency as percentage of optimal"""
        if optimal_value == 0:
            return 100.0
        return (achieved_value / optimal_value) * 100.0
    
    def simulate_vcg_mechanism(self, agents: List[Agent], contracts: List[Contract]) -> float:
        """Simulate traditional VCG mechanism (single agent per contract)"""
        total_value = 0
        
        for contract in contracts:
            best_agent = None
            best_value = 0
            
            for agent in agents:
                if agent.workload > 0.8:
                    continue
                    
                # Single agent quality calculation
                quality_score = 0
                for skill, required_level in contract.requirements.items():
                    agent_level = agent.capabilities.get(skill, 0)
                    quality_score += min(agent_level * agent.reputation, required_level) / required_level
                
                quality_score /= len(contract.requirements)
                value = quality_score * contract.budget
                
                if value > best_value:
                    best_value = value
                    best_agent = agent
            
            if best_agent:
                total_value += best_value
                best_agent.workload += 0.3  # Increase workload
        
        return total_value
    
    def simulate_stigmergic_mechanism(self, marketplace: StigmergicMarketplace) -> float:
        """Simulate stigmergic team formation mechanism"""
        total_value = 0
        
        for contract in marketplace.contracts:
            team = marketplace.emergent_team_formation(contract)
            if team:
                quality = marketplace.calculate_team_quality_gradient(team, contract)
                value = quality * contract.budget
                total_value += value
                
                # Update agent workloads
                for agent in team:
                    agent.workload += 0.2 / len(team)  # Distributed workload
        
        return total_value

# Run comprehensive efficiency analysis
print("=== EFFICIENCY ANALYSIS ===\n")

# Reset agent workloads
for agent in agents:
    agent.workload = 0.0

analyzer = EfficiencyAnalyzer()

# Calculate optimal allocation (theoretical maximum)
optimal_value = analyzer.calculate_optimal_allocation(agents, contracts)
print(f"Theoretical optimal value: ${optimal_value:,.2f}")

# Test VCG mechanism (current VibeLaunch approach)
for agent in agents:
    agent.workload = 0.0  # Reset

vcg_value = analyzer.simulate_vcg_mechanism(agents, contracts)
vcg_efficiency = analyzer.calculate_mechanism_efficiency("VCG", vcg_value, optimal_value)

print(f"\nVCG Mechanism (single agents):")
print(f"  Achieved value: ${vcg_value:,.2f}")
print(f"  Efficiency: {vcg_efficiency:.1f}%")

# Test stigmergic mechanism  
for agent in agents:
    agent.workload = 0.0  # Reset

stigmergic_value = analyzer.simulate_stigmergic_mechanism(marketplace)
stigmergic_efficiency = analyzer.calculate_mechanism_efficiency("Stigmergic", stigmergic_value, optimal_value)

print(f"\nStigmergic Mechanism (emergent teams):")
print(f"  Achieved value: ${stigmergic_value:,.2f}")
print(f"  Efficiency: {stigmergic_efficiency:.1f}%")

# Calculate improvement
improvement = stigmergic_efficiency - vcg_efficiency
print(f"\nEfficiency improvement: +{improvement:.1f} percentage points")
print(f"Value improvement: ${stigmergic_value - vcg_value:,.2f} ({((stigmergic_value/vcg_value - 1) * 100):.1f}% increase)")

# Test with more complex scenarios
print("\n=== SCALABILITY TEST ===")

# Create larger scenario
additional_agents = [
    Agent("specialist_writer", {"content": 0.95, "copywriting": 0.9}, 0.8),
    Agent("data_scientist", {"analytics": 0.95, "strategy": 0.8}, 0.85),
    Agent("brand_expert", {"branding": 0.9, "design": 0.7, "strategy": 0.6}, 0.9),
    Agent("social_media_mgr", {"social": 0.9, "content": 0.6, "analytics": 0.5}, 0.75),
]

complex_contracts = [
    Contract("enterprise_rebrand", {"design": 0.9, "branding": 0.95, "content": 0.8, "strategy": 0.7}, 15000, 20),
    Contract("data_driven_campaign", {"analytics": 0.95, "content": 0.7, "seo": 0.8, "strategy": 0.6}, 12000, 18),
    Contract("social_campaign", {"social": 0.9, "content": 0.8, "design": 0.6, "analytics": 0.5}, 8000, 14),
]

large_agents = agents + additional_agents
large_marketplace = StigmergicMarketplace(large_agents, complex_contracts)

# Position new agents
for i, agent in enumerate(additional_agents):
    agent.location = ((i + len(agents)) * 0.5, sum(agent.capabilities.values()) / len(agent.capabilities))

large_optimal = analyzer.calculate_optimal_allocation(large_agents, complex_contracts)
large_stigmergic = analyzer.simulate_stigmergic_mechanism(large_marketplace)
large_efficiency = analyzer.calculate_mechanism_efficiency("Large Stigmergic", large_stigmergic, large_optimal)

print(f"Large-scale scenario:")
print(f"  Agents: {len(large_agents)}")
print(f"  Contracts: {len(complex_contracts)}")
print(f"  Optimal value: ${large_optimal:,.2f}")
print(f"  Achieved value: ${large_stigmergic:,.2f}")
print(f"  Efficiency: {large_efficiency:.1f}%")

if large_efficiency >= 95:
    print(f"✓ TARGET ACHIEVED: {large_efficiency:.1f}% efficiency exceeds 95% threshold!")