import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime, timedelta

# Data for the Gantt chart
data = [
    {"task": "Core pheromone system", "phase": "Phase 1", "start": 1, "duration": 2, "efficiency_target": "75-80%"},
    {"task": "Quality signal APIs", "phase": "Phase 1", "start": 2, "duration": 2, "efficiency_target": "75-80%"}, 
    {"task": "Basic team formation", "phase": "Phase 1", "start": 2, "duration": 2, "efficiency_target": "75-80%"},
    {"task": "Reputation tracking", "phase": "Phase 1", "start": 1, "duration": 3, "efficiency_target": "75-80%"},
    {"task": "Pattern learning system", "phase": "Phase 2", "start": 4, "duration": 2, "efficiency_target": "85-90%"},
    {"task": "Phase transition detection", "phase": "Phase 2", "start": 5, "duration": 2, "efficiency_target": "85-90%"},
    {"task": "Algorithm optimization", "phase": "Phase 2", "start": 4, "duration": 3, "efficiency_target": "85-90%"},
    {"task": "Quality prediction models", "phase": "Phase 2", "start": 5, "duration": 2, "efficiency_target": "85-90%"},
    {"task": "Full phase transition system", "phase": "Phase 3", "start": 7, "duration": 2, "efficiency_target": "95-100%"},
    {"task": "Crystallization bonuses", "phase": "Phase 3", "start": 8, "duration": 2, "efficiency_target": "95-100%"},
    {"task": "Failure recovery", "phase": "Phase 3", "start": 7, "duration": 3, "efficiency_target": "95-100%"},
    {"task": "Analytics dashboard", "phase": "Phase 3", "start": 8, "duration": 2, "efficiency_target": "95-100%"},
    {"task": "Multi-tenant security", "phase": "Phase 4", "start": 10, "duration": 2, "efficiency_target": "Production"},
    {"task": "Real-time monitoring", "phase": "Phase 4", "start": 10, "duration": 3, "efficiency_target": "Production"},
    {"task": "API ecosystem", "phase": "Phase 4", "start": 11, "duration": 2, "efficiency_target": "Production"},
    {"task": "Performance optimization", "phase": "Phase 4", "start": 10, "duration": 3, "efficiency_target": "Production"}
]

# Create DataFrame
df = pd.DataFrame(data)

# Calculate end dates
df['end'] = df['start'] + df['duration']

# Create base date for visualization (Jan 1, 2024)
base_date = datetime(2024, 1, 1)

# Convert months to actual dates
df['start_date'] = df['start'].apply(lambda x: base_date + timedelta(days=(x-1)*30))
df['end_date'] = df['end'].apply(lambda x: base_date + timedelta(days=(x-1)*30))

# Create better abbreviated task names within 15 characters
task_abbreviations = {
    "Core pheromone system": "Core Pheromone",
    "Quality signal APIs": "Quality APIs",
    "Basic team formation": "Team Formation", 
    "Reputation tracking": "Reputation Sys",
    "Pattern learning system": "Pattern Learn",
    "Phase transition detection": "Phase Detect",
    "Algorithm optimization": "Algorithm Opt",
    "Quality prediction models": "Quality Pred",
    "Full phase transition system": "Full Phase Sys",
    "Crystallization bonuses": "Crystal Bonus",
    "Failure recovery": "Failure Recov",
    "Analytics dashboard": "Analytics Dash",
    "Multi-tenant security": "Multi-tenant",
    "Real-time monitoring": "Real-time Mon",
    "API ecosystem": "API Ecosystem",
    "Performance optimization": "Performance"
}

df['task_short'] = df['task'].map(task_abbreviations)

# Add efficiency target to task label
df['task_with_target'] = df['task_short'] + ' (' + df['efficiency_target'] + ')'

# Create color mapping for phases
phase_colors = {
    'Phase 1': '#1FB8CD',
    'Phase 2': '#FFC185', 
    'Phase 3': '#ECEBD5',
    'Phase 4': '#5D878F'
}

# Create Gantt chart using timeline
fig = px.timeline(df, 
                  x_start="start_date", 
                  x_end="end_date",
                  y="task_with_target",
                  color="phase",
                  color_discrete_map=phase_colors,
                  title="Quality Framework Roadmap")

# Update layout
fig.update_layout(
    xaxis_title="Timeline",
    yaxis_title="Tasks",
    legend=dict(orientation='h', yanchor='bottom', y=1.05, xanchor='center', x=0.5)
)

# Update x-axis to show months M1-M12 only
fig.update_xaxes(
    tickmode='array',
    tickvals=[base_date + timedelta(days=i*30) for i in range(12)],
    ticktext=[f'M{i+1}' for i in range(12)]
)

# Save the chart
fig.write_image("gantt_chart.png")