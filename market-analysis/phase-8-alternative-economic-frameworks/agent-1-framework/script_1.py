class StigmergicMarketplace:
    """
    Emergent coordination marketplace using stigmergic principles
    """
    def __init__(self, agents: List[Agent], contracts: List[Contract]):
        self.agents = agents
        self.contracts = contracts
        self.pheromone_grid = {}  # location -> List[QualityPheromone]
        self.team_formations = []  # successful team patterns
        self.efficiency_history = []
        self.time_step = 0
        
    def calculate_skill_distance(self, agent_skills: Dict[str, float], 
                                required_skills: Dict[str, float]) -> float:
        """Calculate Euclidean distance in skill space"""
        total_distance = 0
        for skill, required_level in required_skills.items():
            agent_level = agent_skills.get(skill, 0)
            total_distance += (required_level - agent_level) ** 2
        return np.sqrt(total_distance)
    
    def deposit_quality_pheromone(self, agent: Agent, contract: Contract, 
                                 quality_achieved: float, location: Tuple[float, float]):
        """Agent deposits pheromone based on quality delivery"""
        for skill in contract.requirements:
            if skill in agent.capabilities:
                pheromone = QualityPheromone(
                    skill_type=skill,
                    location=location,
                    strength=quality_achieved * agent.reputation,
                    quality_signal=quality_achieved,
                    evaporation_rate=0.95
                )
                
                if location not in self.pheromone_grid:
                    self.pheromone_grid[location] = []
                self.pheromone_grid[location].append(pheromone)
    
    def sense_pheromones(self, agent: Agent, search_radius: float = 1.0) -> List[QualityPheromone]:
        """Agent senses nearby quality pheromones"""
        detected_pheromones = []
        agent_location = agent.location
        
        for location, pheromones in self.pheromone_grid.items():
            distance = np.sqrt((location[0] - agent_location[0])**2 + 
                             (location[1] - agent_location[1])**2)
            if distance <= search_radius:
                detected_pheromones.extend(pheromones)
                
        return detected_pheromones
    
    def calculate_team_quality_gradient(self, team: List[Agent], contract: Contract) -> float:
        """Calculate the quality gradient attracting team formation"""
        total_capability = {}
        
        # Aggregate team capabilities
        for agent in team:
            for skill, level in agent.capabilities.items():
                if skill not in total_capability:
                    total_capability[skill] = 0
                total_capability[skill] += level * agent.reputation
        
        # Calculate how well team meets requirements
        quality_score = 0
        for skill, required_level in contract.requirements.items():
            team_level = total_capability.get(skill, 0)
            if team_level >= required_level:
                quality_score += 1.0  # Full points for meeting requirement
            else:
                quality_score += team_level / required_level  # Partial credit
        
        return quality_score / len(contract.requirements)
    
    def emergent_team_formation(self, contract: Contract) -> List[Agent]:
        """
        Agents self-organize into teams following quality gradients
        Mimics ant colony optimization for team formation
        """
        max_team_size = min(4, len(self.agents))  # Limit team size
        best_team = []
        best_quality = 0
        
        # Simple emergent rule: agents with complementary skills attract each other
        for primary_agent in self.agents:
            if primary_agent.workload > 0.8:  # Skip overloaded agents
                continue
                
            current_team = [primary_agent]
            remaining_requirements = contract.requirements.copy()
            
            # Reduce requirements based on primary agent capabilities
            for skill, level in primary_agent.capabilities.items():
                if skill in remaining_requirements:
                    remaining_requirements[skill] = max(0, 
                        remaining_requirements[skill] - level * primary_agent.reputation)
            
            # Attract complementary agents based on pheromone trails
            for _ in range(max_team_size - 1):
                best_complement = None
                best_complement_score = 0
                
                for candidate in self.agents:
                    if candidate in current_team or candidate.workload > 0.8:
                        continue
                    
                    # Calculate attraction based on skill complementarity
                    complement_score = 0
                    for skill, remaining_level in remaining_requirements.items():
                        if remaining_level > 0 and skill in candidate.capabilities:
                            contribution = candidate.capabilities[skill] * candidate.reputation
                            complement_score += min(contribution, remaining_level)
                    
                    # Add pheromone influence (agents follow successful patterns)
                    pheromones = self.sense_pheromones(candidate)
                    pheromone_bonus = sum(p.quality_signal * p.strength 
                                        for p in pheromones if p.skill_type in remaining_requirements)
                    complement_score += pheromone_bonus * 0.1  # Weight pheromone influence
                    
                    if complement_score > best_complement_score:
                        best_complement = candidate
                        best_complement_score = complement_score
                
                if best_complement:
                    current_team.append(best_complement)
                    # Update remaining requirements
                    for skill, level in best_complement.capabilities.items():
                        if skill in remaining_requirements:
                            remaining_requirements[skill] = max(0,
                                remaining_requirements[skill] - level * best_complement.reputation)
                else:
                    break  # No more beneficial agents found
            
            # Evaluate team quality
            team_quality = self.calculate_team_quality_gradient(current_team, contract)
            if team_quality > best_quality:
                best_quality = team_quality
                best_team = current_team
        
        return best_team
    
    def evaporate_pheromones(self):
        """Evaporate pheromones over time to prevent local optima"""
        for location in list(self.pheromone_grid.keys()):
            remaining_pheromones = []
            for pheromone in self.pheromone_grid[location]:
                pheromone.strength *= pheromone.evaporation_rate
                pheromone.age += 1
                
                # Keep pheromones that are still strong enough
                if pheromone.strength > 0.01:
                    remaining_pheromones.append(pheromone)
            
            if remaining_pheromones:
                self.pheromone_grid[location] = remaining_pheromones
            else:
                del self.pheromone_grid[location]

# Test the emergent team formation
print("Testing emergent team formation mechanism...")

marketplace = StigmergicMarketplace(agents, contracts)

# Position agents in skill space (for visualization)
for i, agent in enumerate(agents):
    agent.location = (i * 0.5, sum(agent.capabilities.values()) / len(agent.capabilities))

print(f"\nAgent positions in skill space:")
for agent in agents:
    print(f"{agent.id}: {agent.location}, avg_skill: {sum(agent.capabilities.values())/len(agent.capabilities):.2f}")

# Test team formation for each contract
for contract in contracts:
    print(f"\n--- Forming team for {contract.id} ---")
    team = marketplace.emergent_team_formation(contract)
    
    if team:
        print(f"Formed team of {len(team)} agents:")
        for agent in team:
            print(f"  - {agent.id}: {agent.capabilities}")
        
        # Calculate team effectiveness
        quality = marketplace.calculate_team_quality_gradient(team, contract)
        print(f"Team quality score: {quality:.3f}")
        
        # Simulate successful delivery and pheromone deposition
        if quality > 0.7:  # High quality delivery
            for agent in team:
                marketplace.deposit_quality_pheromone(agent, contract, quality, agent.location)
            print(f"✓ High quality delivery - pheromones deposited")
    else:
        print("No suitable team found")

print(f"\nPheromone locations created: {len(marketplace.pheromone_grid)}")
print(f"Total pheromones: {sum(len(pheromones) for pheromones in marketplace.pheromone_grid.values())}")