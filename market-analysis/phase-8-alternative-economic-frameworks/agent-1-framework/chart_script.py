import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np

# Create the data
data = [
    {"mechanism": "VibeLaunch Current", "efficiency": 42.0, "status": "poor"},
    {"mechanism": "Traditional VCG", "efficiency": 64.0, "status": "poor"}, 
    {"mechanism": "Basic Stigmergic", "efficiency": 100.0, "status": "excellent"},
    {"mechanism": "Realistic Stigmergic", "efficiency": 84.1, "status": "poor"},
    {"mechanism": "Optimized Stigmergic", "efficiency": 91.1, "status": "good"},
    {"mechanism": "Phase Transition", "efficiency": 100.0, "status": "excellent"}
]

df = pd.DataFrame(data)

# Define colors based on efficiency thresholds - using proper colors
def get_color(efficiency):
    if efficiency < 85:
        return '#B4413C'  # Red for <85%
    elif efficiency < 95:
        return '#FFC185'  # Orange for 85-94%
    else:
        return '#13A538'  # Green for 95%+ (using a proper green color)

# Add color column and performance category
df['color'] = df['efficiency'].apply(get_color)
df['category'] = df['efficiency'].apply(lambda x: 'Poor (<85%)' if x < 85 else 'Good (85-94%)' if x < 95 else 'Excellent (95%+)')

# Keep mechanism names but make them fit better
df['display_name'] = df['mechanism'].str.replace('Stigmergic', 'Stig')

# Create horizontal bar chart
fig = go.Figure()

# Get unique categories and colors for legend
categories = df['category'].unique()
colors = {
    'Poor (<85%)': '#B4413C',
    'Good (85-94%)': '#FFC185', 
    'Excellent (95%+)': '#13A538'
}

# Add bars for each category to create legend
for category in ['Poor (<85%)', 'Good (85-94%)', 'Excellent (95%+)']:
    category_data = df[df['category'] == category]
    if not category_data.empty:
        fig.add_trace(go.Bar(
            y=category_data['display_name'],
            x=category_data['efficiency'],
            orientation='h',
            marker_color=colors[category],
            text=[f"{eff}%" for eff in category_data['efficiency']],
            textposition='inside',
            textfont=dict(color='white', size=12),
            hovertemplate='<b>%{y}</b><br>Efficiency: %{x}%<extra></extra>',
            name=category,
            cliponaxis=False
        ))

# Add red dashed line at 95%
fig.add_vline(x=95, line_dash="dash", line_color="red", line_width=2)

# Update layout with legend
fig.update_layout(
    title="Market Mechanism Efficiency Comparison",
    xaxis_title="Efficiency (%)",
    yaxis_title="Mechanism",
    xaxis=dict(range=[0, 105]),
    legend=dict(orientation='h', yanchor='bottom', y=1.05, xanchor='center', x=0.5)
)

# Save the chart
fig.write_image("market_efficiency_comparison.png")