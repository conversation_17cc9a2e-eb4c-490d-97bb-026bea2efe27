import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from dataclasses import dataclass
from typing import List, Dict, Tuple
import json

# Let me start by modeling the core components of an emergent stigmergic system for VibeLaunch

@dataclass
class Agent:
    """AI Agent with capabilities and reputation"""
    id: str
    capabilities: Dict[str, float]  # skill -> proficiency (0-1)
    reputation: float = 0.5  # 0-1 scale
    workload: float = 0.0    # current workload (0-1)
    location: Tuple[float, float] = (0.0, 0.0)  # position in skill space
    
@dataclass 
class Contract:
    """Marketing contract requiring multiple skills"""
    id: str
    requirements: Dict[str, float]  # skill -> required level
    budget: float
    deadline: int  # time steps
    complexity: float = 1.0
    
@dataclass
class QualityPheromone:
    """Digital pheromone encoding quality signals"""
    skill_type: str
    location: Tuple[float, float]
    strength: float = 1.0
    quality_signal: float = 0.5  # 0-1 quality indicator
    evaporation_rate: float = 0.95
    age: int = 0
    
# Create sample data for testing
print("Setting up emergent coordination simulation...")

# Sample agents with different specializations
agents = [
    Agent("content_creator_1", {"content": 0.9, "seo": 0.3, "design": 0.2}, 0.8),
    Agent("seo_specialist_1", {"seo": 0.95, "content": 0.4, "analytics": 0.6}, 0.7),
    Agent("designer_1", {"design": 0.9, "content": 0.3, "branding": 0.8}, 0.85),
    Agent("analytics_expert", {"analytics": 0.9, "seo": 0.5, "strategy": 0.7}, 0.9),
    Agent("content_creator_2", {"content": 0.8, "social": 0.7, "design": 0.4}, 0.6),
    Agent("generalist_1", {"content": 0.6, "seo": 0.5, "design": 0.5, "analytics": 0.4}, 0.5),
]

# Sample contracts requiring team collaboration
contracts = [
    Contract("campaign_1", {"content": 0.8, "seo": 0.7, "design": 0.6}, 5000, 10),
    Contract("rebrand_project", {"design": 0.9, "content": 0.6, "branding": 0.8}, 8000, 15),
    Contract("analytics_campaign", {"analytics": 0.9, "seo": 0.8, "content": 0.5}, 6000, 12),
]

print(f"Created {len(agents)} agents and {len(contracts)} contracts")
print("\nAgent capabilities:")
for agent in agents[:3]:  # Show first 3
    print(f"{agent.id}: {agent.capabilities} (rep: {agent.reputation:.2f})")
    
print("\nContract requirements:")
for contract in contracts:
    print(f"{contract.id}: {contract.requirements} (budget: ${contract.budget})")