import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import json

# Load the data
with open('phase_transition_data.json', 'r') as f:
    data = json.load(f)

# Convert to DataFrame
df = pd.DataFrame(data)

# Create the figure
fig = go.Figure()

# Define colors for each phase using brand colors (using darker cyan for crystallization for better visibility)
phase_colors = {
    'exploration': '#1FB8CD',  # Strong cyan
    'exploitation': '#FFC185',  # Light orange
    'crystallization': '#5D878F'  # Darker cyan (more visible than light green)
}

# Add line segments for each phase
for phase in df['phase'].unique():
    phase_data = df[df['phase'] == phase]
    fig.add_trace(go.Scatter(
        x=phase_data['iteration'],
        y=phase_data['efficiency'],
        mode='lines',
        name=phase.capitalize(),
        line=dict(color=phase_colors[phase], width=3),
        cliponaxis=False
    ))

# Add horizontal dashed line at 95% efficiency target
fig.add_hline(y=95, line_dash="dash", line_color="gray", opacity=0.7)

# Add vertical lines for phase transitions (around iterations 10 and 25)
fig.add_vline(x=10, line_dash="dot", line_color="gray", opacity=0.5)
fig.add_vline(x=25, line_dash="dot", line_color="gray", opacity=0.5)

# Update layout
fig.update_layout(
    title="Market Evolution: Phase Transitions",
    xaxis_title="Iteration",
    yaxis_title="Efficiency (%)",
    legend=dict(orientation='h', yanchor='bottom', y=1.05, xanchor='center', x=0.5)
)

# Update axes
fig.update_xaxes(range=[1, 80])
fig.update_yaxes(range=[0, 100])

# Save as PNG
fig.write_image("market_efficiency_evolution.png")