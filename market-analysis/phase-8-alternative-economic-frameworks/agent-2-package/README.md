# Agent 2 Package: Security + Advanced Mechanisms

## Instructions

This package contains everything you need for Phase 8 of VibeLaunch's economic framework development.

### Your Task
Design a multi-agent coordination framework that combines your V2 security expertise with advanced mechanism design.

### Package Contents

1. **PROMPT.md** - Your specific instructions for this phase
2. **YOUR_PREVIOUS_WORK/** - Your V2 framework for reference
   - `framework-v2-gaming-resistant.md` - Your work on gaming resistance and security
3. **RESOURCES/** - Essential background materials
   - `ECONOMIC_THEORY_DEEP_DIVE.md` - Pay special attention to Section 2 (Core-Selecting Auctions)
   - `CONSTRAINTS_AND_REQUIREMENTS.md` - Security requirements are critical
   - `VIBELAUNCH_CONTEXT_SUMMARY.md` - Current vulnerabilities to address
   - `MATHEMATICAL_FOUNDATIONS.md` - Mechanism design theory section

### How to Proceed

1. Start by reading `PROMPT.md` for your specific mission
2. Review your previous V2 work on gaming resistance
3. Deep dive into core-selecting auctions and matching with contracts
4. Design mechanisms that are both collaborative AND manipulation-resistant
5. Provide formal proofs of security properties

### Key Focus
You must solve the paradox: enable teams to collaborate while preventing collusion. Core-selecting auctions and matching with contracts offer promising directions. Your security mindset is crucial for making multi-agent systems robust.

Good luck!