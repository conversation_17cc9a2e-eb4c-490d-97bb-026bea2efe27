# Your Prompt for Phase 8: Multi-Agent Team Coordination

## Agent 2: Security + Advanced Mechanisms

You are the mechanism design expert who previously created Framework V2 for VibeLaunch, focusing on gaming resistance, sybil attack prevention, and secure mechanisms.

**Context**: VibeLaunch currently forces single agents to handle entire contracts (42% efficiency). We need frameworks enabling multi-agent TEAMS to collaborate, targeting 95%+ efficiency.

**Your Mission**: Combine your security expertise with ADVANCED MECHANISM DESIGN. Create manipulation-resistant mechanisms specifically designed for AI agent teams, going beyond traditional VCG to explore core-selecting auctions, matching with contracts, and novel truthful mechanisms.

**Theoretical Lens - Advanced Mechanisms**:
- **Core-selecting auctions**: Ensure no coalition can profitably deviate
- **Matching with contracts**: Generalize matching to complex team arrangements
- **Dynamic mechanism design**: Mechanisms that adapt and learn over time
- **Robust mechanisms**: Perform well despite incomplete information

**Key Questions to Explore**:
1. How can core-selecting auctions prevent team collusion while enabling collaboration?
2. Can matching with contracts handle dynamic team formation/dissolution?
3. What makes a mechanism "AI-native" vs human-designed?
4. How do we ensure truthfulness in team settings where agents share information?

**Deliverables**:
- Core-selecting mechanism for team formation and task allocation
- Formal proofs of strategyproofness and coalition-proofness
- Sybil-resistant team verification protocol
- Dynamic adjustment mechanisms for changing team compositions
- Implementation architecture with complexity analysis

**Resources to Reference**:
- Your previous V2 work in `YOUR_PREVIOUS_WORK/` folder
- Economic theory in `RESOURCES/ECONOMIC_THEORY_DEEP_DIVE.md` (especially core-selecting auctions)
- System constraints in `RESOURCES/CONSTRAINTS_AND_REQUIREMENTS.md`
- VibeLaunch context in `RESOURCES/VIBELAUNCH_CONTEXT_SUMMARY.md`
- Mathematical tools in `RESOURCES/MATHEMATICAL_FOUNDATIONS.md`

Deep dive into Ausubel-Milgrom core-selecting auctions, Hatfield-Milgrom matching with contracts, and dynamic mechanism design literature. Your security expertise combined with advanced mechanisms could create an unmanipulable multi-agent system.

---

## Package Instructions (from README)

### Your Task
Design a multi-agent coordination framework that combines your V2 security expertise with advanced mechanism design.

### Package Contents

1. **PROMPT.md** - Your specific instructions for this phase (this document)
2. **YOUR_PREVIOUS_WORK/** - Your V2 framework for reference
   - `framework-v2-gaming-resistant.md` - Your work on gaming resistance and security
3. **RESOURCES/** - Essential background materials
   - `ECONOMIC_THEORY_DEEP_DIVE.md` - Pay special attention to Section 2 (Core-Selecting Auctions)
   - `CONSTRAINTS_AND_REQUIREMENTS.md` - Security requirements are critical
   - `VIBELAUNCH_CONTEXT_SUMMARY.md` - Current vulnerabilities to address
   - `MATHEMATICAL_FOUNDATIONS.md` - Mechanism design theory section

### How to Proceed

1. Review your previous V2 work on gaming resistance
2. Deep dive into core-selecting auctions and matching with contracts
3. Design mechanisms that are both collaborative AND manipulation-resistant
4. Provide formal proofs of security properties

### Key Focus
You must solve the paradox: enable teams to collaborate while preventing collusion. Core-selecting auctions and matching with contracts offer promising directions. Your security mindset is crucial for making multi-agent systems robust.