# VibeLaunch Greenfield Development Guide
## Building Production-Ready Multi-Tenant SaaS Applications

This guide extracts battle-tested patterns, configurations, and best practices from the VibeLaunch marketplace platform to help you build similar applications from scratch.

## Table of Contents
1. [Quick Start Template](#quick-start-template)
2. [Architecture Patterns](#architecture-patterns)
3. [Database Design](#database-design)
4. [Authentication & Security](#authentication--security)
5. [Real-time Systems](#real-time-systems)
6. [Deployment Configurations](#deployment-configurations)
7. [Development Workflow](#development-workflow)
8. [Testing Strategy](#testing-strategy)
9. [Production Checklist](#production-checklist)

---

## Quick Start Template

### Project Structure
```
your-project/
├── packages/              # Monorepo structure
│   ├── api/              # Backend API service
│   ├── ui/               # Frontend application
│   ├── worker/           # Background job processor
│   ├── types/            # Shared TypeScript types
│   └── scripts/          # Utility scripts
├── supabase/
│   ├── migrations/       # Database migrations
│   └── functions/        # Edge functions
├── docker/               # Docker configurations
├── docs/                 # Documentation
├── __tests__/            # Test suites
└── .github/              # GitHub Actions workflows
```

### Initial Setup Script
```bash
#!/bin/bash
# setup-project.sh

# Create project structure
mkdir -p packages/{api,ui,worker,types,scripts}
mkdir -p supabase/{migrations,functions}
mkdir -p docker docs __tests__/{unit,integration,e2e}

# Initialize pnpm workspace
cat > pnpm-workspace.yaml << EOF
packages:
  - 'packages/*'
EOF

# Initialize root package.json
cat > package.json << EOF
{
  "name": "@your-project/monorepo",
  "private": true,
  "scripts": {
    "dev": "pnpm -r --parallel dev",
    "build": "pnpm -r build",
    "test": "pnpm -r test",
    "lint": "pnpm -r lint",
    "type-check": "pnpm -r type-check"
  },
  "devDependencies": {
    "@types/node": "^18.0.0",
    "typescript": "^5.0.0"
  }
}
EOF

# Initialize TypeScript config
cat > tsconfig.base.json << EOF
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "lib": ["ES2022"],
    "moduleResolution": "node",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "composite": true
  }
}
EOF
```

---

## Architecture Patterns

### Event-Driven Architecture

```typescript
// packages/types/src/events.ts
export interface BaseEvent {
  id: string;
  type: string;
  organizationId: string;
  userId?: string;
  payload: unknown;
  metadata: {
    timestamp: string;
    version: string;
    correlationId?: string;
    causationId?: string;
  };
}

export interface EventBus {
  publish<T extends BaseEvent>(event: T): Promise<void>;
  subscribe<T extends BaseEvent>(
    eventType: string,
    handler: (event: T) => Promise<void>
  ): () => void;
}
```

### PostgreSQL Event Bus Implementation
```sql
-- supabase/migrations/001_event_system.sql
CREATE TABLE bus_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  event_type TEXT NOT NULL,
  payload JSONB NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT now(),
  processed BOOLEAN DEFAULT false,
  processed_at TIMESTAMPTZ,
  error TEXT
);

-- Create notification trigger
CREATE OR REPLACE FUNCTION notify_bus_event() RETURNS TRIGGER AS $$
BEGIN
  PERFORM pg_notify(
    NEW.event_type,
    json_build_object(
      'id', NEW.id,
      'organization_id', NEW.organization_id,
      'payload', NEW.payload,
      'metadata', NEW.metadata
    )::text
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER bus_events_notify
  AFTER INSERT ON bus_events
  FOR EACH ROW
  EXECUTE FUNCTION notify_bus_event();

-- Create indexes for performance
CREATE INDEX idx_bus_events_org_type ON bus_events(organization_id, event_type);
CREATE INDEX idx_bus_events_created ON bus_events(created_at DESC);
CREATE INDEX idx_bus_events_processed ON bus_events(processed, created_at);
```

### Multi-Tenant Architecture

```sql
-- supabase/migrations/002_multi_tenant.sql

-- Organizations table
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Users/Profiles with organization association
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  organization_id UUID REFERENCES organizations(id),
  email TEXT NOT NULL,
  full_name TEXT,
  role TEXT NOT NULL DEFAULT 'member',
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- RLS Policies Template
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Organization members can view their organization
CREATE POLICY "Users can view their organization" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT organization_id FROM profiles 
      WHERE profiles.id = auth.uid()
    )
  );

-- Template for other tables
CREATE OR REPLACE FUNCTION create_tenant_policies(table_name text) RETURNS void AS $$
BEGIN
  -- Enable RLS
  EXECUTE format('ALTER TABLE %I ENABLE ROW LEVEL SECURITY', table_name);
  
  -- Create policies
  EXECUTE format('
    CREATE POLICY "Users can view their org data" ON %I
    FOR SELECT USING (
      organization_id IN (
        SELECT organization_id FROM profiles 
        WHERE profiles.id = auth.uid()
      )
    )', table_name, table_name);
    
  EXECUTE format('
    CREATE POLICY "Users can insert in their org" ON %I
    FOR INSERT WITH CHECK (
      organization_id IN (
        SELECT organization_id FROM profiles 
        WHERE profiles.id = auth.uid()
      )
    )', table_name, table_name);
    
  -- Add similar policies for UPDATE and DELETE
END;
$$ LANGUAGE plpgsql;
```

---

## Database Design

### Performance Optimization Indexes

```sql
-- supabase/migrations/003_performance_indexes.sql

-- Generic indexes for common patterns
CREATE INDEX CONCURRENTLY idx_created_at ON table_name(created_at DESC);
CREATE INDEX CONCURRENTLY idx_updated_at ON table_name(updated_at DESC);
CREATE INDEX CONCURRENTLY idx_org_created ON table_name(organization_id, created_at DESC);

-- JSONB indexes for metadata/settings
CREATE INDEX CONCURRENTLY idx_metadata_gin ON table_name USING GIN(metadata);
CREATE INDEX CONCURRENTLY idx_settings_gin ON table_name USING GIN(settings);

-- Text search indexes
CREATE INDEX CONCURRENTLY idx_search_text ON table_name USING GIN(to_tsvector('english', title || ' ' || description));

-- Partial indexes for common queries
CREATE INDEX CONCURRENTLY idx_active_items ON table_name(organization_id, created_at DESC) 
  WHERE status = 'active';
```

### Connection Pooling Configuration

```ini
# docker/pgbouncer/pgbouncer.ini
[databases]
* = host=host.docker.internal port=5432

[pgbouncer]
listen_port = 6432
listen_addr = *
auth_type = trust
auth_file = /etc/pgbouncer/userlist.txt
pool_mode = transaction
max_client_conn = 1000
default_pool_size = 25
reserve_pool_size = 5
reserve_pool_timeout = 5
max_db_connections = 100
log_connections = 1
log_disconnections = 1
stats_period = 60
```

---

## Authentication & Security

### API Key Encryption Service

```typescript
// packages/api/src/security/encryption.ts
import { createCipheriv, createDecipheriv, randomBytes, scrypt } from 'crypto';
import { promisify } from 'util';

const scryptAsync = promisify(scrypt);

export class EncryptionService {
  private algorithm = 'aes-256-gcm';
  private keyLength = 32;
  
  constructor(private masterKey: string) {
    if (masterKey.length < 32) {
      throw new Error('Encryption key must be at least 32 characters');
    }
  }

  async encrypt(text: string): Promise<string> {
    const iv = randomBytes(16);
    const salt = randomBytes(32);
    const key = await this.deriveKey(salt);
    
    const cipher = createCipheriv(this.algorithm, key, iv);
    const encrypted = Buffer.concat([
      cipher.update(text, 'utf8'),
      cipher.final()
    ]);
    
    const authTag = cipher.getAuthTag();
    
    return Buffer.concat([salt, iv, authTag, encrypted]).toString('base64');
  }

  async decrypt(encryptedData: string): Promise<string> {
    const buffer = Buffer.from(encryptedData, 'base64');
    
    const salt = buffer.slice(0, 32);
    const iv = buffer.slice(32, 48);
    const authTag = buffer.slice(48, 64);
    const encrypted = buffer.slice(64);
    
    const key = await this.deriveKey(salt);
    
    const decipher = createDecipheriv(this.algorithm, key, iv);
    decipher.setAuthTag(authTag);
    
    const decrypted = Buffer.concat([
      decipher.update(encrypted),
      decipher.final()
    ]);
    
    return decrypted.toString('utf8');
  }

  private async deriveKey(salt: Buffer): Promise<Buffer> {
    return (await scryptAsync(this.masterKey, salt, this.keyLength)) as Buffer;
  }
}
```

### Input Validation with Zod

```typescript
// packages/api/src/security/validation.ts
import { z } from 'zod';

// Common validation schemas
export const uuidSchema = z.string().uuid();
export const emailSchema = z.string().email();
export const urlSchema = z.string().url();

// Pagination schema
export const paginationSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

// Request validation middleware
export function validateRequest(schema: z.ZodSchema) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validated = await schema.parseAsync({
        body: req.body,
        query: req.query,
        params: req.params
      });
      
      req.validated = validated;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          error: 'Validation failed',
          details: error.errors
        });
      } else {
        next(error);
      }
    }
  };
}
```

### Rate Limiting

```typescript
// packages/api/src/security/rate-limiter.ts
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  constructor(
    private windowMs: number = 60000, // 1 minute
    private maxRequests: number = 60
  ) {}

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(
      timestamp => now - timestamp < this.windowMs
    );
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    // Cleanup old entries periodically
    if (Math.random() < 0.01) {
      this.cleanup();
    }
    
    return true;
  }
  
  private cleanup() {
    const now = Date.now();
    for (const [key, timestamps] of this.requests.entries()) {
      const valid = timestamps.filter(t => now - t < this.windowMs);
      if (valid.length === 0) {
        this.requests.delete(key);
      } else {
        this.requests.set(key, valid);
      }
    }
  }
}
```

---

## Real-time Systems

### Supabase Real-time Integration

```typescript
// packages/ui/src/hooks/useRealtimeSubscription.ts
import { useEffect, useRef } from 'react';
import { RealtimeChannel } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';

export function useRealtimeSubscription<T>(
  table: string,
  filter?: { column: string; value: string },
  onInsert?: (payload: T) => void,
  onUpdate?: (payload: T) => void,
  onDelete?: (payload: T) => void
) {
  const channelRef = useRef<RealtimeChannel | null>(null);

  useEffect(() => {
    // Build channel name
    const channelName = filter 
      ? `${table}:${filter.column}=eq.${filter.value}`
      : table;

    // Create subscription
    channelRef.current = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table,
          filter: filter ? `${filter.column}=eq.${filter.value}` : undefined
        },
        (payload) => {
          switch (payload.eventType) {
            case 'INSERT':
              onInsert?.(payload.new as T);
              break;
            case 'UPDATE':
              onUpdate?.(payload.new as T);
              break;
            case 'DELETE':
              onDelete?.(payload.old as T);
              break;
          }
        }
      )
      .subscribe();

    // Cleanup
    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [table, filter?.column, filter?.value]);

  return channelRef.current;
}
```

### WebSocket Event System

```typescript
// packages/api/src/lib/websocket-manager.ts
import { WebSocketServer, WebSocket } from 'ws';
import { EventEmitter } from 'events';

export class WebSocketManager extends EventEmitter {
  private wss: WebSocketServer;
  private clients: Map<string, Set<WebSocket>> = new Map();

  constructor(server: any) {
    super();
    this.wss = new WebSocketServer({ server });
    this.setupServer();
  }

  private setupServer() {
    this.wss.on('connection', (ws, req) => {
      const organizationId = this.extractOrgId(req);
      
      // Add to organization's client set
      if (!this.clients.has(organizationId)) {
        this.clients.set(organizationId, new Set());
      }
      this.clients.get(organizationId)!.add(ws);

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.emit('message', { ws, organizationId, message });
        } catch (error) {
          ws.send(JSON.stringify({ error: 'Invalid message format' }));
        }
      });

      ws.on('close', () => {
        this.clients.get(organizationId)?.delete(ws);
      });

      // Send initial connection success
      ws.send(JSON.stringify({ type: 'connected', organizationId }));
    });
  }

  broadcast(organizationId: string, message: any) {
    const clients = this.clients.get(organizationId);
    if (!clients) return;

    const data = JSON.stringify(message);
    clients.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(data);
      }
    });
  }

  private extractOrgId(req: any): string {
    // Extract from auth token or query params
    return req.url.split('org=')[1]?.split('&')[0] || 'default';
  }
}
```

---

## Deployment Configurations

### Multi-Service Dockerfile

```dockerfile
# Dockerfile
FROM node:18-alpine AS base
RUN apk add --no-cache python3 make g++
WORKDIR /app

# Dependencies stage
FROM base AS deps
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/*/package.json ./packages/
RUN npm install -g pnpm && pnpm install --frozen-lockfile

# Build stage
FROM base AS build
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/packages/*/node_modules ./packages/
COPY . .
RUN npm install -g pnpm && pnpm build

# Runtime stage
FROM base AS runtime
ENV NODE_ENV=production
COPY --from=build /app/packages ./packages
COPY --from=build /app/node_modules ./node_modules

# Service router script
RUN echo '#!/bin/sh' > /app/start.sh && \
    echo 'case "$SERVICE_NAME" in' >> /app/start.sh && \
    echo '  "api") cd packages/api && node dist/index.js ;;' >> /app/start.sh && \
    echo '  "worker") cd packages/worker && node dist/index.js ;;' >> /app/start.sh && \
    echo '  *) echo "Unknown service: $SERVICE_NAME" && exit 1 ;;' >> /app/start.sh && \
    echo 'esac' >> /app/start.sh && \
    chmod +x /app/start.sh

EXPOSE ${PORT:-8080}
CMD ["/app/start.sh"]
```

### Docker Compose for Development

```yaml
# docker-compose.yml
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: myapp
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data

  # Redis for caching/queues
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes

  # API Service
  api:
    build:
      context: .
      target: runtime
    environment:
      SERVICE_NAME: api
      DATABASE_URL: ********************************************/myapp
      REDIS_URL: redis://redis:6379
      PORT: 8080
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./packages/api:/app/packages/api
    command: sh -c "cd packages/api && pnpm dev"

  # Worker Service
  worker:
    build:
      context: .
      target: runtime
    environment:
      SERVICE_NAME: worker
      DATABASE_URL: ********************************************/myapp
      REDIS_URL: redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./packages/worker:/app/packages/worker
    command: sh -c "cd packages/worker && pnpm dev"

  # Frontend UI
  ui:
    build:
      context: ./packages/ui
      dockerfile: Dockerfile.dev
    ports:
      - "5173:5173"
    environment:
      VITE_API_URL: http://localhost:8080
    volumes:
      - ./packages/ui:/app
      - /app/node_modules

volumes:
  postgres-data:
```

### Railway Deployment Script

```bash
#!/bin/bash
# deploy-railway.sh

set -e

echo "🚀 Deploying to Railway..."

# Check prerequisites
command -v railway >/dev/null 2>&1 || { echo "Railway CLI required"; exit 1; }

# Services to deploy
SERVICES=("api" "worker" "ui")

# Deploy each service
for SERVICE in "${SERVICES[@]}"; do
  echo "📦 Deploying $SERVICE..."
  
  cd "packages/$SERVICE"
  
  # Link to Railway project
  railway link
  
  # Set environment variables
  railway variables set NODE_ENV=production
  railway variables set SERVICE_NAME=$SERVICE
  
  # Deploy
  railway up --detach
  
  cd ../..
  
  echo "✅ $SERVICE deployed"
  sleep 5
done

echo "🎉 All services deployed successfully!"

# Show service URLs
railway status
```

### Kubernetes Manifests

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
  labels:
    app: api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api
  template:
    metadata:
      labels:
        app: api
    spec:
      containers:
      - name: api
        image: your-registry/api:latest
        ports:
        - containerPort: 8080
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: api
spec:
  selector:
    app: api
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: LoadBalancer
```

---

## Development Workflow

### Git Hooks Setup

```bash
#!/bin/bash
# .husky/pre-commit

# Run type checking
pnpm type-check

# Run linting
pnpm lint

# Run tests for changed packages
pnpm test --changed

# Check for secrets
if git diff --cached --name-only | xargs grep -E "(api_key|secret|password|token)" ; then
  echo "⚠️  Possible secrets detected in commit"
  exit 1
fi
```

### Development Environment Setup

```typescript
// packages/api/src/lib/env.ts
import { z } from 'zod';

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'test', 'production']).default('development'),
  PORT: z.coerce.number().default(8080),
  DATABASE_URL: z.string().url(),
  REDIS_URL: z.string().url().optional(),
  JWT_SECRET: z.string().min(32),
  ENCRYPTION_KEY: z.string().min(32),
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
  CORS_ORIGIN: z.string().default('http://localhost:5173'),
});

export type Env = z.infer<typeof envSchema>;

export function validateEnv(): Env {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    console.error('❌ Invalid environment variables:', error);
    process.exit(1);
  }
}

export const env = validateEnv();
```

### Logging Configuration

```typescript
// packages/api/src/lib/logger.ts
import winston from 'winston';
import { env } from './env';

const format = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

export const logger = winston.createLogger({
  level: env.LOG_LEVEL,
  format,
  defaultMeta: { service: 'api' },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
      silent: env.NODE_ENV === 'test'
    })
  ]
});

// Production transports
if (env.NODE_ENV === 'production') {
  logger.add(new winston.transports.File({
    filename: 'error.log',
    level: 'error'
  }));
  
  logger.add(new winston.transports.File({
    filename: 'combined.log'
  }));
}

// Request logging middleware
export const requestLogger = (req: any, res: any, next: any) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('Request processed', {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration,
      ip: req.ip,
      userAgent: req.get('user-agent')
    });
  });
  
  next();
};
```

---

## Testing Strategy

### Integration Test Setup

```typescript
// __tests__/utils/test-helpers.ts
import { createClient } from '@supabase/supabase-js';
import { faker } from '@faker-js/faker';

export class TestHelper {
  private supabase: any;
  private testOrganizationId: string;
  
  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  async setup() {
    // Create test organization
    const { data: org } = await this.supabase
      .from('organizations')
      .insert({
        name: faker.company.name(),
        slug: faker.helpers.slugify(faker.company.name()).toLowerCase()
      })
      .select()
      .single();
      
    this.testOrganizationId = org.id;
    
    // Create test user
    const { data: { user } } = await this.supabase.auth.admin.createUser({
      email: faker.internet.email(),
      password: 'test-password-123',
      email_confirm: true
    });
    
    // Link user to organization
    await this.supabase
      .from('profiles')
      .insert({
        id: user.id,
        organization_id: this.testOrganizationId,
        email: user.email,
        role: 'admin'
      });
      
    return { organization: org, user };
  }

  async cleanup() {
    // Clean up test data
    await this.supabase
      .from('organizations')
      .delete()
      .eq('id', this.testOrganizationId);
  }

  async createAuthenticatedClient(userId: string) {
    const { data: { session } } = await this.supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: '<EMAIL>',
      options: { data: { user_id: userId } }
    });
    
    return createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
          detectSessionInUrl: false
        },
        global: {
          headers: {
            Authorization: `Bearer ${session.access_token}`
          }
        }
      }
    );
  }
}
```

### E2E Test Example

```typescript
// __tests__/e2e/user-flow.test.ts
import { test, expect } from '@playwright/test';

test.describe('User Flow', () => {
  test('complete user journey', async ({ page }) => {
    // Navigate to app
    await page.goto('http://localhost:5173');
    
    // Sign up
    await page.click('text=Sign Up');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'Test123!@#');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('**/dashboard');
    
    // Create a new item
    await page.click('text=Create New');
    await page.fill('input[name="title"]', 'Test Item');
    await page.fill('textarea[name="description"]', 'Test Description');
    await page.click('text=Save');
    
    // Verify creation
    await expect(page.locator('text=Test Item')).toBeVisible();
    
    // Test real-time update
    const itemLocator = page.locator('[data-testid="item-list"]');
    await expect(itemLocator).toContainText('Test Item');
  });
});
```

---

## Production Checklist

### Pre-deployment Checklist

```markdown
## Security
- [ ] All environment variables are properly set
- [ ] Encryption keys are unique and secure (32+ chars)
- [ ] Database has proper RLS policies
- [ ] API endpoints have authentication
- [ ] Input validation on all endpoints
- [ ] Rate limiting is configured
- [ ] CORS is properly restricted
- [ ] Security headers are set (CSP, HSTS, etc.)

## Performance
- [ ] Database indexes are created
- [ ] Connection pooling is configured
- [ ] Redis caching is implemented
- [ ] Static assets are CDN-hosted
- [ ] Images are optimized
- [ ] Code is minified and bundled
- [ ] Gzip/Brotli compression enabled

## Monitoring
- [ ] Error tracking is set up (Sentry)
- [ ] Application monitoring (APM)
- [ ] Log aggregation configured
- [ ] Alerts are configured
- [ ] Health check endpoints exist
- [ ] Metrics are being collected

## Database
- [ ] Backups are automated
- [ ] Point-in-time recovery enabled
- [ ] Read replicas configured (if needed)
- [ ] Connection limits set appropriately
- [ ] Maintenance windows scheduled

## Infrastructure
- [ ] Auto-scaling configured
- [ ] Load balancer health checks
- [ ] SSL certificates installed
- [ ] DNS configured correctly
- [ ] CDN configured for assets
- [ ] DDoS protection enabled

## Testing
- [ ] All tests passing
- [ ] Load testing completed
- [ ] Security scan completed
- [ ] Accessibility audit passed
- [ ] Browser compatibility tested
- [ ] Mobile responsiveness verified

## Documentation
- [ ] API documentation complete
- [ ] Deployment runbook created
- [ ] Incident response plan
- [ ] Architecture diagrams updated
- [ ] Environment variables documented
- [ ] On-call rotation set up
```

### Monitoring Setup

```yaml
# docker/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'api'
    static_configs:
      - targets: ['api:8080']
    metrics_path: '/metrics'

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

rule_files:
  - '/etc/prometheus/alerts/*.yml'

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['alertmanager:9093']
```

### Performance Monitoring Dashboard

```typescript
// packages/api/src/lib/metrics.ts
import { Registry, Counter, Histogram, Gauge } from 'prom-client';

export const register = new Registry();

// Request metrics
export const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status'],
  buckets: [0.1, 0.5, 1, 2, 5],
  registers: [register]
});

export const httpRequestTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status'],
  registers: [register]
});

// Business metrics
export const activeUsers = new Gauge({
  name: 'active_users',
  help: 'Number of active users',
  labelNames: ['organization'],
  registers: [register]
});

// Middleware to track metrics
export const metricsMiddleware = (req: any, res: any, next: any) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route?.path || req.path;
    
    httpRequestDuration
      .labels(req.method, route, res.statusCode.toString())
      .observe(duration);
      
    httpRequestTotal
      .labels(req.method, route, res.statusCode.toString())
      .inc();
  });
  
  next();
};
```

---

## Additional Resources

### Useful Scripts

```bash
#!/bin/bash
# scripts/health-check.sh

# Check all services
SERVICES=("api:8080" "worker:8081" "ui:5173")

for SERVICE in "${SERVICES[@]}"; do
  SERVICE_NAME=$(echo $SERVICE | cut -d: -f1)
  PORT=$(echo $SERVICE | cut -d: -f2)
  
  if curl -f -s "http://localhost:$PORT/health" > /dev/null; then
    echo "✅ $SERVICE_NAME is healthy"
  else
    echo "❌ $SERVICE_NAME is unhealthy"
  fi
done

# Check database
if pg_isready -h localhost -p 5432; then
  echo "✅ Database is ready"
else
  echo "❌ Database is not ready"
fi

# Check Redis
if redis-cli ping > /dev/null 2>&1; then
  echo "✅ Redis is ready"
else
  echo "❌ Redis is not ready"
fi
```

### Migration Runner

```typescript
// scripts/run-migrations.ts
import { readdir } from 'fs/promises';
import { join } from 'path';
import { createClient } from '@supabase/supabase-js';

async function runMigrations() {
  const supabase = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );

  const migrationsDir = join(__dirname, '../supabase/migrations');
  const files = await readdir(migrationsDir);
  const sqlFiles = files.filter(f => f.endsWith('.sql')).sort();

  console.log(`Found ${sqlFiles.length} migration files`);

  for (const file of sqlFiles) {
    console.log(`Running migration: ${file}`);
    
    const sql = await Bun.file(join(migrationsDir, file)).text();
    
    try {
      const { error } = await supabase.rpc('exec_sql', { sql });
      if (error) throw error;
      console.log(`✅ ${file} completed`);
    } catch (error) {
      console.error(`❌ ${file} failed:`, error);
      process.exit(1);
    }
  }

  console.log('All migrations completed successfully');
}

runMigrations().catch(console.error);
```

---

## Conclusion

This guide extracts the most valuable patterns and configurations from VibeLaunch to help you build production-ready multi-tenant SaaS applications. The patterns here are battle-tested and can be adapted to various use cases beyond marketplaces.

Key takeaways:
- Start with a solid monorepo structure
- Implement multi-tenancy from day one
- Use event-driven architecture for scalability
- Secure your application at every layer
- Plan for monitoring and observability
- Automate your deployment pipeline
- Write comprehensive tests

Remember to adapt these patterns to your specific needs and always consider the trade-offs for your particular use case.