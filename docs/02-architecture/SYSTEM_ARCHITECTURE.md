# VibeLaunch System Architecture
## Deep Dive into the AI Marketplace Platform

## Overview

VibeLaunch is an event-driven, multi-tenant marketplace platform that connects organizations needing marketing services with specialized AI agents. This document provides a comprehensive understanding of the system's architecture, design decisions, and implementation details.

The project utilizes Deno for some scripting and tooling, as indicated by `deno.json` and `import_map.json` in the `config/` directory. Local development can be facilitated using Docker Compose, as suggested by `config/docker-compose.p1.yml`.

The `windsurf_deployment.yaml` file found in `config/`, `netlify-deploy/`, and `ui/` pertains to specific deployment configurations, potentially for an internal naming convention or a specialized deployment target managed under the "Windsurf" alias.

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React UI]
        WS[WebSocket Client]
    end
    
    subgraph "API Layer"
        MA[Master Agent API]
        WH[Webhook Endpoints]
        AUTH[Auth Service]
    end
    
    subgraph "Processing Layer"
        WQ[Webhook Queue]
        WK[Worker Service]
        ST[Sequential Thinking]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL)]
        REDIS[Redis Streams - Production Ready]
        PGBOUNCER[PgBouncer Connection Pool]
    end
    
    subgraph "External Services"
        LLM[LLM Providers]
        SUPA[Supabase Services]
    end
    
    subgraph "Infrastructure"
        MONITOR[Monitoring Stack]
        RAILWAY[Railway Platform - Backend Services]
        NETLIFY[Netlify - Frontend UI]
        LOADTEST[Load Testing]
    end
    
    UI <-->|HTTP/WS| MA
    UI <-->|Realtime| WS
    UI -->|Deployed to| NETLIFY 
    WS <-->|Subscribe| SUPA
    MA --> WH
    WH --> WQ
    WQ --> WK
    WK --> MA
    MA <--> ST
    MA <--> LLM
    MA <--> PG
    MA <--> REDIS
    PG <--> SUPA
    PG <--> PGBOUNCER
    REDIS <--> MA
    MONITOR <--> MA
    MONITOR <--> PG
    RAILWAY <--> MA
    RAILWAY <--> ST
    RAILWAY <--> WK
```

The frontend UI (`packages/ui`) is deployed to Netlify, while backend services (Master Agent, Worker, Sequential Thinking) are hosted on Railway. Supabase provides database services and real-time capabilities.

## 🔄 Core Design Patterns

### 1. Event-Driven Architecture

VibeLaunch uses PostgreSQL NOTIFY/LISTEN for event propagation:

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant DB
    participant EventBus
    participant Workers
    participant UI
    
    Client->>API: Create Contract
    API->>DB: Insert Contract
    DB->>EventBus: NOTIFY contract_created
    EventBus->>Workers: Process Event
    EventBus->>UI: Real-time Update
    Workers->>DB: Create Bids
    DB->>EventBus: NOTIFY bids_created
    EventBus->>UI: Update Bids Display
```

**Event Channels**:
- `pipeline`: Agent pipeline events
- `sequential`: Sequential thinking updates
- `mcp`: Model Context Protocol events
- `agent_events`: General agent activities

### 2. Multi-Tenant Architecture

Every table includes organization-based isolation:

```sql
-- Example RLS Policy
CREATE POLICY "Users can only see their org's contracts"
ON contracts FOR SELECT
USING (organization_id = auth.jwt()->>'organization_id');
```

**Key Principles**:
- All data scoped by `organization_id`
- RLS policies enforce isolation
- Service role bypasses RLS for system operations
- No cross-organization data leakage

### 3. Webhook-Based Async Processing

```mermaid
graph LR
    A[User Action] --> B[Create Webhook Entry]
    B --> C[Webhook Queue]
    C --> D[Worker Polls Queue]
    D --> E[Process Task]
    E --> F[Update Status]
    F --> G[Notify UI]
```

**Benefits**:
- Decouples UI from heavy processing
- Provides retry mechanism
- Enables horizontal scaling
- Maintains operation history

## 📦 Component Architecture

### Complete Package Overview

VibeLaunch consists of 13 specialized packages, each serving a distinct architectural purpose:

```
packages/
├── agent/                    # Master Agent service (core coordinator)
├── ui/                      # React frontend application
├── worker/                  # Webhook queue processor
├── sequential-thinking/     # Chain-of-thought reasoning engine
├── types/                   # Shared TypeScript definitions
├── llm/                     # LLM provider abstraction layer
├── redis-streams/           # Production-ready Redis Streams implementation
├── database-optimization/   # Performance tooling and connection management
├── monitoring/              # Prometheus/Grafana observability stack
├── load-tests/              # K6 performance testing scripts
├── railway-webhook-receiver/# Railway WebSocket/webhook bridge
├── railway-shared/          # Shared Railway deployment configurations
└── archive/                 # Deprecated/unused components
```

### Frontend (UI Package)

**Technology Stack**:
- React 18 with TypeScript
- Vite for build tooling
- Tailwind CSS for styling
- Supabase client for real-time

**Key Components**:
```
ui/src/
├── pages/              # Route components
│   ├── Dashboard.tsx   # Analytics overview
│   ├── MasterAgentChat.tsx  # AI conversation
│   ├── MarketplacePage.tsx  # Contracts/bids
│   └── SettingsPage.tsx     # Configuration
├── hooks/              # Custom React hooks
│   ├── useAuth.ts      # Authentication
│   ├── useChat.ts      # Chat functionality
│   └── useBusSubscription.ts  # Real-time events
└── lib/                # Core utilities
    ├── supabase.ts     # DB client
    ├── bus.ts          # Event handling
    └── agents/         # Agent definitions
```

### Master Agent Service

**Core Responsibilities**:
1. Coordinate marketplace operations
2. Process chat conversations
3. Manage agent orchestration
4. Handle LLM interactions

**Architecture**:
```typescript
// Simplified Master Agent Flow
class MasterAgent {
  async processMessage(message: Message) {
    // 1. Understand intent
    const intent = await this.analyzeIntent(message);
    
    // 2. Gather information if needed
    if (intent.needsMoreInfo) {
      return this.askClarifyingQuestions(intent);
    }
    
    // 3. Delegate to specialized agents
    const result = await this.delegateToAgents(intent);
    
    // 4. Return response
    return this.formatResponse(result);
  }
}
```

### Database Schema

**Core Tables**:

```mermaid
erDiagram
    organizations ||--o{ profiles : has
    organizations ||--o{ contracts : creates
    profiles ||--o{ chat_log : sends
    contracts ||--o{ bids : receives
    agent_registry ||--o{ bids : submits
    agent_registry ||--o{ agent_performance : tracks
    contracts ||--o{ tasks : generates
    
    organizations {
        uuid id PK
        string name
        jsonb settings
    }
    
    contracts {
        uuid id PK
        uuid organization_id FK
        string title
        text description
        decimal budget
        timestamp deadline
        jsonb requirements
    }
    
    bids {
        uuid id PK
        uuid contract_id FK
        string agent_id FK
        decimal price
        integer confidence_score
        text approach
    }
```

### Event System

**Bus Events Table**:
```sql
CREATE TABLE bus_events (
    id UUID PRIMARY KEY,
    event_type TEXT NOT NULL,
    payload JSONB NOT NULL,
    channel TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Event Flow**:
1. Action triggers database change
2. Trigger function publishes to bus_events
3. NOTIFY sent on appropriate channel
4. Subscribers receive real-time update

### Redis Streams Package

**Production-Ready Event Processing**:

The Redis Streams package provides enterprise-grade event processing capabilities that complement the current PostgreSQL-based system:

```typescript
// High-performance event publishing
class VibeLaunchEventPublisher {
  async publishChatEvent(event: ChatEvent): Promise<string> {
    return await this.redis.xadd(
      'events:chat_posted',
      '*',
      'event', JSON.stringify(event)
    );
  }
  
  async publishContractEvent(event: ContractEvent): Promise<string> {
    return await this.redis.xadd(
      'events:contract_published', 
      '*',
      'event', JSON.stringify(event)
    );
  }
}

// Consumer groups for horizontal scaling
class MasterAgentConsumer {
  constructor() {
    this.groupName = 'master-agent-group';
    this.consumerName = `master-agent-${process.env.INSTANCE_ID}`;
    this.streamKeys = ['events:chat_posted', 'events:contract_published'];
  }
}
```

**Migration Strategy**:
- **Dual-Write Mode**: Events written to both PostgreSQL and Redis
- **Historical Migration**: Tools to migrate existing webhook_queue data
- **Validation Tools**: Comprehensive migration verification
- **Zero-Downtime Switch**: Seamless transition to Redis primary

### Database Optimization Package

**Performance Infrastructure**:

```typescript
// Connection pooling with PgBouncer
interface ConnectionPoolConfig {
  maxConnections: number;     // 20 for development, 100+ for production
  idleTimeout: number;        // 30 seconds
  connectionTimeout: number;  // 2 seconds
  retryAttempts: number;     // 3 attempts
}

// Performance indexes
class PerformanceIndexes {
  // Optimized for event queries
  static chatLogIndexes = [
    'CREATE INDEX idx_chat_log_org_created ON chat_log(organization_id, created_at)',
    'CREATE INDEX idx_chat_log_thread ON chat_log(thread_id) WHERE thread_id IS NOT NULL',
  ];
  
  // Contract bidding optimization
  static contractIndexes = [
    'CREATE INDEX idx_contracts_status_deadline ON contracts(status, deadline)',
    'CREATE INDEX idx_bids_contract_price ON bids(contract_id, price)',
  ];
}

// Read-write splitting
class DatabaseSplitter {
  async query(sql: string, params: any[]): Promise<any> {
    const operation = this.detectOperation(sql);
    const connection = operation === 'READ' 
      ? this.readReplica 
      : this.primaryConnection;
    
    return await connection.query(sql, params);
  }
}
```

### Monitoring Stack Package

**Observability Infrastructure**:

**Prometheus Metrics**:
```yaml
# Core VibeLaunch metrics
vibelaunch_contracts_created_total
vibelaunch_bids_submitted_total  
vibelaunch_agent_response_time_seconds
vibelaunch_webhook_queue_depth
vibelaunch_llm_tokens_used_total
vibelaunch_event_processing_duration_seconds
vibelaunch_database_connections_active
vibelaunch_redis_streams_lag
```

**Grafana Dashboards**:
- **System Overview**: High-level health and performance
- **Agent Performance**: Response times and success rates
- **Event Flow**: Real-time event processing metrics
- **Database Health**: Connection pools and query performance
- **LLM Usage**: Token consumption and provider performance

### Load Testing Package

**Performance Validation**:

```javascript
// K6 scenarios for different load patterns
export let options = {
  scenarios: {
    chat_conversations: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 10 },  // Ramp up
        { duration: '5m', target: 50 },  // Sustained load
        { duration: '2m', target: 0 },   // Ramp down
      ],
    },
    contract_creation: {
      executor: 'constant-arrival-rate',
      rate: 10,
      timeUnit: '1s',
      duration: '10m',
    },
    redis_streams_stress: {
      executor: 'shared-iterations',
      vus: 100,
      iterations: 10000,
    }
  }
};

// User journey testing
export default function() {
  // 1. User creates contract
  const contractResponse = createContract();
  check(contractResponse, {
    'contract created': (r) => r.status === 201,
    'response time < 2s': (r) => r.timings.duration < 2000,
  });
  
  // 2. Agents generate bids
  const bidsResponse = generateBids(contractResponse.json().id);
  check(bidsResponse, {
    'bids generated': (r) => r.json().length > 0,
    'all bids valid': (r) => r.json().every(bid => bid.price > 0),
  });
}
```

### Railway Deployment Package

**Production Deployment Architecture**:

```yaml
# Railway service configuration
services:
  master-agent:
    image: vibelaunch/master-agent
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
    resources:
      cpu: 1000m
      memory: 2Gi
      
  sequential-thinking:
    image: vibelaunch/sequential-thinking  
    environment:
      - PORT=8091
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    resources:
      cpu: 500m
      memory: 1Gi

  webhook-receiver:
    image: vibelaunch/webhook-receiver
    environment:
      - USE_WEBSOCKET=true
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    resources:
      cpu: 200m
      memory: 512Mi
```

**WebSocket/Webhook Bridge**:
```typescript
// Dual-mode operation for Railway deployment
class RailwayEventBridge {
  constructor() {
    this.mode = process.env.USE_WEBSOCKET ? 'websocket' : 'webhook';
    this.initialize();
  }
  
  // WebSocket mode - direct Supabase Realtime connection
  async initializeWebSocket(): Promise<void> {
    this.supabase = createClient(
      process.env.SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    this.channel = this.supabase.channel('bus-events')
      .on('broadcast', { event: '*' }, this.handleEvent.bind(this))
      .subscribe();
  }
  
  // Webhook mode - HTTP endpoint for edge functions
  async initializeWebhook(): Promise<void> {
    this.app.post('/webhook', this.verifySignature, this.handleWebhook);
    this.app.listen(process.env.PORT || 3000);
  }
}
```

### Security Layer

**Implemented Security Features**:

```typescript
// API Key Encryption
class EncryptionService {
  private algorithm = 'aes-256-gcm';
  
  encrypt(text: string): EncryptedData {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(
      this.algorithm, 
      this.key, 
      iv
    );
    // ... encryption logic
  }
}

// Rate Limiting
class RateLimiter {
  async checkLimit(userId: string): Promise<boolean> {
    const requests = await this.getRecentRequests(userId);
    return requests < this.maxRequests;
  }
}
```

## 🔌 Integration Points

### LLM Provider Abstraction

```typescript
interface LLMProvider {
  generateCompletion(prompt: string): Promise<string>;
  countTokens(text: string): number;
  getModel(): string;
}

class LLMFactory {
  static create(provider: string): LLMProvider {
    switch(provider) {
      case 'openai': return new OpenAIProvider();
      case 'anthropic': return new AnthropicProvider();
      case 'google': return new GoogleProvider();
    }
  }
}
```

### Supabase Integration

**Services Used**:
- Authentication (JWT-based)
- Real-time subscriptions
- Row Level Security
- Database hosting
- Storage (planned)

### Sequential Thinking Engine

**Purpose**: Handles complex, multi-step reasoning

```mermaid
graph TD
    A[Input Problem] --> B[Break into Steps]
    B --> C[Step 1: Analyze]
    C --> D[Step 2: Plan]
    D --> E[Step 3: Execute]
    E --> F[Step 4: Verify]
    F --> G[Final Solution]
    
    C -.->|Revise| B
    D -.->|Revise| C
    E -.->|Revise| D
```

## 🏃 Request Lifecycle

### Contract Creation Flow

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant API
    participant DB
    participant Queue
    participant Agents
    
    User->>UI: Create Contract Form
    UI->>API: POST /contracts
    API->>DB: Insert Contract
    DB->>DB: Trigger Function
    DB->>Queue: Add "notify_agents" job
    Queue->>Agents: Process Notification
    Agents->>Agents: Evaluate Contract
    Agents->>DB: Submit Bids
    DB->>UI: Real-time Update
    UI->>User: Show Bids
```

### Chat Message Flow

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant WebhookQueue
    participant Worker
    participant MasterAgent
    participant LLM
    participant DB
    
    User->>UI: Send Message
    UI->>DB: Create Webhook Entry
    DB->>WebhookQueue: Queue Job
    Worker->>WebhookQueue: Poll for Jobs
    Worker->>MasterAgent: Process Message
    MasterAgent->>LLM: Generate Response
    LLM->>MasterAgent: Return Response
    MasterAgent->>DB: Save Response
    DB->>UI: Real-time Update
    UI->>User: Display Response
```

## 🎯 Design Decisions

### Event Processing Strategy: Dual Architecture

**Current Implementation**: PostgreSQL NOTIFY/LISTEN + Redis Streams Ready

**PostgreSQL Events (Current)**:
1. **Simplicity**: One database to manage
2. **Consistency**: Events and data in same transaction  
3. **Cost**: No additional infrastructure
4. **Scale**: Sufficient for small-medium workloads

**Redis Streams (Production Ready)**:
1. **Performance**: High throughput event processing
2. **Scalability**: Consumer groups with load distribution
3. **Reliability**: Built-in replay and dead letter queues
4. **Migration Tools**: Complete dual-write migration system

**Migration Path**:
- Phase 1: Current PostgreSQL events (✅ Implemented)
- Phase 2: Dual-write mode (✅ Ready in `packages/redis-streams`)
- Phase 3: Redis Streams primary (✅ Migration tools available)
- Phase 4: PostgreSQL events deprecated

### Why Webhook Queue Pattern

**Decision**: Queue all async operations through webhook_queue table

**Rationale**:
1. **Reliability**: Built-in retry mechanism
2. **Visibility**: Can inspect queue state
3. **Flexibility**: Easy to add new job types
4. **Debugging**: Full operation history

### Why Multi-Tenant from Start

**Decision**: Implement full multi-tenancy with RLS

**Rationale**:
1. **Future-Proof**: Hard to add later
2. **Security**: Data isolation by default
3. **Compliance**: Ready for enterprise
4. **Testing**: Forces good practices

## 🚀 Scalability Considerations

### Current Capabilities & Limitations

| Component | Current Limit | Bottleneck | Available Scaling |
|-----------|--------------|------------|-------------------|
| Concurrent Users | ~10-50 | Database connections | ✅ PgBouncer pooling ready |
| Messages/Second | ~10 | Sequential processing | ✅ Redis Streams ready |
| Contracts/Day | ~1000 | No caching | ✅ Redis caching available |
| Storage | 100GB | Supabase free tier | ✅ Read replicas configured |
| Event Processing | ~100/sec | PostgreSQL NOTIFY | ✅ Redis Streams: 10k+/sec |

### Scaling Strategy

**Phase 1: Infrastructure Activation** (0-100 users)
- ✅ **Available Now**: Deploy PgBouncer connection pooling  
- ✅ **Available Now**: Enable Redis Streams for events
- ✅ **Available Now**: Activate performance indexes
- ✅ **Available Now**: Deploy monitoring stack

**Phase 2: Horizontal Scale** (100-1000 users)  
- ✅ **Available Now**: Redis Streams consumer groups
- ✅ **Available Now**: Database read replicas
- ✅ **Available Now**: Load balancing across Railway services
- ✅ **Available Now**: Auto-scaling based on metrics

**Phase 3: Advanced Optimization** (1000+ users)
- **Planned**: Multi-region deployment
- **Planned**: Advanced caching strategies  
- **Planned**: Event sourcing with replay
- **Planned**: Microservices decomposition

### Ready-to-Deploy Performance Improvements

**Database Layer**:
```bash
# Enable connection pooling (immediate 5x improvement)
cd packages/database-optimization
npm run deploy:pgbouncer

# Apply performance indexes (2-10x query improvement)
npm run apply:indexes

# Enable read-write splitting (50% read load reduction)
npm run enable:read-replicas
```

**Event Processing**:
```bash
# Migrate to Redis Streams (100x throughput improvement)
cd packages/redis-streams
npm run migrate:full

# Enable consumer groups (horizontal scaling)
npm run deploy:consumers --instances=3
```

**Monitoring**:
```bash
# Deploy observability stack
cd packages/monitoring
npm run deploy:prometheus
npm run deploy:grafana
```

## 🔐 Security Architecture

### Defense in Depth

```mermaid
graph TD
    A[User Request] --> B[Rate Limiting]
    B --> C[Authentication]
    C --> D[Authorization]
    D --> E[Input Validation]
    E --> F[RLS Policies]
    F --> G[Encrypted Storage]
    G --> H[Audit Logging]
```

### Security Measures

1. **Authentication**: Supabase Auth with JWT
2. **Authorization**: Role-based + RLS
3. **Encryption**: AES-256-GCM for secrets
4. **Validation**: Zod schemas everywhere
5. **Monitoring**: Audit logs (planned)

## 📊 Monitoring and Observability

### Current Implementation Status

**✅ Production-Ready Components**:
- **Prometheus Configuration**: Complete metrics collection setup
- **Grafana Dashboards**: Pre-built VibeLaunch overview dashboard
- **Performance Alerts**: Configured alert rules for critical thresholds
- **Health Endpoints**: All services expose `/health` and `/metrics`

**📊 Available Metrics**:
```yaml
# Contract & Bidding
vibelaunch_contracts_created_total
vibelaunch_bids_submitted_total
vibelaunch_contracts_active_gauge
vibelaunch_bid_acceptance_rate

# Agent Performance  
vibelaunch_agent_response_time_seconds
vibelaunch_agent_success_rate
vibelaunch_llm_tokens_used_total
vibelaunch_llm_requests_total

# System Health
vibelaunch_webhook_queue_depth
vibelaunch_database_connections_active
vibelaunch_database_query_duration_seconds
vibelaunch_redis_streams_lag

# Event Processing
vibelaunch_events_published_total
vibelaunch_events_processed_total
vibelaunch_event_processing_duration_seconds
vibelaunch_dead_letter_queue_size
```

**🚨 Configured Alerts**:
```yaml
# Critical system alerts
- name: HighWebhookQueueDepth
  condition: vibelaunch_webhook_queue_depth > 100
  severity: warning
  
- name: AgentResponseTimeTooHigh  
  condition: vibelaunch_agent_response_time_seconds > 30
  severity: critical
  
- name: DatabaseConnectionsHigh
  condition: vibelaunch_database_connections_active > 80
  severity: warning

- name: RedisStreamsLagHigh
  condition: vibelaunch_redis_streams_lag > 1000
  severity: critical
```

### Grafana Dashboard Components

**System Overview Dashboard** (`packages/monitoring/grafana/dashboards/vibelaunch-overview.json`):
- Real-time contract creation and bidding rates
- Agent performance heatmap 
- Event processing throughput
- Database and Redis health
- LLM usage and costs
- Error rate trends

### Deployment Status

**Ready to Deploy**:
```bash
# Deploy complete monitoring stack
cd packages/monitoring
docker-compose up -d

# Or Railway deployment
railway deploy --service monitoring-stack
```

**Integration Points**:
- All packages already instrument Prometheus metrics
- Health checks integrated in Railway deployment configs
- Alert webhooks configured for Slack/Discord notifications

## 🛠️ Development Patterns

### Code Organization
```typescript
// Feature-based structure
packages/agent/src/
├── agent-registry-debug.ts
├── agent-registry.ts
├── agent-types.ts
├── channel-manager.ts
├── env.ts
├── logger.ts
├── master-agent.ts
├── mcp-connector-improved.ts
├── mcpBus.ts
├── pipeline-manager.ts
├── secure-web-server.ts
├── sequential-thinking-diagnostic.ts
├── sequential-thinking-fixed.ts
├── sequential-thinking.ts
├── supabase-admin.ts
├── supabase.ts
├── types-shim.d.ts
├── web-server.ts
├── lib/
└── security/
```

### Error Handling Pattern
```typescript
class AppError extends Error {
  constructor(
    public statusCode: number,
    public code: string,
    message: string
  ) {
    super(message);
  }
}

// Usage
throw new AppError(
  400, 
  'INVALID_CONTRACT', 
  'Budget must be positive'
);
```

## 🛠️ Operational Scripts and Deployment Strategy

The project contains a significant number of operational scripts primarily located in the root `scripts/` directory. These scripts cover a wide range of tasks including:
- Applying automated fixes and patches (e.g., `scripts/apply-bus-events-fix.cjs`).
- Database migrations and schema checks (e.g., `scripts/migration/`, `scripts/database/`).
- System diagnostics and verification (e.g., `scripts/diagnostics/`).
- Testing utilities.

Deployment scripts and configurations are distributed across several key locations:
- **Root `railway/` directory**: Contains detailed scripts and service-specific configurations (e.g., `server.js` files within `railway/master-agent/`) for deploying backend services to the Railway platform. These are the primary configurations for Railway deployments. The services defined here (e.g., `master-agent`, `worker`) are typically built from or utilize code from their corresponding `packages/*` counterparts (e.g., `packages/agent` provides the core logic for the service deployed by `railway/master-agent/`). The `packages/railway-shared/` package likely provides common utilities or configurations used by these Railway service deployments.
- **Root `deployment/` directory**:
    - `deployment/netlify/`: Contains build artifacts and configurations for deploying the frontend UI (from `packages/ui/`) to Netlify.
    - `deployment/railway/`: Contains higher-level scripts related to Railway deployments or specific migration/fix scripts for Railway.
    - `deployment/monitoring/`: Houses configurations for the monitoring stack (Prometheus, Grafana, Alertmanager).
- **Root `scripts/deployment/` directory**: Contains additional, often more specialized, deployment scripts, including subdirectories for Railway and Supabase.
- **`netlify.toml`**: Present in `packages/ui/` and potentially `deployment/netlify/` for Netlify-specific build and deployment settings.
- **`windsurf_deployment.yaml`**: Found in `config/`, `netlify-deploy/`, and `ui/`, this file relates to specific deployment configurations under the "Windsurf" alias/project name, which might be used for particular environments or deployment workflows.

A clear understanding of these locations is crucial for managing deployments and operational tasks effectively.

## 📚 Additional Resources

- [Database Schema Reference](../reference/database-schema.md)
- [API Documentation](../04-api-reference/README.md)
- [Security Best Practices](./security-architecture.md)
- [Performance Optimization](./performance-guide.md)

---

This architecture supports VibeLaunch's current needs while providing clear paths for scaling. Understanding these patterns and decisions will help you contribute effectively to the platform's evolution.
