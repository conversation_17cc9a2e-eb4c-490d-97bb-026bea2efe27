# Environment Configuration Architecture

## Overview

VibeLaunch's environment system provides secure, scalable configuration management across all packages and deployment environments. This document details the actual implementation of environment variable handling, secret management, and configuration validation throughout the system.

## Configuration Philosophy

### Design Principles

1. **Security First**: Sensitive data encrypted, never logged or committed
2. **Environment Isolation**: Clear separation between dev/staging/production
3. **Validation at Startup**: Fail fast with clear error messages
4. **Hierarchical Overrides**: Local > environment > defaults
5. **Documentation by Example**: `.env.example` files for all packages

### Configuration Sources (Priority Order)

```
1. Environment Variables (highest priority)
2. .env files in package directories
3. .env.example files (documentation/defaults)
4. Code-defined defaults (lowest priority)
```

## Package-Specific Configuration

### 1. Master Agent (`packages/agent/.env.example`)

```bash
# =================================================================
# VibeLaunch Master Agent Configuration
# =================================================================

# -------------------- Core Services --------------------
# Supabase Database Configuration (Required)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...your-service-role-key
SUPABASE_ANON_KEY=eyJ...your-anon-key

# Master Agent Server Configuration
PORT=8090
NODE_ENV=development
LOG_LEVEL=debug

# -------------------- Security --------------------
# Encryption key for LLM API keys (32 characters minimum)
ENCRYPTION_KEY=your-32-character-encryption-key-here!!

# Webhook security for external integrations
WEBHOOK_SECRET=your-webhook-secret-16-chars
JWT_SECRET=your-jwt-secret-for-internal-auth

# Rate limiting configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# -------------------- LLM Providers --------------------
# At least one LLM provider required for agent functionality

# OpenAI Configuration
OPENAI_API_KEY=sk-...
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000

# Anthropic Configuration  
ANTHROPIC_API_KEY=sk-ant-...
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=4000

# Google AI Configuration
GOOGLE_AI_API_KEY=AIza...
GOOGLE_AI_MODEL=gemini-pro
GOOGLE_AI_MAX_TOKENS=4000

# -------------------- Agent Configuration --------------------
# Master Agent behavior settings
DEFAULT_ORG_ID=00000000-0000-0000-0000-000000000000
MAX_CONVERSATION_HISTORY=50
AGENT_TIMEOUT_SECONDS=30
SEQUENTIAL_THINKING_ENABLED=true

# Agent registry settings
AGENT_REGISTRY_REFRESH_INTERVAL=300000
AGENT_HEARTBEAT_INTERVAL=60000

# -------------------- Performance --------------------
# Database connection pooling
DB_POOL_SIZE=20
DB_TIMEOUT=30000
DB_IDLE_TIMEOUT=300000

# Cache configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# -------------------- Monitoring --------------------
# Prometheus metrics
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_PATH=/metrics

# Health check configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health

# -------------------- Development --------------------
# Development-only settings
DEBUG_ENABLED=false
DEBUG_SQL=false
DEBUG_EVENTS=false
MOCK_LLM_RESPONSES=false

# -------------------- Production --------------------
# Production-specific overrides
# REDIS_URL=redis://redis:6379
# SENTRY_DSN=https://...
# NEW_RELIC_LICENSE_KEY=...
```

**Validation Implementation** (`packages/agent/src/env.ts`):
```typescript
import { z } from 'zod';

const envSchema = z.object({
  // Core services
  SUPABASE_URL: z.string().url('Must be a valid Supabase URL'),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1, 'Service role key required'),
  SUPABASE_ANON_KEY: z.string().min(1, 'Anon key required'),
  
  // Server configuration
  PORT: z.coerce.number().min(1000).max(65535).default(8090),
  NODE_ENV: z.enum(['development', 'staging', 'production']).default('development'),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  
  // Security
  ENCRYPTION_KEY: z.string().min(32, 'Encryption key must be at least 32 characters'),
  WEBHOOK_SECRET: z.string().min(16, 'Webhook secret must be at least 16 characters'),
  
  // LLM providers (at least one required)
  OPENAI_API_KEY: z.string().optional(),
  ANTHROPIC_API_KEY: z.string().optional(),
  GOOGLE_AI_API_KEY: z.string().optional(),
  
  // Agent configuration
  DEFAULT_ORG_ID: z.string().uuid('Must be a valid UUID'),
  MAX_CONVERSATION_HISTORY: z.coerce.number().min(1).max(1000).default(50),
  SEQUENTIAL_THINKING_ENABLED: z.coerce.boolean().default(true),
  
  // Performance
  DB_POOL_SIZE: z.coerce.number().min(1).max(100).default(20),
  CACHE_TTL: z.coerce.number().min(60).default(3600),
  
  // Monitoring
  METRICS_ENABLED: z.coerce.boolean().default(true),
  HEALTH_CHECK_ENABLED: z.coerce.boolean().default(true)
}).refine(
  (data) => !!(data.OPENAI_API_KEY || data.ANTHROPIC_API_KEY || data.GOOGLE_AI_API_KEY),
  {
    message: 'At least one LLM provider API key must be configured',
    path: ['LLM_PROVIDERS']
  }
);

export type EnvConfig = z.infer<typeof envSchema>;

export function validateEnvironment(): EnvConfig {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const formattedErrors = error.errors.map(err => 
        `${err.path.join('.')}: ${err.message}`
      ).join('\n');
      
      throw new Error(
        `Environment validation failed:\n${formattedErrors}\n\n` +
        `Please check your .env file and ensure all required variables are set.`
      );
    }
    throw error;
  }
}
```

### 2. Sequential Thinking (`packages/sequential-thinking/.env.example`)

```bash
# =================================================================
# VibeLaunch Sequential Thinking Service Configuration  
# =================================================================

# -------------------- Server Configuration --------------------
PORT=8091
NODE_ENV=development
LOG_LEVEL=debug

# -------------------- Supabase Configuration --------------------
# Database connection for thought storage and event publishing
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...your-service-role-key

# -------------------- LLM Configuration --------------------
# Optional fallback LLM for independent operation
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...

# -------------------- Sequential Thinking Settings --------------------
# Reasoning engine configuration
MAX_THOUGHTS_PER_SESSION=20
THOUGHT_TIMEOUT_SECONDS=30
ENABLE_THOUGHT_CACHING=true
ENABLE_THOUGHT_PERSISTENCE=true

# Confidence scoring
MIN_CONFIDENCE_THRESHOLD=0.6
ENABLE_CONFIDENCE_SCORING=true

# -------------------- Performance --------------------
# Processing limits
MAX_CONCURRENT_SESSIONS=10
SESSION_TTL_SECONDS=3600
MEMORY_LIMIT_MB=512

# -------------------- Monitoring --------------------
# Health and metrics
HEALTH_CHECK_PORT=8091
METRICS_ENABLED=true

# -------------------- Railway Environment --------------------
# Railway-specific configuration
RAILWAY_ENVIRONMENT=development
RAILWAY_SERVICE_NAME=sequential-thinking
```

### 3. Worker Service (`packages/worker/.env.example`)

```bash
# =================================================================
# VibeLaunch Webhook Queue Worker Configuration
# =================================================================

# -------------------- Database Configuration --------------------
# Supabase connection for webhook queue processing
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...your-service-role-key

# -------------------- Worker Configuration --------------------
# Queue processing settings
WORKER_CONCURRENCY=5
POLL_INTERVAL=1000
MAX_RETRIES=3
RETRY_BACKOFF_MS=5000

# Batch processing
BATCH_SIZE=10
BATCH_TIMEOUT_MS=30000

# -------------------- Error Handling --------------------
# Dead letter queue configuration
DLQ_ENABLED=true
DLQ_MAX_RETRIES=5
DLQ_RETENTION_HOURS=168

# Circuit breaker
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT_MS=60000

# -------------------- Logging --------------------
LOG_LEVEL=debug
NODE_ENV=development

# -------------------- Monitoring --------------------
# Worker health monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# -------------------- Railway Environment --------------------
RAILWAY_ENVIRONMENT=development
RAILWAY_SERVICE_NAME=worker
```

### 4. UI Frontend (`packages/ui/public/config.js.example`)

```javascript
// =================================================================
// VibeLaunch Frontend Configuration
// =================================================================

window.ENV = {
  // -------------------- Supabase Configuration --------------------
  // Frontend requires anon key, NOT service role key
  SUPABASE_URL: 'https://your-project.supabase.co',
  SUPABASE_ANON_KEY: 'eyJ...your-anon-key',
  
  // -------------------- Environment --------------------
  NODE_ENV: 'development',
  
  // -------------------- API Configuration --------------------
  // Master Agent API endpoint
  MASTER_AGENT_URL: 'http://localhost:8090',
  SEQUENTIAL_THINKING_URL: 'http://localhost:8091',
  
  // -------------------- Feature Flags --------------------
  // UI feature toggles
  ENABLE_DEBUG_PANEL: true,
  ENABLE_MASTER_AGENT_CHAT: true,
  ENABLE_CONTRACTS_PAGE: true,
  ENABLE_MARKETPLACE: true,
  ENABLE_BRAND_HUB: true,
  ENABLE_INTEGRATION_VAULT: true,
  
  // -------------------- Real-time Configuration --------------------
  // WebSocket/Realtime settings
  REALTIME_ENABLED: true,
  REALTIME_HEARTBEAT_INTERVAL: 30000,
  REALTIME_RECONNECT_INTERVAL: 5000,
  
  // -------------------- Chat Configuration --------------------
  // Chat interface settings
  MAX_MESSAGE_LENGTH: 4000,
  CHAT_HISTORY_LIMIT: 100,
  AUTO_SCROLL_ENABLED: true,
  TYPING_INDICATOR_ENABLED: true,
  
  // -------------------- Performance --------------------
  // Frontend optimization
  LAZY_LOADING_ENABLED: true,
  CHUNK_SIZE_KB: 250,
  CACHE_STRATEGY: 'stale-while-revalidate',
  
  // -------------------- Analytics --------------------
  // Usage tracking (optional)
  ANALYTICS_ENABLED: false,
  // GOOGLE_ANALYTICS_ID: 'GA_MEASUREMENT_ID',
  
  // -------------------- Security --------------------
  // Frontend security settings
  CSP_ENABLED: true,
  SECURE_COOKIES: false, // Set to true in production
  
  // -------------------- Development --------------------
  // Development-only features
  DEBUG_MODE: true,
  HOT_RELOAD_ENABLED: true,
  SOURCE_MAPS_ENABLED: true,
  
  // -------------------- Production Overrides --------------------
  // These should be overridden in production
  // CDN_URL: 'https://cdn.vibelaunch.com',
  // SENTRY_DSN: 'https://...ingest.sentry.io/...',
  // API_BASE_URL: 'https://api.vibelaunch.com'
};
```

### 5. Redis Streams (`packages/redis-streams/.env.example`)

```bash
# =================================================================
# VibeLaunch Redis Streams Configuration
# =================================================================

# -------------------- Redis Configuration --------------------
# Redis connection for event streaming
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3

# Redis Cluster (if using cluster mode)
# REDIS_CLUSTER_NODES=redis-1:6379,redis-2:6379,redis-3:6379

# -------------------- Supabase Configuration --------------------
# Database connection for dual-write mode
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...your-service-role-key

# -------------------- Consumer Configuration --------------------
# Event processing settings
CONSUMER_GROUP=vibelaunch-consumers
CONSUMER_NAME=consumer-1
BLOCK_TIME_MS=1000
BATCH_SIZE=20
MAX_RETRIES=3

# Consumer instances for horizontal scaling
INSTANCE_ID=1
TOTAL_INSTANCES=1

# -------------------- Stream Configuration --------------------
# Stream management
STREAM_PREFIX=vibelaunch
MAX_STREAM_LENGTH=1000000
TRIM_STRATEGY=MAXLEN

# Event types to process
PROCESS_CHAT_EVENTS=true
PROCESS_CONTRACT_EVENTS=true
PROCESS_PIPELINE_EVENTS=true
PROCESS_AGENT_EVENTS=true

# -------------------- Migration Configuration --------------------
# Dual-write mode settings
DUAL_WRITE_ENABLED=false
MIGRATION_BATCH_SIZE=1000
MIGRATION_DAYS_BACK=30

# -------------------- Monitoring --------------------
# Performance monitoring
METRICS_ENABLED=true
HEALTH_CHECK_PORT=8093
LAG_ALERT_THRESHOLD=1000

# -------------------- Error Handling --------------------
# Dead letter queue
DLQ_ENABLED=true
DLQ_MAX_RETRIES=5
DLQ_STREAM_SUFFIX=:dlq

# -------------------- Performance --------------------
# Processing optimization
ENABLE_PIPELINING=true
PIPELINE_SIZE=100
MEMORY_USAGE_LIMIT_MB=256

# -------------------- Development --------------------
LOG_LEVEL=debug
NODE_ENV=development

# -------------------- Production --------------------
# Production Redis configuration
# REDIS_URL=redis://redis.railway.internal:6379
# REDIS_TLS_ENABLED=true
# REDIS_CONNECTION_POOL_SIZE=20
```

## Environment Validation System

### 1. Startup Validation

**Master Validator** (`shared/env-validator.ts`):
```typescript
export class EnvironmentValidator {
  private static validators = new Map<string, z.ZodSchema>();
  
  static registerPackage(packageName: string, schema: z.ZodSchema): void {
    this.validators.set(packageName, schema);
  }
  
  static async validateAll(): Promise<ValidationResult> {
    const results = new Map<string, PackageValidation>();
    
    for (const [packageName, schema] of this.validators) {
      try {
        const validated = schema.parse(process.env);
        results.set(packageName, {
          package: packageName,
          valid: true,
          config: validated
        });
      } catch (error) {
        results.set(packageName, {
          package: packageName,
          valid: false,
          errors: this.formatErrors(error)
        });
      }
    }
    
    return {
      allValid: Array.from(results.values()).every(r => r.valid),
      packages: results,
      summary: this.generateSummary(results)
    };
  }
  
  private static formatErrors(error: z.ZodError): EnvError[] {
    return error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      received: err.received,
      expected: err.expected
    }));
  }
  
  private static generateSummary(results: Map<string, PackageValidation>): ValidationSummary {
    const packages = Array.from(results.values());
    const validPackages = packages.filter(p => p.valid);
    const invalidPackages = packages.filter(p => !p.valid);
    
    return {
      totalPackages: packages.length,
      validPackages: validPackages.length,
      invalidPackages: invalidPackages.length,
      criticalErrors: invalidPackages.filter(p => 
        p.errors?.some(e => e.field.includes('SUPABASE') || e.field.includes('ENCRYPTION'))
      ).length
    };
  }
}

// Usage in each package
// packages/agent/src/index.ts
EnvironmentValidator.registerPackage('agent', agentEnvSchema);
EnvironmentValidator.registerPackage('sequential-thinking', sequentialThinkingEnvSchema);
EnvironmentValidator.registerPackage('worker', workerEnvSchema);

async function bootstrap() {
  const validation = await EnvironmentValidator.validateAll();
  
  if (!validation.allValid) {
    console.error('Environment validation failed:');
    for (const [name, result] of validation.packages) {
      if (!result.valid) {
        console.error(`❌ ${name}:`);
        result.errors?.forEach(err => {
          console.error(`   ${err.field}: ${err.message}`);
        });
      }
    }
    process.exit(1);
  }
  
  console.log('✅ All environment configurations valid');
  // Continue with application startup
}
```

### 2. Runtime Configuration Management

**Configuration Manager** (`shared/config-manager.ts`):
```typescript
export class ConfigManager {
  private static instance: ConfigManager;
  private configs = new Map<string, any>();
  private watchers = new Set<ConfigWatcher>();
  
  static getInstance(): ConfigManager {
    if (!this.instance) {
      this.instance = new ConfigManager();
    }
    return this.instance;
  }
  
  registerConfig<T>(packageName: string, config: T): void {
    this.configs.set(packageName, config);
    this.notifyWatchers(packageName, config);
  }
  
  getConfig<T>(packageName: string): T {
    const config = this.configs.get(packageName);
    if (!config) {
      throw new Error(`Configuration for package '${packageName}' not found`);
    }
    return config as T;
  }
  
  updateConfig<T>(packageName: string, updates: Partial<T>): void {
    const currentConfig = this.getConfig<T>(packageName);
    const newConfig = { ...currentConfig, ...updates };
    this.configs.set(packageName, newConfig);
    this.notifyWatchers(packageName, newConfig);
  }
  
  watchConfig(packageName: string, callback: (config: any) => void): void {
    this.watchers.add({ packageName, callback });
  }
  
  private notifyWatchers(packageName: string, config: any): void {
    this.watchers.forEach(watcher => {
      if (watcher.packageName === packageName) {
        watcher.callback(config);
      }
    });
  }
  
  // Environment-specific overrides
  applyEnvironmentOverrides(environment: 'development' | 'staging' | 'production'): void {
    const overrides = this.getEnvironmentOverrides(environment);
    
    for (const [packageName, packageOverrides] of Object.entries(overrides)) {
      const currentConfig = this.configs.get(packageName);
      if (currentConfig) {
        this.updateConfig(packageName, packageOverrides);
      }
    }
  }
  
  private getEnvironmentOverrides(environment: string): Record<string, any> {
    switch (environment) {
      case 'production':
        return {
          agent: {
            LOG_LEVEL: 'warn',
            DEBUG_ENABLED: false,
            METRICS_ENABLED: true,
            CACHE_TTL: 7200
          },
          'sequential-thinking': {
            LOG_LEVEL: 'error',
            MAX_CONCURRENT_SESSIONS: 50
          },
          worker: {
            WORKER_CONCURRENCY: 20,
            POLL_INTERVAL: 500
          }
        };
      case 'staging':
        return {
          agent: {
            LOG_LEVEL: 'info',
            METRICS_ENABLED: true
          }
        };
      default:
        return {};
    }
  }
}
```

## Secret Management

### 1. Encryption Service

**Secret Encryption** (`shared/secret-manager.ts`):
```typescript
export class SecretManager {
  private cipher: Cipher;
  private keyDerivation: KeyDerivation;
  
  constructor(masterKey: string) {
    this.keyDerivation = new KeyDerivation(masterKey);
    this.cipher = new AESCipher();
  }
  
  async encryptSecret(secret: string, context: string): Promise<EncryptedSecret> {
    const derivedKey = await this.keyDerivation.deriveKey(context);
    const encrypted = await this.cipher.encrypt(secret, derivedKey);
    
    return {
      encryptedData: encrypted.data,
      iv: encrypted.iv,
      tag: encrypted.tag,
      context: context,
      algorithm: 'aes-256-gcm',
      keyDerivation: 'pbkdf2',
      createdAt: new Date().toISOString()
    };
  }
  
  async decryptSecret(encryptedSecret: EncryptedSecret): Promise<string> {
    const derivedKey = await this.keyDerivation.deriveKey(encryptedSecret.context);
    
    return await this.cipher.decrypt({
      data: encryptedSecret.encryptedData,
      iv: encryptedSecret.iv,
      tag: encryptedSecret.tag
    }, derivedKey);
  }
  
  // Automatic secret rotation
  async rotateSecret(secretId: string): Promise<EncryptedSecret> {
    const currentSecret = await this.getSecret(secretId);
    const decrypted = await this.decryptSecret(currentSecret);
    
    // Generate new context for rotation
    const newContext = `${currentSecret.context}_${Date.now()}`;
    const rotatedSecret = await this.encryptSecret(decrypted, newContext);
    
    await this.storeSecret(secretId, rotatedSecret);
    await this.archiveSecret(secretId, currentSecret);
    
    return rotatedSecret;
  }
}
```

### 2. LLM Credential Management

**Provider-Specific Encryption**:
```typescript
export class LLMCredentialManager {
  private secretManager: SecretManager;
  private organizationSecrets = new Map<string, Map<string, EncryptedSecret>>();
  
  constructor(masterKey: string) {
    this.secretManager = new SecretManager(masterKey);
  }
  
  async storeLLMCredentials(
    organizationId: string, 
    provider: LLMProvider, 
    credentials: LLMCredentials
  ): Promise<void> {
    const context = `llm_${provider}_${organizationId}`;
    
    const encryptedApiKey = await this.secretManager.encryptSecret(
      credentials.apiKey, 
      context
    );
    
    if (!this.organizationSecrets.has(organizationId)) {
      this.organizationSecrets.set(organizationId, new Map());
    }
    
    this.organizationSecrets.get(organizationId)!.set(provider, encryptedApiKey);
    
    // Store in database
    await this.persistCredentials(organizationId, provider, encryptedApiKey);
  }
  
  async getLLMCredentials(
    organizationId: string, 
    provider: LLMProvider
  ): Promise<LLMCredentials> {
    const orgSecrets = this.organizationSecrets.get(organizationId);
    let encryptedSecret = orgSecrets?.get(provider);
    
    if (!encryptedSecret) {
      encryptedSecret = await this.loadCredentials(organizationId, provider);
      if (!encryptedSecret) {
        throw new Error(`No credentials found for ${provider} in organization ${organizationId}`);
      }
    }
    
    const apiKey = await this.secretManager.decryptSecret(encryptedSecret);
    
    return {
      apiKey,
      provider,
      organizationId,
      expiresAt: this.calculateExpiry(encryptedSecret)
    };
  }
  
  // Automatic credential rotation
  async rotateCredentials(
    organizationId: string, 
    provider: LLMProvider
  ): Promise<void> {
    const secretId = `${organizationId}_${provider}`;
    await this.secretManager.rotateSecret(secretId);
    
    // Notify organization of rotation requirement
    await this.notifyCredentialRotation(organizationId, provider);
  }
}
```

## Environment-Specific Configurations

### 1. Development Environment

**Local Development Setup** (`.env.development`):
```bash
# Development-specific overrides
NODE_ENV=development
LOG_LEVEL=debug

# Local services
SUPABASE_URL=http://localhost:54321
REDIS_HOST=localhost
REDIS_PORT=6379

# Development flags
DEBUG_ENABLED=true
DEBUG_SQL=true
DEBUG_EVENTS=true
MOCK_LLM_RESPONSES=false

# Relaxed security for development
RATE_LIMIT_MAX_REQUESTS=1000
WEBHOOK_SECRET=dev-webhook-secret
ENCRYPTION_KEY=development-encryption-key-32chars!

# Local service URLs
MASTER_AGENT_URL=http://localhost:8090
SEQUENTIAL_THINKING_URL=http://localhost:8091
WORKER_URL=http://localhost:8092

# Development database
DB_POOL_SIZE=5
DB_TIMEOUT=5000
```

### 2. Staging Environment

**Staging Configuration** (`.env.staging`):
```bash
# Staging environment
NODE_ENV=staging
LOG_LEVEL=info

# Staging Supabase project
SUPABASE_URL=https://staging-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=${STAGING_SUPABASE_SERVICE_KEY}

# Production-like settings with debug
DEBUG_ENABLED=false
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true

# Moderate performance settings
DB_POOL_SIZE=10
WORKER_CONCURRENCY=5
CACHE_TTL=1800

# Staging-specific features
ENABLE_EXPERIMENTAL_FEATURES=true
FEATURE_FLAG_CONTRACTS_V2=true
```

### 3. Production Environment

**Production Configuration** (`.env.production`):
```bash
# Production environment
NODE_ENV=production
LOG_LEVEL=warn

# Production Supabase
SUPABASE_URL=${PRODUCTION_SUPABASE_URL}
SUPABASE_SERVICE_ROLE_KEY=${PRODUCTION_SUPABASE_SERVICE_KEY}

# Security hardening
ENCRYPTION_KEY=${PRODUCTION_ENCRYPTION_KEY}
WEBHOOK_SECRET=${PRODUCTION_WEBHOOK_SECRET}
JWT_SECRET=${PRODUCTION_JWT_SECRET}

# Production performance
DB_POOL_SIZE=50
WORKER_CONCURRENCY=20
CACHE_TTL=7200

# Production services
REDIS_URL=${PRODUCTION_REDIS_URL}
REDIS_TLS_ENABLED=true

# Monitoring and observability
METRICS_ENABLED=true
SENTRY_DSN=${PRODUCTION_SENTRY_DSN}
NEW_RELIC_LICENSE_KEY=${PRODUCTION_NEW_RELIC_KEY}

# Production flags
DEBUG_ENABLED=false
MOCK_LLM_RESPONSES=false
ENABLE_EXPERIMENTAL_FEATURES=false

# Rate limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# Error handling
CIRCUIT_BREAKER_ENABLED=true
DLQ_ENABLED=true
```

## Configuration Management Tools

### 1. Environment Checker

**Health Check Tool** (`scripts/check-env.js`):
```javascript
#!/usr/bin/env node

const { EnvironmentValidator } = require('../shared/env-validator');
const fs = require('fs');
const path = require('path');

async function checkEnvironment() {
  console.log('🔍 Checking VibeLaunch environment configuration...\n');
  
  // Check for .env files
  const packages = ['agent', 'ui', 'worker', 'sequential-thinking', 'redis-streams'];
  const missingEnvFiles = [];
  
  for (const pkg of packages) {
    const envPath = path.join(__dirname, '..', 'packages', pkg, '.env');
    if (!fs.existsSync(envPath)) {
      missingEnvFiles.push(`packages/${pkg}/.env`);
    }
  }
  
  if (missingEnvFiles.length > 0) {
    console.log('❌ Missing .env files:');
    missingEnvFiles.forEach(file => console.log(`   ${file}`));
    console.log('\n💡 Copy from .env.example files and configure with your values\n');
  } else {
    console.log('✅ All .env files present\n');
  }
  
  // Validate environment variables
  const validation = await EnvironmentValidator.validateAll();
  
  if (validation.allValid) {
    console.log('✅ All environment configurations valid');
    console.log(`📊 Validated ${validation.summary.totalPackages} packages\n`);
  } else {
    console.log('❌ Environment validation failed:\n');
    
    for (const [name, result] of validation.packages) {
      if (!result.valid) {
        console.log(`❌ ${name}:`);
        result.errors?.forEach(err => {
          console.log(`   ${err.field}: ${err.message}`);
        });
        console.log('');
      }
    }
    
    console.log('🔧 Fix the above issues and run the check again\n');
    process.exit(1);
  }
  
  // Check service connectivity
  await checkServiceConnectivity();
}

async function checkServiceConnectivity() {
  console.log('🌐 Checking service connectivity...\n');
  
  const checks = [
    { name: 'Supabase', url: process.env.SUPABASE_URL + '/health' },
    { name: 'Master Agent', url: 'http://localhost:8090/health' },
    { name: 'Sequential Thinking', url: 'http://localhost:8091/health' },
  ];
  
  for (const check of checks) {
    try {
      const response = await fetch(check.url);
      if (response.ok) {
        console.log(`✅ ${check.name} - Connected`);
      } else {
        console.log(`⚠️  ${check.name} - Responded with ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ${check.name} - Connection failed`);
    }
  }
}

if (require.main === module) {
  checkEnvironment().catch(console.error);
}
```

### 2. Configuration Generator

**Setup Tool** (`scripts/setup-env.js`):
```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function setupEnvironment() {
  console.log('🚀 VibeLaunch Environment Setup\n');
  
  // Collect basic configuration
  const config = await collectConfiguration();
  
  // Generate secure secrets
  config.ENCRYPTION_KEY = crypto.randomBytes(32).toString('hex');
  config.WEBHOOK_SECRET = crypto.randomBytes(16).toString('hex');
  config.JWT_SECRET = crypto.randomBytes(32).toString('hex');
  
  // Create .env files for each package
  await createEnvironmentFiles(config);
  
  console.log('\n✅ Environment setup complete!');
  console.log('\n📋 Next steps:');
  console.log('1. Review and customize the generated .env files');
  console.log('2. Run `npm run check:env` to validate configuration');
  console.log('3. Start development with `npm run dev`');
}

async function collectConfiguration() {
  const config = {};
  
  console.log('📋 Please provide the following configuration:\n');
  
  config.SUPABASE_URL = await question('Supabase URL: ');
  config.SUPABASE_SERVICE_ROLE_KEY = await question('Supabase Service Role Key: ');
  config.SUPABASE_ANON_KEY = await question('Supabase Anon Key: ');
  
  console.log('\n🤖 LLM Provider Configuration (at least one required):');
  config.OPENAI_API_KEY = await question('OpenAI API Key (optional): ');
  config.ANTHROPIC_API_KEY = await question('Anthropic API Key (optional): ');
  config.GOOGLE_AI_API_KEY = await question('Google AI API Key (optional): ');
  
  if (!config.OPENAI_API_KEY && !config.ANTHROPIC_API_KEY && !config.GOOGLE_AI_API_KEY) {
    throw new Error('At least one LLM provider API key is required');
  }
  
  return config;
}

async function createEnvironmentFiles(config) {
  const packages = [
    { name: 'agent', template: 'agent-env-template' },
    { name: 'worker', template: 'worker-env-template' },
    { name: 'sequential-thinking', template: 'sequential-thinking-env-template' },
    { name: 'redis-streams', template: 'redis-streams-env-template' }
  ];
  
  for (const pkg of packages) {
    const envPath = path.join(__dirname, '..', 'packages', pkg.name, '.env');
    const examplePath = path.join(__dirname, '..', 'packages', pkg.name, '.env.example');
    
    let template = fs.readFileSync(examplePath, 'utf8');
    
    // Replace placeholders with actual values
    for (const [key, value] of Object.entries(config)) {
      if (value) {
        template = template.replace(new RegExp(`your-${key.toLowerCase().replace(/_/g, '-')}`, 'g'), value);
        template = template.replace(new RegExp(`\\$\\{${key}\\}`, 'g'), value);
      }
    }
    
    fs.writeFileSync(envPath, template);
    console.log(`✅ Created packages/${pkg.name}/.env`);
  }
  
  // Create UI config
  const uiConfigPath = path.join(__dirname, '..', 'packages', 'ui', 'public', 'config.js');
  const uiTemplate = `window.ENV = {
  SUPABASE_URL: '${config.SUPABASE_URL}',
  SUPABASE_ANON_KEY: '${config.SUPABASE_ANON_KEY}',
  NODE_ENV: 'development',
  MASTER_AGENT_URL: 'http://localhost:8090',
  SEQUENTIAL_THINKING_URL: 'http://localhost:8091',
  ENABLE_DEBUG_PANEL: true,
  REALTIME_ENABLED: true
};`;
  
  fs.writeFileSync(uiConfigPath, uiTemplate);
  console.log('✅ Created packages/ui/public/config.js');
}

function question(prompt) {
  return new Promise(resolve => rl.question(prompt, resolve));
}

if (require.main === module) {
  setupEnvironment()
    .then(() => rl.close())
    .catch(error => {
      console.error('❌ Setup failed:', error.message);
      rl.close();
      process.exit(1);
    });
}
```

## Integration with VibeLaunch

### 1. Startup Sequence

**Application Bootstrap** (`packages/agent/src/bootstrap.ts`):
```typescript
export async function bootstrap(): Promise<void> {
  console.log('🚀 Starting VibeLaunch Master Agent...');
  
  // 1. Validate environment
  console.log('📋 Validating environment configuration...');
  const config = validateEnvironment();
  ConfigManager.getInstance().registerConfig('agent', config);
  
  // 2. Initialize security
  console.log('🔒 Initializing security services...');
  const secretManager = new SecretManager(config.ENCRYPTION_KEY);
  const credentialManager = new LLMCredentialManager(config.ENCRYPTION_KEY);
  
  // 3. Apply environment-specific overrides
  console.log('⚙️  Applying environment overrides...');
  ConfigManager.getInstance().applyEnvironmentOverrides(config.NODE_ENV);
  
  // 4. Initialize database connections
  console.log('💾 Connecting to database...');
  await initializeDatabase(config);
  
  // 5. Initialize LLM providers
  console.log('🤖 Initializing LLM providers...');
  await initializeLLMProviders(config, credentialManager);
  
  // 6. Start monitoring
  console.log('📊 Starting monitoring services...');
  await initializeMonitoring(config);
  
  // 7. Start web server
  console.log('🌐 Starting web server...');
  await startWebServer(config);
  
  console.log(`✅ Master Agent started successfully on port ${config.PORT}`);
}
```

### 2. Runtime Configuration Updates

**Hot Configuration Reloading**:
```typescript
export class ConfigWatcher {
  private watchers = new Map<string, FSWatcher>();
  
  watchEnvironmentFiles(): void {
    const envFiles = [
      'packages/agent/.env',
      'packages/worker/.env',
      'packages/sequential-thinking/.env'
    ];
    
    envFiles.forEach(file => {
      const watcher = fs.watch(file, (eventType) => {
        if (eventType === 'change') {
          this.reloadConfiguration(file);
        }
      });
      
      this.watchers.set(file, watcher);
    });
  }
  
  private async reloadConfiguration(file: string): Promise<void> {
    console.log(`🔄 Reloading configuration from ${file}...`);
    
    try {
      // Re-validate environment
      const newConfig = validateEnvironment();
      
      // Update runtime configuration
      const packageName = this.getPackageNameFromFile(file);
      ConfigManager.getInstance().updateConfig(packageName, newConfig);
      
      console.log(`✅ Configuration reloaded for ${packageName}`);
    } catch (error) {
      console.error(`❌ Failed to reload configuration: ${error.message}`);
    }
  }
}
```

## Future Enhancements

### 1. Advanced Secret Management

**Planned Features**:
- **HashiCorp Vault Integration**: Enterprise secret management
- **AWS Secrets Manager**: Cloud-native secret storage
- **Azure Key Vault**: Microsoft cloud integration
- **Automatic Secret Rotation**: Zero-downtime credential updates

### 2. Configuration as Code

**Infrastructure as Code**:
- **Terraform Modules**: Infrastructure provisioning with secrets
- **Helm Charts**: Kubernetes deployment with config management
- **Ansible Playbooks**: Automated environment setup
- **GitOps Integration**: Configuration version control

### 3. Dynamic Configuration

**Runtime Features**:
- **Feature Flags**: Runtime feature toggles
- **A/B Testing**: Configuration-driven experiments
- **Canary Deployments**: Gradual configuration rollouts
- **Circuit Breakers**: Automatic configuration fallbacks

---

This environment system provides VibeLaunch with enterprise-grade configuration management, ensuring secure, scalable, and maintainable deployment across all environments. The system is designed for both developer productivity and production reliability.