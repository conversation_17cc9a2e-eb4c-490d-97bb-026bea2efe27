# System Outage Response Runbook

## Purpose
This runbook provides step-by-step procedures for responding to VibeLaunch system outages, from initial detection through full resolution and post-incident review.

## Severity Levels

### P0 - Critical Outage
- Complete system unavailable
- All users unable to access VibeLaunch
- Revenue-impacting outage
- **Response Time**: Immediate (< 5 minutes)

### P1 - Major Outage  
- Core functionality unavailable
- Significant user impact
- Some services still functional
- **Response Time**: < 15 minutes

### P2 - Minor Outage
- Non-critical features unavailable
- Limited user impact
- Core functionality working
- **Response Time**: < 1 hour

## Prerequisites

### Required Access
- [ ] Production environment access
- [ ] Monitoring dashboards (Grafana, Prometheus)
- [ ] Supabase dashboard access
- [ ] Railway deployment access
- [ ] Status page update permissions

### Required Tools
- [ ] Railway CLI installed and authenticated
- [ ] VPN connection (if required)
- [ ] Communication tools (Slack, phone)
- [ ] Incident management system access

## Response Procedures

### Phase 1: Initial Response (0-5 minutes)

#### Step 1: Confirm Outage
```bash
# Check system health endpoints
curl -I https://app.vibelaunch.com/health
curl -I https://api.vibelaunch.com/health

# Check monitoring dashboards
# - Open Grafana: https://monitoring.vibelaunch.com
# - Check VibeLaunch overview dashboard
# - Look for red indicators or downtime
```

**Verification**: At least 2 independent sources confirm outage

#### Step 2: Activate Incident Response
```bash
# Start incident timer
INCIDENT_START=$(date -u +"%Y-%m-%d %H:%M:%S UTC")
echo "Incident started at: $INCIDENT_START"

# Create incident ID
INCIDENT_ID="INC-$(date +%Y%m%d%H%M%S)"
echo "Incident ID: $INCIDENT_ID"
```

#### Step 3: Initial Communication
- [ ] Update status page: "Investigating reports of service issues"
- [ ] Post in #incidents Slack channel
- [ ] Notify on-call manager if P0/P1

**Communication Template**:
```
🚨 INCIDENT ALERT
ID: INC-YYYYMMDDHHMMSS
Severity: P0/P1/P2
Status: Investigating
Started: YYYY-MM-DD HH:MM UTC
Impact: [Brief description]
```

### Phase 2: Assessment (5-15 minutes)

#### Step 4: Determine Scope and Impact
```bash
# Check service status
railway status --project vibelaunch

# Check individual services
for service in master-agent sequential-thinking webhook-receiver worker; do
  echo "Checking $service..."
  railway logs --service $service --tail 50
done

# Check database connectivity
psql $SUPABASE_DATABASE_URL -c "SELECT NOW();"

# Check external dependencies
curl -I https://api.openai.com/v1/models
curl -I https://api.anthropic.com/v1/models
```

#### Step 5: Identify Root Cause
Use this decision tree:

```mermaid
graph TD
    A[Services Responding?] -->|No| B[Infrastructure Issue]
    A -->|Yes| C[Database Accessible?]
    B --> B1[Check Railway Status]
    B --> B2[Check Service Logs]
    C -->|No| D[Database Issue]
    C -->|Yes| E[Application Issue]
    D --> D1[Check Supabase Status]
    D --> D2[Check Connection Limits]
    E --> E1[Check Application Logs]
    E --> E2[Check Error Rates]
```

**Common Root Causes**:
1. **Railway Platform Issues**
   - Check [Railway Status](https://status.railway.app)
   - Check service deployment status
   
2. **Database Issues**  
   - Supabase outage or maintenance
   - Connection pool exhaustion
   - Long-running queries
   
3. **Application Errors**
   - Recent deployments
   - Configuration changes
   - Resource exhaustion

4. **External Dependencies**
   - LLM provider outages
   - Third-party service failures

#### Step 6: Update Stakeholders
```bash
# Update status page with findings
# Example: "We have identified the issue with our database connectivity and are working on a resolution"

# Post update in incident channel
# Include:
# - Root cause (if identified)
# - Estimated time to resolution
# - Current mitigation efforts
```

### Phase 3: Resolution (15 minutes - 2 hours)

#### Step 7: Implement Fix

**For Infrastructure Issues:**
```bash
# Restart failed services
railway service restart --service master-agent
railway service restart --service sequential-thinking

# Check deployment status
railway status

# Verify services are healthy
for service in master-agent sequential-thinking webhook-receiver worker; do
  railway logs --service $service --tail 10
done
```

**For Database Issues:**
```bash
# Check connection pool status
psql $SUPABASE_DATABASE_URL -c "
  SELECT 
    pid, 
    state, 
    application_name, 
    client_addr 
  FROM pg_stat_activity 
  WHERE state != 'idle';"

# Kill problematic connections if needed
psql $SUPABASE_DATABASE_URL -c "
  SELECT pg_terminate_backend(pid) 
  FROM pg_stat_activity 
  WHERE state = 'active' 
    AND now() - query_start > interval '5 minutes';"

# Restart connection pools
railway service restart --service master-agent
```

**For Application Issues:**
```bash
# Rollback recent deployment if needed
railway deployment rollback --service master-agent

# Or deploy hotfix
git checkout main
git pull origin main
railway deploy --service master-agent

# Monitor deployment
railway logs --service master-agent --follow
```

#### Step 8: Verify Resolution
```bash
# Test critical user journeys
./scripts/smoke-test.sh

# Check health endpoints
curl https://app.vibelaunch.com/health
curl https://api.vibelaunch.com/health

# Monitor key metrics for 10 minutes
# - Response times
# - Error rates  
# - Active users
```

**Success Criteria**:
- [ ] All health checks passing
- [ ] Error rates < 1%
- [ ] Response times < 2 seconds
- [ ] Users can complete core workflows

### Phase 4: Communication and Recovery (Ongoing)

#### Step 9: Announce Resolution
```bash
# Update status page
# "The issue has been resolved. All services are operating normally."

# Post in incident channel
# Include:
# - Resolution confirmation
# - Summary of fix applied
# - Next steps for monitoring
```

#### Step 10: Monitor for Recurrence
```bash
# Extended monitoring period (2-4 hours)
# - Check metrics every 15 minutes
# - Monitor error logs
# - Watch for user reports

# Set up alerts for early detection
# - Response time alerts
# - Error rate thresholds
# - Health check failures
```

## Escalation Procedures

### When to Escalate

**Immediate Escalation (< 15 minutes)**:
- Unable to identify root cause
- Resolution attempts unsuccessful
- Multiple systems affected
- Data integrity concerns

**Secondary Escalation (< 1 hour)**:
- Complex technical issues
- Requires architectural changes
- External vendor involvement needed
- Legal/compliance implications

### Escalation Contacts

1. **Technical Lead**: @tech-lead (Slack)
2. **Engineering Manager**: +1-XXX-XXX-XXXX
3. **CTO**: <EMAIL>
4. **External Vendors**:
   - Railway Support: <EMAIL>
   - Supabase Support: <EMAIL>

## Rollback Procedures

### Application Rollback
```bash
# Identify last known good deployment
railway deployment list --service master-agent

# Rollback to previous version
railway deployment rollback --service master-agent --to <deployment-id>

# Verify rollback success
railway logs --service master-agent --tail 20
curl https://api.vibelaunch.com/health
```

### Configuration Rollback
```bash
# Revert environment variables
railway variables list --service master-agent
railway variables set VARIABLE_NAME=previous_value --service master-agent

# Restart service to apply changes
railway service restart --service master-agent
```

### Database Rollback
```sql
-- Only if schema changes were made
-- Use migration rollback (dangerous - contact DBA)
-- Restore from backup if data corruption
```

⚠️ **Warning**: Database rollbacks can cause data loss. Always consult DBA before proceeding.

## Post-Incident Procedures

### Immediate Actions (Within 24 hours)
- [ ] Document timeline of events
- [ ] Collect all relevant logs and metrics
- [ ] Identify what worked well and what didn't
- [ ] Schedule post-mortem meeting

### Post-Mortem Meeting (Within 1 week)
- [ ] Blameless review of incident
- [ ] Identify root cause and contributing factors
- [ ] Define action items to prevent recurrence
- [ ] Update runbooks and procedures

### Follow-up Actions (Within 1 month)
- [ ] Implement preventive measures
- [ ] Update monitoring and alerting
- [ ] Test incident response procedures
- [ ] Share learnings with team

## Common Scenarios and Quick Fixes

### Scenario 1: High Response Times
```bash
# Check CPU and memory usage
railway metrics --service master-agent

# Scale up if needed
railway service scale --service master-agent --replicas 3

# Check database performance
psql $SUPABASE_DATABASE_URL -c "
  SELECT query, calls, mean_exec_time 
  FROM pg_stat_statements 
  ORDER BY mean_exec_time DESC 
  LIMIT 10;"
```

### Scenario 2: Database Connection Errors
```bash
# Check connection count
psql $SUPABASE_DATABASE_URL -c "
  SELECT count(*) as active_connections 
  FROM pg_stat_activity 
  WHERE state != 'idle';"

# Reset connections
railway service restart --service master-agent
railway service restart --service worker

# Monitor recovery
watch -n 5 'curl -s https://api.vibelaunch.com/health | jq .database'
```

### Scenario 3: LLM Provider Outages
```bash
# Check provider status
curl -I https://api.openai.com/v1/models
curl -I https://api.anthropic.com/v1/complete

# Switch to backup provider
railway variables set PRIMARY_LLM_PROVIDER=anthropic --service master-agent
railway service restart --service master-agent

# Monitor fallback success
railway logs --service master-agent --grep "LLM provider"
```

## Prevention Checklist

### Weekly Health Checks
- [ ] Review system metrics trends
- [ ] Check for capacity issues  
- [ ] Update dependencies
- [ ] Test backup systems

### Monthly Exercises  
- [ ] Conduct outage simulation
- [ ] Test rollback procedures
- [ ] Review and update runbooks
- [ ] Train new team members

### Quarterly Reviews
- [ ] Analyze incident patterns
- [ ] Update escalation procedures
- [ ] Review monitoring coverage
- [ ] Capacity planning review

## Documentation and Reporting

### Incident Report Template
```markdown
# Incident Report: [Incident ID]

## Summary
- Start Time: YYYY-MM-DD HH:MM UTC
- End Time: YYYY-MM-DD HH:MM UTC  
- Duration: X hours Y minutes
- Severity: P0/P1/P2
- Services Affected: [List]

## Impact
- Users Affected: X
- Revenue Impact: $X
- Feature Availability: X%

## Root Cause
[Detailed explanation]

## Timeline
[Chronological list of events]

## Resolution
[What fixed the issue]

## Lessons Learned
[What went well, what didn't]

## Action Items
[Specific tasks to prevent recurrence]
```

### Metrics to Track
- Mean Time to Detection (MTTD)
- Mean Time to Resolution (MTTR)  
- Customer Impact Duration
- Number of False Alarms
- Escalation Frequency

## Related Documentation
- [Monitoring Setup](../../monitoring/prometheus-setup.md)
- [Security Incident Response](./security-incident.md)
- [Database Recovery](./database-recovery.md)
- [Troubleshooting Guide](../../troubleshooting/README.md)