const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testGetLlmConfig() {
  console.log('Testing get_llm_config RPC...\n');
  
  try {
    // Test with a sample org_id
    const { data, error } = await supabase.rpc('get_llm_config', {
      p_org_id: '00000000-0000-0000-0000-000000000000'
    });
    
    if (error) {
      console.error('❌ Error calling get_llm_config:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      
      // Check if it's the specific column error
      if (error.message && error.message.includes('column c.default_model does not exist')) {
        console.error('\n⚠️  CONFIRMED: The "column c.default_model does not exist" error is still present!');
      }
    } else {
      console.log('✅ get_llm_config executed successfully');
      console.log('Result:', JSON.stringify(data, null, 2));
    }
  } catch (err) {
    console.error('❌ Unexpected error:', err);
  }
}

testGetLlmConfig();