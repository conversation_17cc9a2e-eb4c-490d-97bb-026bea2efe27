const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testGetLlmConfig() {
  console.log('🔍 Testing get_llm_config function after fix...\n');
  
  // Test 1: With UUID string (should work)
  console.log('Test 1: Testing with UUID string...');
  try {
    const { data, error } = await supabase.rpc('get_llm_config', {
      p_org_id: '00000000-0000-0000-0000-000000000000'
    });
    
    if (error) {
      console.error('❌ Error with UUID string:', error);
    } else {
      console.log('✅ Success with UUID string!');
      console.log('Result:', JSON.stringify(data, null, 2));
    }
  } catch (err) {
    console.error('❌ Unexpected error:', err);
  }
  
  // Test 2: Test with the default organization ID
  console.log('\nTest 2: Testing with default organization ID...');
  try {
    const { data, error } = await supabase.rpc('get_llm_config', {
      p_org_id: '07905865-ef02-421c-aea6-5c26c81d1a79'
    });
    
    if (error) {
      console.error('❌ Error with default org:', error);
    } else {
      console.log('✅ Success with default org!');
      console.log('Result:', JSON.stringify(data, null, 2));
    }
  } catch (err) {
    console.error('❌ Unexpected error:', err);
  }
  
  // Test 3: Direct SQL query to verify the function exists
  console.log('\nTest 3: Checking function signatures in database...');
  try {
    const { data, error } = await supabase
      .from('pg_proc')
      .select('proname')
      .eq('proname', 'get_llm_config');
    
    if (error) {
      console.error('❌ Error querying pg_proc:', error);
    } else {
      console.log('Functions found:', data?.length || 0);
    }
  } catch (err) {
    console.error('❌ Unexpected error:', err);
  }
  
  // Test 4: Try calling with explicit casting
  console.log('\nTest 4: Testing with explicit UUID cast...');
  try {
    // Use raw SQL to test
    const query = `SELECT * FROM get_llm_config('07905865-ef02-421c-aea6-5c26c81d1a79'::uuid)`;
    
    const { data, error } = await supabase.rpc('execute_sql', { 
      sql: query 
    }).catch(() => {
      // If execute_sql doesn't exist, try a different approach
      return { data: null, error: 'execute_sql not available' };
    });
    
    if (error) {
      console.log('ℹ️  Direct SQL execution not available through RPC');
    } else if (data) {
      console.log('✅ Direct SQL execution successful!');
      console.log('Result:', JSON.stringify(data, null, 2));
    }
  } catch (err) {
    console.error('❌ Unexpected error:', err);
  }
}

async function checkDatabaseSchema() {
  console.log('\n📊 Checking database schema...\n');
  
  // Check org_llm_credentials columns
  console.log('Checking org_llm_credentials table...');
  try {
    const { data, error } = await supabase
      .from('org_llm_credentials')
      .select('*')
      .limit(0);
    
    if (error) {
      console.error('❌ Error accessing org_llm_credentials:', error);
    } else {
      console.log('✅ org_llm_credentials table accessible');
    }
  } catch (err) {
    console.error('❌ Unexpected error:', err);
  }
  
  // Check org_llm_settings columns
  console.log('\nChecking org_llm_settings table...');
  try {
    const { data, error } = await supabase
      .from('org_llm_settings')
      .select('*')
      .limit(0);
    
    if (error) {
      console.error('❌ Error accessing org_llm_settings:', error);
    } else {
      console.log('✅ org_llm_settings table accessible');
    }
  } catch (err) {
    console.error('❌ Unexpected error:', err);
  }
}

async function main() {
  console.log('🔧 Comprehensive Test of get_llm_config Fix\n');
  console.log('=' .repeat(60) + '\n');
  
  await testGetLlmConfig();
  await checkDatabaseSchema();
  
  console.log('\n' + '=' .repeat(60));
  console.log('✅ Testing complete!');
}

main().catch(console.error);