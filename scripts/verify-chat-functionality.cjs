const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = 'https://umsrsnewtfwegtqowmmj.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtc3JzbmV3dGZ3ZWd0cW93bW1qIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczMjgyNDMsImV4cCI6MjA2MjkwNDI0M30.SgjcIrzmP3qaN89i_jPCFQgialB_4G_t4Tj0osRGbP0';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function verifyChatFunctionality() {
  try {
    console.log('=== VERIFYING CHAT FUNCTIONALITY ===\n');
    
    // Sign in with test user
    console.log('1. Signing in with test user...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'TestVerification123!'
    });
    
    if (authError) {
      console.error('Error signing in:', authError);
      return;
    }
    
    console.log('✅ Signed in successfully');
    console.log('User ID:', authData.user.id);
    
    // Get or create a channel
    console.log('\n2. Getting/creating a channel...');
    const { data: channels, error: channelError } = await supabase
      .from('channels')
      .select('*')
      .eq('created_by', authData.user.id)
      .limit(1);
      
    let channelId;
    if (channelError || !channels || channels.length === 0) {
      // Create a new channel
      const { data: newChannel, error: createChannelError } = await supabase
        .from('channels')
        .insert({
          name: 'Test Verification Channel',
          created_by: authData.user.id,
          is_private: false
        })
        .select()
        .single();
        
      if (createChannelError) {
        console.error('Error creating channel:', createChannelError);
        return;
      }
      
      channelId = newChannel.id;
      console.log('✅ Created new channel:', channelId);
    } else {
      channelId = channels[0].id;
      console.log('✅ Using existing channel:', channelId);
    }
    
    // Send a test message
    console.log('\n3. Sending test message...');
    const testMessage = {
      channel_id: channelId,
      user_id: authData.user.id,
      content: 'Test message for verification: Hello from the verification script!',
      metadata: {
        test: true,
        timestamp: new Date().toISOString()
      }
    };
    
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .insert(testMessage)
      .select()
      .single();
      
    if (messageError) {
      console.error('Error sending message:', messageError);
      console.error('This might be the PGRST203 error if the database fix was not applied correctly.');
      return;
    }
    
    console.log('✅ Message sent successfully!');
    console.log('Message ID:', message.id);
    console.log('Message content:', message.content);
    
    // Wait a moment for the event to be processed
    console.log('\n4. Waiting for event processing...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check if a pipeline was created
    console.log('\n5. Checking for pipeline creation...');
    const { data: pipelines, error: pipelineError } = await supabase
      .from('pipeline_runs')
      .select('*')
      .eq('channel_id', channelId)
      .order('created_at', { ascending: false })
      .limit(1);
      
    if (pipelineError) {
      console.error('Error checking pipelines:', pipelineError);
      return;
    }
    
    if (pipelines && pipelines.length > 0) {
      console.log('✅ Pipeline created successfully!');
      console.log('Pipeline ID:', pipelines[0].id);
      console.log('Pipeline status:', pipelines[0].status);
      console.log('Created at:', pipelines[0].created_at);
    } else {
      console.log('❌ No pipeline found. The backend might not have processed the event yet.');
      console.log('Check the Railway logs for the master-agent service.');
    }
    
    // Check for bus events
    console.log('\n6. Checking for bus events...');
    const { data: busEvents, error: busError } = await supabase
      .from('bus_events')
      .select('*')
      .eq('channel_id', channelId)
      .eq('type', 'chat_posted')
      .order('created_at', { ascending: false })
      .limit(1);
      
    if (busError) {
      console.error('Error checking bus events:', busError);
      return;
    }
    
    if (busEvents && busEvents.length > 0) {
      console.log('✅ Bus event created successfully!');
      console.log('Event ID:', busEvents[0].id);
      console.log('Event type:', busEvents[0].type);
      console.log('Created at:', busEvents[0].created_at);
    } else {
      console.log('❌ No bus event found.');
    }
    
    console.log('\n=== VERIFICATION COMPLETE ===');
    console.log('\nSUMMARY:');
    console.log('- Frontend can authenticate: ✅');
    console.log('- Messages can be sent without PGRST203 error: ✅');
    console.log(`- Pipeline created: ${pipelines && pipelines.length > 0 ? '✅' : '❌ (check backend logs)'}`);
    console.log(`- Bus event created: ${busEvents && busEvents.length > 0 ? '✅' : '❌'}`);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the verification
verifyChatFunctionality();