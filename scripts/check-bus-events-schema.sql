-- Script to check the current bus_events table schema
-- Run this in your Supabase SQL editor to see what columns exist

-- Check if bus_events table exists and show its columns
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'bus_events'
ORDER BY 
    ordinal_position;

-- Check if channel_id column exists
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'bus_events' 
            AND column_name = 'channel_id'
        ) THEN 'channel_id column EXISTS'
        ELSE 'channel_id column is MISSING'
    END as channel_id_status;

-- Check if user_id column exists
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'bus_events' 
            AND column_name = 'user_id'
        ) THEN 'user_id column EXISTS'
        ELSE 'user_id column is MISSING'
    END as user_id_status;

-- Check if channel column exists (the original column)
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'bus_events' 
            AND column_name = 'channel'
        ) THEN 'channel column EXISTS'
        ELSE 'channel column is MISSING'
    END as channel_status;