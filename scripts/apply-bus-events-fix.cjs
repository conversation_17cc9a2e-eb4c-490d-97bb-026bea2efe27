#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to apply the bus_events table fix migration
 * This adds the missing channel_id (as an alias) and user_id columns
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Get Supabase credentials from environment
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  if (!supabaseUrl) console.error('  - SUPABASE_URL or VITE_SUPABASE_URL');
  if (!supabaseServiceKey) console.error('  - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkCurrentSchema() {
  console.log('\n📊 Checking current bus_events table schema...');
  
  try {
    // Query to check column existence
    const { data: columns, error } = await supabase
      .rpc('get_table_columns', { 
        table_name: 'bus_events',
        schema_name: 'public'
      })
      .catch(() => {
        // If the RPC doesn't exist, try a direct query
        return supabase.from('bus_events').select('*').limit(0);
      });

    if (error && error.message.includes('get_table_columns')) {
      // Try alternative method to check schema
      const { error: testError } = await supabase
        .from('bus_events')
        .select('id, channel, channel_id, user_id')
        .limit(1);
      
      if (testError) {
        console.log('Current schema check results:');
        if (testError.message.includes('channel_id')) {
          console.log('  ❌ channel_id column is missing');
        }
        if (testError.message.includes('user_id')) {
          console.log('  ❌ user_id column is missing');
        }
        if (testError.message.includes('channel')) {
          console.log('  ✅ channel column exists');
        }
      } else {
        console.log('  ✅ All columns already exist');
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error checking schema:', error.message);
    return false;
  }
}

async function applyMigration() {
  console.log('\n🔧 Applying bus_events fix migration...');
  
  // Read the migration file
  const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250613_add_channel_id_to_bus_events.sql');
  
  if (!fs.existsSync(migrationPath)) {
    console.error(`❌ Migration file not found at: ${migrationPath}`);
    process.exit(1);
  }
  
  const migrationSql = fs.readFileSync(migrationPath, 'utf8');
  
  // Split the migration into individual statements
  const statements = migrationSql
    .split(';')
    .map(s => s.trim())
    .filter(s => s.length > 0 && !s.startsWith('--'));
  
  console.log(`📝 Found ${statements.length} SQL statements to execute`);
  
  // Execute each statement
  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i] + ';';
    console.log(`\n🔄 Executing statement ${i + 1}/${statements.length}:`);
    console.log(`   ${statement.substring(0, 60)}...`);
    
    try {
      const { error } = await supabase.rpc('exec_sql', { 
        sql: statement 
      }).catch(async () => {
        // If exec_sql RPC doesn't exist, we need to apply manually
        console.log('   ⚠️  exec_sql RPC not available, migration must be applied manually');
        throw new Error('Manual migration required');
      });
      
      if (error) {
        console.error(`   ❌ Error: ${error.message}`);
        if (!error.message.includes('already exists')) {
          throw error;
        }
        console.log('   ⚠️  Column already exists, continuing...');
      } else {
        console.log('   ✅ Success');
      }
    } catch (error) {
      if (error.message === 'Manual migration required') {
        console.log('\n⚠️  Cannot apply migration automatically.');
        console.log('Please run the following command to apply the migration:');
        console.log('\n  npx supabase db push\n');
        return false;
      }
      throw error;
    }
  }
  
  return true;
}

async function verifyMigration() {
  console.log('\n✅ Verifying migration...');
  
  try {
    // Test inserting a record with the new columns
    const testData = {
      event_name: 'test_migration',
      event_type: 'test',
      channel: 'test-channel',
      channel_id: 'test-channel-id',
      user_id: '00000000-0000-0000-0000-000000000000',
      organisation_id: '00000000-0000-0000-0000-000000000000',
      payload: { test: true }
    };
    
    const { error: insertError } = await supabase
      .from('bus_events')
      .insert(testData);
    
    if (insertError) {
      console.error('❌ Verification failed:', insertError.message);
      return false;
    }
    
    // Clean up test record
    await supabase
      .from('bus_events')
      .delete()
      .eq('event_name', 'test_migration');
    
    console.log('✅ Migration verified successfully!');
    return true;
  } catch (error) {
    console.error('❌ Verification error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Bus Events Table Fix Script');
  console.log('==============================');
  
  try {
    // Check current schema
    const schemaOk = await checkCurrentSchema();
    
    if (schemaOk) {
      console.log('\n✅ Schema is already up to date!');
      return;
    }
    
    // Apply migration
    const migrationApplied = await applyMigration();
    
    if (!migrationApplied) {
      console.log('\n⚠️  Migration needs to be applied manually');
      process.exit(1);
    }
    
    // Verify the migration worked
    const verified = await verifyMigration();
    
    if (verified) {
      console.log('\n🎉 Bus events table fix completed successfully!');
      console.log('\nThe worker service should now be able to publish events without errors.');
    } else {
      console.log('\n⚠️  Migration applied but verification failed');
      console.log('Please check the database manually');
    }
    
  } catch (error) {
    console.error('\n❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();