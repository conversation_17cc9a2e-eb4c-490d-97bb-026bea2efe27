-- Fix post_chat Function Ambiguity
-- This script resolves the PGRST203 error by removing ambiguous function versions

-- First, check what functions currently exist
DO $$
DECLARE
  v_count INTEGER;
  v_functions TEXT;
BEGIN
  SELECT COUNT(*), STRING_AGG(
    format('%s(%s)', p.proname, pg_catalog.pg_get_function_identity_arguments(p.oid)), 
    ', '
  )
  INTO v_count, v_functions
  FROM pg_catalog.pg_proc p
  WHERE p.proname = 'post_chat';
  
  RAISE NOTICE 'Found % post_chat function(s): %', v_count, v_functions;
END $$;

-- Drop ALL versions of post_chat to eliminate ambiguity
DROP FUNCTION IF EXISTS post_chat(uuid, uuid, text);
DROP FUNCTION IF EXISTS post_chat(uuid, uuid, text, jsonb);
DROP FUNCTION IF EXISTS post_chat(uuid, text, jsonb);
DROP FUNCTION IF EXISTS post_chat(uuid, text);

-- Recreate ONLY the 4-parameter version as <PERSON><PERSON>ECATED
CREATE OR REPLACE FUNCTION post_chat(
  p_org uuid,
  p_user_id uuid,
  p_message text,
  p_metadata jsonb DEFAULT NULL
) RETURNS uuid AS $$
DECLARE
  new_id uuid := gen_random_uuid();
  chat_id uuid := gen_random_uuid();
  v_payload jsonb;
BEGIN
  -- Log deprecation warning
  RAISE WARNING 'post_chat() is DEPRECATED. Use event-based communication instead. Frontend should publish chat_posted events.';
  
  -- Insert the message into chat_log with metadata
  INSERT INTO chat_log(id, chat_id, sender, message, metadata, created_at, user_id)
  VALUES (new_id, chat_id, 'user', p_message, COALESCE(p_metadata, '{}'::jsonb), now(), p_user_id);
  
  -- Record the organisation ownership
  INSERT INTO chat_log_organisations(chat_id, organisations_id)
  VALUES (chat_id, p_org);

  -- Build the payload
  v_payload := json_build_object(
    'chat_id', chat_id,
    'user_id', p_user_id,
    'message', p_message,
    'metadata', p_metadata,
    'timestamp', now()
  )::jsonb;

  -- Use bus_notify to create the event (this will insert into bus_events and trigger webhooks)
  PERFORM bus_notify('chat_posted', p_org, v_payload);
  
  RETURN chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION post_chat(uuid, uuid, text, jsonb) TO authenticated;

-- Add comprehensive documentation
COMMENT ON FUNCTION post_chat(uuid, uuid, text, jsonb) IS 
  'DEPRECATED: Legacy RPC function for posting chat messages. 
   DO NOT USE IN NEW CODE.
   
   The frontend should publish chat_posted events directly to bus channels instead.
   This function is kept only for backward compatibility and will be removed in future.
   
   Parameters:
   - p_org: Organization ID
   - p_user_id: User ID
   - p_message: Chat message text
   - p_metadata: Optional metadata (jsonb)
   
   For new implementations, use:
   mcpPublish("chat_posted", { chat_id, user_id, message, timestamp }, orgId)';

-- Verify the fix
DO $$
DECLARE
  v_count INTEGER;
  v_functions TEXT;
BEGIN
  SELECT COUNT(*), STRING_AGG(
    format('%s(%s)', p.proname, pg_catalog.pg_get_function_identity_arguments(p.oid)), 
    ', '
  )
  INTO v_count, v_functions
  FROM pg_catalog.pg_proc p
  WHERE p.proname = 'post_chat';
  
  RAISE NOTICE 'After fix - Found % post_chat function(s): %', v_count, v_functions;
  
  IF v_count = 1 THEN
    RAISE NOTICE '✅ Success! Only one post_chat function exists now.';
  ELSE
    RAISE WARNING '⚠️  Warning: Expected 1 function but found %', v_count;
  END IF;
END $$;