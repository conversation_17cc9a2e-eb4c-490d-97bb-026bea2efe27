const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixGetLlmConfig() {
  console.log('🔧 Fixing get_llm_config function directly...\n');

  try {
    // First, let's check what functions exist
    console.log('1. Checking existing get_llm_config functions...');
    const { data: functions, error: funcError } = await supabase.rpc('query', {
      query: `
        SELECT 
          proname as function_name,
          pg_get_functiondef(oid) as definition
        FROM pg_proc 
        WHERE proname = 'get_llm_config'
        AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
      `
    }).single();

    if (funcError) {
      console.log('Could not query functions directly, proceeding with fix...');
    } else if (functions && functions.length > 0) {
      console.log('Found existing functions:', functions);
    }

    // Drop all existing versions
    console.log('\n2. Dropping existing get_llm_config functions...');
    const dropStatements = [
      'DROP FUNCTION IF EXISTS public.get_llm_config(uuid) CASCADE',
      'DROP FUNCTION IF EXISTS public.get_llm_config(text) CASCADE',
      'DROP FUNCTION IF EXISTS public.get_llm_config_v2(uuid) CASCADE'
    ];

    for (const stmt of dropStatements) {
      try {
        await supabase.rpc('query', { query: stmt });
        console.log(`✅ Executed: ${stmt}`);
      } catch (e) {
        console.log(`⚠️  Could not execute: ${stmt}`);
      }
    }

    // Create the corrected function
    console.log('\n3. Creating corrected get_llm_config function...');
    const createFunction = `
      CREATE OR REPLACE FUNCTION public.get_llm_config(p_org_id uuid)
      RETURNS TABLE(provider text, api_key text, default_model text, openai_org_id text) 
      LANGUAGE plpgsql
      SECURITY DEFINER
      SET search_path = public
      AS $$
      DECLARE
        result_row RECORD;
        decrypted_key text;
      BEGIN
        -- Get the data with proper joins (no c.default_model!)
        SELECT 
          c.provider,
          c.api_key,
          COALESCE(s.default_model, 'gpt-4') as default_model,
          c.openai_org_id
        INTO result_row
        FROM org_llm_credentials c
        LEFT JOIN org_llm_settings s 
          ON s.organisation_id = c.organisation_id 
          AND s.preferred_provider = c.provider
        WHERE c.organisation_id = p_org_id
        ORDER BY 
          CASE WHEN s.preferred_provider IS NOT NULL THEN 0 ELSE 1 END,
          c.created_at DESC
        LIMIT 1;
        
        -- If no result found, return NULL row
        IF NOT FOUND THEN
          RETURN QUERY SELECT NULL::text, NULL::text, NULL::text, NULL::text;
          RETURN;
        END IF;
        
        -- Try to decrypt if encrypted, otherwise use as-is
        BEGIN
          -- Check if decrypt_api_key function exists
          IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'decrypt_api_key') THEN
            IF length(result_row.api_key) > 100 THEN
              decrypted_key := decrypt_api_key(result_row.api_key);
            ELSE
              decrypted_key := result_row.api_key;
            END IF;
          ELSE
            decrypted_key := result_row.api_key;
          END IF;
        EXCEPTION WHEN OTHERS THEN
          decrypted_key := result_row.api_key;
        END;
        
        -- Return the result
        RETURN QUERY SELECT 
          result_row.provider,
          decrypted_key,
          result_row.default_model,
          result_row.openai_org_id;
      END;
      $$
    `;

    await supabase.rpc('query', { query: createFunction });
    console.log('✅ Created main get_llm_config function');

    // Create text variant
    console.log('\n4. Creating text parameter variant...');
    const createTextVariant = `
      CREATE OR REPLACE FUNCTION public.get_llm_config(p_org text)
      RETURNS TABLE(provider text, api_key text, default_model text, openai_org_id text) 
      LANGUAGE sql
      SECURITY DEFINER
      AS $$
        SELECT * FROM get_llm_config(p_org::uuid);
      $$
    `;

    await supabase.rpc('query', { query: createTextVariant });
    console.log('✅ Created text variant');

    // Grant permissions
    console.log('\n5. Granting permissions...');
    const grantStatements = [
      'GRANT EXECUTE ON FUNCTION public.get_llm_config(uuid) TO authenticated, service_role, anon',
      'GRANT EXECUTE ON FUNCTION public.get_llm_config(text) TO authenticated, service_role, anon'
    ];

    for (const stmt of grantStatements) {
      try {
        await supabase.rpc('query', { query: stmt });
        console.log(`✅ ${stmt}`);
      } catch (e) {
        console.log(`⚠️  Could not grant permission: ${e.message}`);
      }
    }

    // Test the function
    console.log('\n6. Testing the fixed function...');
    const { data: testResult, error: testError } = await supabase
      .rpc('get_llm_config', { p_org_id: '00000000-0000-0000-0000-000000000000' });

    if (testError) {
      console.error('❌ Function still has errors:', testError.message);
    } else {
      console.log('✅ Function works correctly!');
      console.log('   Test result:', testResult);
    }

    console.log('\n🎉 get_llm_config function has been fixed!');

  } catch (error) {
    console.error('❌ Error fixing function:', error);
    process.exit(1);
  }
}

fixGetLlmConfig();