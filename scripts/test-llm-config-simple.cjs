const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('   Please ensure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testLlmConfig() {
  console.log('🧪 Testing get_llm_config Function\n');

  try {
    // Test 1: Test with a non-existent organization (should not error)
    console.log('Test 1: Non-existent organization');
    const fakeOrgId = '00000000-0000-0000-0000-000000000000';
    
    const { data: fakeConfig, error: fakeError } = await supabase
      .rpc('get_llm_config', { p_org_id: fakeOrgId });
    
    if (fakeError) {
      if (fakeError.message.includes('c.default_model')) {
        console.error('❌ CRITICAL: The c.default_model error still exists!');
        console.error('   Error:', fakeError.message);
        return false;
      } else {
        console.error('❌ Unexpected error:', fakeError.message);
        return false;
      }
    } else {
      console.log('✅ Function executed without c.default_model error');
      console.log('   Result:', fakeConfig);
    }

    // Test 2: Get a real organization to test with
    console.log('\nTest 2: Testing with real organization data');
    const { data: orgs, error: orgsError } = await supabase
      .from('org_llm_credentials')
      .select('organisation_id, provider')
      .limit(1);
    
    if (orgsError) {
      console.log('⚠️  Could not fetch organizations:', orgsError.message);
    } else if (orgs && orgs.length > 0) {
      const testOrgId = orgs[0].organisation_id;
      console.log(`   Testing with org: ${testOrgId}`);
      
      const { data: config, error: configError } = await supabase
        .rpc('get_llm_config', { p_org_id: testOrgId });
      
      if (configError) {
        if (configError.message.includes('c.default_model')) {
          console.error('❌ CRITICAL: The c.default_model error still exists!');
          console.error('   Error:', configError.message);
          return false;
        } else {
          console.error('❌ Error:', configError.message);
          return false;
        }
      } else {
        console.log('✅ Function executed successfully');
        console.log('   Provider:', config?.provider || 'NULL');
        console.log('   Model:', config?.default_model || 'NULL');
      }
    } else {
      console.log('ℹ️  No organizations found to test with');
    }

    // Test 3: Test text-based overload
    console.log('\nTest 3: Testing text-based overload');
    const { data: textConfig, error: textError } = await supabase
      .rpc('get_llm_config', { p_org: fakeOrgId });
    
    if (textError) {
      if (textError.message.includes('c.default_model')) {
        console.error('❌ CRITICAL: The c.default_model error still exists in text overload!');
        console.error('   Error:', textError.message);
        return false;
      } else {
        console.error('❌ Error:', textError.message);
        return false;
      }
    } else {
      console.log('✅ Text overload executed successfully');
    }

    console.log('\n' + '='.repeat(60));
    console.log('✅ ALL TESTS PASSED!');
    console.log('='.repeat(60));
    console.log('\nThe get_llm_config function is working correctly.');
    console.log('The c.default_model error has been resolved.');
    
    return true;

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

// Run the test
testLlmConfig()
  .then(success => {
    if (success) {
      console.log('\n✅ Test completed successfully');
      process.exit(0);
    } else {
      console.log('\n❌ Test failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });