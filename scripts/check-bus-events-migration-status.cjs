#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to check if the bus_events migration has been applied
 * This checks for the presence of channel_id and user_id columns
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Get Supabase credentials from environment
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  if (!supabaseUrl) console.error('  - SUPABASE_URL or VITE_SUPABASE_URL');
  if (!supabaseServiceKey) console.error('  - SUPABASE_SERVICE_ROLE_KEY');
  console.log('\n📋 Manual Check Instructions:');
  console.log('1. Go to your Supabase Dashboard SQL Editor');
  console.log('2. Run the query from scripts/check-bus-events-schema.sql');
  console.log('3. Look for channel_id and user_id columns in the results');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkMigrationStatus() {
  console.log('🔍 Checking bus_events table migration status...\n');
  
  try {
    // Try to select from bus_events with the new columns
    const { data, error } = await supabase
      .from('bus_events')
      .select('id, channel, channel_id, user_id')
      .limit(1);
    
    if (error) {
      if (error.message.includes('channel_id')) {
        console.log('❌ Migration NOT applied - channel_id column is missing');
        console.log('\n📋 To apply the migration:');
        console.log('1. Go to Supabase Dashboard > SQL Editor');
        console.log('2. Run the SQL from: scripts/manual-apply-bus-events-migration.sql');
        return false;
      }
      if (error.message.includes('user_id')) {
        console.log('⚠️  Partial migration - user_id column is missing');
        console.log('   channel_id exists but user_id is missing');
        return false;
      }
      console.error('❌ Unexpected error:', error.message);
      return false;
    }
    
    console.log('✅ Migration appears to be applied!');
    console.log('   - channel column exists');
    console.log('   - channel_id column exists (alias)');
    console.log('   - user_id column exists');
    
    // Check if we can insert a test record
    console.log('\n🧪 Testing insert capability...');
    const testData = {
      event_name: 'migration_test',
      event_type: 'test',
      channel: 'test-channel',
      user_id: '00000000-0000-0000-0000-000000000000',
      organisation_id: '00000000-0000-0000-0000-000000000000',
      payload: { test: true }
    };
    
    const { error: insertError } = await supabase
      .from('bus_events')
      .insert(testData);
    
    if (insertError) {
      console.error('❌ Insert test failed:', insertError.message);
      return false;
    }
    
    // Clean up test record
    await supabase
      .from('bus_events')
      .delete()
      .eq('event_name', 'migration_test');
    
    console.log('✅ Insert test successful!');
    console.log('\n🎉 The bus_events table is properly configured for the worker service.');
    
    return true;
    
  } catch (error) {
    console.error('❌ Error checking migration status:', error.message);
    return false;
  }
}

async function main() {
  console.log('Bus Events Migration Status Check');
  console.log('=================================\n');
  
  const isApplied = await checkMigrationStatus();
  
  if (!isApplied) {
    console.log('\n⚠️  Migration needs to be applied');
    console.log('See docs/apply-bus-events-migration-guide.md for detailed instructions');
    process.exit(1);
  }
  
  console.log('\n✅ All checks passed!');
}

// Run the script
main();