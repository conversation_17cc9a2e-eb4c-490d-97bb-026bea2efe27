-- Migration 2: Update post_chat and post_agent_reply to use bus_notify
-- This MUST be applied AFTER migration-1-metadata.sql

-- Update the post_chat function to use bus_notify
CREATE OR REPLACE FUNCTION post_chat(
  p_org uuid,
  p_user_id uuid,
  p_message text,
  p_metadata jsonb DEFAULT NULL
) RETURNS uuid AS $$
DECLARE
  new_id uuid := gen_random_uuid();
  chat_id uuid := gen_random_uuid();
  v_payload jsonb;
BEGIN
  -- Insert the message into chat_log with metadata
  INSERT INTO chat_log(id, chat_id, sender, message, metadata, created_at, user_id)
  VALUES (new_id, chat_id, 'user', p_message, COALESCE(p_metadata, '{}'::jsonb), now(), p_user_id);
  
  -- Record the organisation ownership
  INSERT INTO chat_log_organisations(chat_id, organisations_id)
  VALUES (chat_id, p_org);

  -- Build the payload
  v_payload := json_build_object(
    'chat_id', chat_id,
    'user_id', p_user_id,
    'message', p_message,
    'metadata', p_metadata,
    'timestamp', now()
  )::jsonb;

  -- Use bus_notify to create the event (this will insert into bus_events and trigger webhooks)
  PERFORM bus_notify('chat_posted', p_org, v_payload);
  
  RETURN chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also update post_agent_reply to use bus_notify for consistency
CREATE OR REPLACE FUNCTION post_agent_reply(
  p_org uuid,
  p_chat_id uuid,
  p_message text,
  p_metadata jsonb DEFAULT NULL
) RETURNS void AS $$
DECLARE
  new_id uuid := gen_random_uuid();
  v_payload jsonb;
BEGIN
  -- Insert the reply into chat_log with metadata
  INSERT INTO chat_log(id, chat_id, sender, message, metadata, created_at)
  VALUES (new_id, p_chat_id, 'master_agent', p_message, COALESCE(p_metadata, '{}'::jsonb), now());

  -- Build the payload
  v_payload := json_build_object(
    'chat_id', p_chat_id,
    'message', p_message,
    'metadata', p_metadata,
    'timestamp', now()
  )::jsonb;

  -- Use bus_notify to create the event
  PERFORM bus_notify('agent_reply', p_org, v_payload);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions (maintain existing permissions)
GRANT EXECUTE ON FUNCTION post_chat(uuid, uuid, text, jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION post_agent_reply(uuid, uuid, text, jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION post_agent_reply(uuid, uuid, text, jsonb) TO service_role;

-- Add comments
COMMENT ON FUNCTION post_chat(uuid, uuid, text, jsonb) IS 
  'Posts a user chat message and creates a chat_posted event in bus_events';
  
COMMENT ON FUNCTION post_agent_reply(uuid, uuid, text, jsonb) IS 
  'Posts an agent reply and creates an agent_reply event in bus_events';