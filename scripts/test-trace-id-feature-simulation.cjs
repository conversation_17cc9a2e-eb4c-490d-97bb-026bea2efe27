const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL || 'https://umsrsnewtfwegtqowmmj.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false
  }
});

async function testTraceIdFeatureSimulation() {
  console.log('🔍 Testing trace_id feature (SIMULATION MODE)...\n');
  console.log('⚠️  Note: This is a simulation since the trace_id column doesn\'t exist yet.\n');

  try {
    // First, get an existing organization or create one
    console.log('0️⃣ Getting or creating test organization...');
    let orgId;
    
    // Try to get an existing organization
    const { data: existingOrg } = await supabase
      .from('organisations')
      .select('id')
      .limit(1)
      .single();
    
    if (existingOrg) {
      orgId = existingOrg.id;
      console.log('✅ Using existing organization:', orgId);
    } else {
      // Create a test organization
      const { data: newOrg, error: orgError } = await supabase
        .from('organisations')
        .insert({
          name: 'Test Organization for trace_id',
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (orgError) {
        console.error('❌ Failed to create test organization:', orgError);
        return;
      }
      
      orgId = newOrg.id;
      console.log('✅ Created test organization:', orgId);
    }

    // 1. Simulate creating a pipeline run with trace_id
    console.log('\n1️⃣ Simulating pipeline run creation with trace_id...');
    
    // Generate a trace_id manually (simulating what the database would do)
    const simulatedTraceId = crypto.randomUUID();
    
    const { data: pipelineRun, error: createError } = await supabase
      .from('pipeline_runs')
      .insert({
        task_id: 'test-task-' + Date.now(),
        organisation_id: orgId,
        chat_id: 'test-chat-' + Date.now(),
        status: 'initiated',
        input: { test: true, message: 'Testing trace_id feature' },
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ Failed to create pipeline run:', createError);
      return;
    }

    console.log('✅ Pipeline run created successfully');
    console.log('   ID:', pipelineRun.id);
    console.log('   Simulated Trace ID:', simulatedTraceId);

    // 2. Create a bus event with the trace_id in payload
    console.log('\n2️⃣ Creating a bus event with trace_id in payload...');
    const { data: busEvent, error: busError } = await supabase
      .from('bus_events')
      .insert({
        event_name: 'test.event',
        organisation_id: orgId,
        channel: 'test-channel',
        payload: {
          message: 'Test event for trace_id verification',
          trace_id: simulatedTraceId,
          pipeline_run_id: pipelineRun.id,
          metadata: {
            trace_id: simulatedTraceId,
            pipeline_run_id: pipelineRun.id
          }
        }
      })
      .select()
      .single();

    if (busError) {
      console.error('❌ Failed to create bus event:', busError);
      return;
    }

    console.log('✅ Bus event created with trace_id in payload');
    console.log('   Event ID:', busEvent.id);
    console.log('   Payload:', JSON.stringify(busEvent.payload, null, 2));

    // 3. Query events by trace_id
    console.log('\n3️⃣ Querying events by trace_id...');
    const { data: relatedEvents, error: queryError } = await supabase
      .from('bus_events')
      .select('*')
      .eq('payload->>trace_id', simulatedTraceId);

    if (queryError) {
      console.error('❌ Failed to query events by trace_id:', queryError);
      return;
    }

    console.log(`✅ Found ${relatedEvents.length} event(s) with trace_id: ${simulatedTraceId}`);

    // 4. Clean up test data
    console.log('\n4️⃣ Cleaning up test data...');
    await supabase.from('bus_events').delete().eq('id', busEvent.id);
    await supabase.from('pipeline_runs').delete().eq('id', pipelineRun.id);
    console.log('✅ Test data cleaned up');

    console.log('\n🎉 trace_id feature simulation completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   - The application code is ready to use trace_id');
    console.log('   - Events can be correlated using trace_id in payload');
    console.log('   - Once the migration is applied, trace_id will be automatically generated');
    console.log('\n⚠️  Next Step: Apply the migration via Supabase Dashboard to enable automatic trace_id generation');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the test
testTraceIdFeatureSimulation().catch(console.error);