const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL || 'https://umsrsnewtfwegtqowmmj.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false
  }
});

async function verifyTraceIdDeployment() {
  console.log('🔍 Verifying trace_id deployment readiness...\n');

  // Check if pipeline_runs table exists
  try {
    const { data, error } = await supabase
      .from('pipeline_runs')
      .select('id')
      .limit(1);

    if (error) {
      console.log('❌ pipeline_runs table not accessible:', error.message);
      return;
    }

    console.log('✅ pipeline_runs table exists');

    // Check if trace_id column already exists
    const { data: columns, error: columnError } = await supabase
      .rpc('get_table_columns', { table_name: 'pipeline_runs' })
      .single();

    if (!columnError && columns) {
      const hasTraceId = columns.some(col => col.column_name === 'trace_id');
      if (hasTraceId) {
        console.log('✅ trace_id column already exists!');
        console.log('🎉 The trace_id feature has already been deployed to the database.');
      } else {
        console.log('⚠️  trace_id column does not exist yet');
        console.log('📝 Database migration needs to be applied');
      }
    }
  } catch (error) {
    console.log('⚠️  Could not verify column existence');
  }

  console.log('\n📋 Deployment Summary:');
  console.log('====================');
  console.log('1. Database Migration:');
  console.log('   - Location: supabase/migrations/20250612_add_trace_id_to_pipeline_runs.sql');
  console.log('   - Status: Needs manual application via Supabase Dashboard');
  console.log('   - URL: https://supabase.com/dashboard/project/umsrsnewtfwegtqowmmj/sql/new');
  console.log('\n2. Application Code:');
  console.log('   - Updated files ready for deployment');
  console.log('   - Railway project: sequential-thinking');
  console.log('   - Services to deploy: master-agent, sequential-thinking, webhook-receiver, worker');
  console.log('\n3. Next Steps:');
  console.log('   a) Apply the database migration manually');
  console.log('   b) Deploy the application code to Railway');
  console.log('   c) Verify the feature is working by triggering a pipeline');
}

// Run verification
verifyTraceIdDeployment().catch(console.error);