require('dotenv').config();
const https = require('https');

const GITHUB_TOKEN = process.env.GITHUB_TOKEN;

if (!GITHUB_TOKEN) {
  console.error('Error: GITHUB_TOKEN not found in environment variables');
  process.exit(1);
}

console.log('Testing GitHub token...');
console.log('Token exists:', !!GITHUB_TOKEN);
console.log('Token length:', GITHUB_TOKEN.length);
console.log('Token starts with:', GITHUB_TOKEN.substring(0, 10) + '...');

// Test API call to verify token
const options = {
  hostname: 'api.github.com',
  path: '/user',
  method: 'GET',
  headers: {
    'Authorization': `token ${GITHUB_TOKEN}`,
    'Accept': 'application/vnd.github.v3+json',
    'User-Agent': 'Node.js-Test'
  }
};

const req = https.request(options, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('\nAPI Response Status:', res.statusCode);
    if (res.statusCode === 200) {
      const user = JSON.parse(data);
      console.log('✅ Token is valid!');
      console.log('Authenticated as:', user.login);
    } else {
      console.log('❌ Token is invalid or expired');
      console.log('Response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('Request error:', error);
});

req.end();