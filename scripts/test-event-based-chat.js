import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testEventBasedChat() {
  console.log('=== Testing Event-Based Chat Communication ===\n');
  
  const testOrgId = 'test-org-123';
  const testUserId = 'test-user-456';
  const testChatId = crypto.randomUUID();
  const testMessage = 'Hello from event-based test!';
  
  console.log('Test Configuration:');
  console.log(`- Organization ID: ${testOrgId}`);
  console.log(`- User ID: ${testUserId}`);
  console.log(`- Chat ID: ${testChatId}`);
  console.log(`- Message: ${testMessage}\n`);
  
  // Subscribe to agent_reply events to see the response
  const replyChannel = supabase.channel(`chat_${testChatId}`);
  let replyReceived = false;
  
  replyChannel.on('broadcast', { event: 'agent_reply' }, (payload) => {
    console.log('✅ Received agent_reply event:', payload);
    replyReceived = true;
  });
  
  await replyChannel.subscribe((status) => {
    console.log(`Reply channel subscription status: ${status}`);
  });
  
  // Wait for subscription to be ready
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Publish chat_posted event
  const channel = supabase.channel(`bus-agent-${testOrgId}`, {
    config: {
      broadcast: { ack: true, self: true }
    }
  });
  
  await channel.subscribe((status) => {
    console.log(`Bus channel subscription status: ${status}`);
  });
  
  // Wait for subscription to be ready
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('\n📤 Publishing chat_posted event...');
  
  const envelope = {
    event: 'chat_posted',
    organisation_id: testOrgId,
    timestamp: new Date().toISOString(),
    payload: {
      chat_id: testChatId,
      user_id: testUserId,
      message: testMessage,
      timestamp: new Date().toISOString()
    }
  };
  
  try {
    await channel.send({
      type: 'broadcast',
      event: 'chat_posted',
      payload: envelope
    });
    
    console.log('✅ Event published successfully\n');
    
    // Also check if event was stored in bus_events table
    console.log('📊 Checking bus_events table...');
    const { data: busEvents, error: busError } = await supabase
      .from('bus_events')
      .select('*')
      .eq('event', 'chat_posted')
      .eq('organisation_id', testOrgId)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (busError) {
      console.error('❌ Error checking bus_events:', busError);
    } else if (busEvents && busEvents.length > 0) {
      console.log('✅ Event found in bus_events table:', busEvents[0]);
    } else {
      console.log('⚠️  Event not found in bus_events table (might be using direct broadcast)');
    }
    
    // Wait for agent reply
    console.log('\n⏳ Waiting for agent reply (10 seconds)...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    if (replyReceived) {
      console.log('\n✅ Test completed successfully! Agent responded to event-based message.');
    } else {
      console.log('\n⚠️  No agent reply received. The master agent might not be running or subscribed.');
    }
    
    // Check chat_log for persistence
    console.log('\n📊 Checking chat_log table...');
    const { data: chatLogs, error: chatError } = await supabase
      .from('chat_log')
      .select('*')
      .eq('chat_id', testChatId)
      .order('created_at', { ascending: false });
    
    if (chatError) {
      console.error('❌ Error checking chat_log:', chatError);
    } else if (chatLogs && chatLogs.length > 0) {
      console.log(`✅ Found ${chatLogs.length} messages in chat_log:`);
      chatLogs.forEach(log => {
        console.log(`  - ${log.sender}: ${log.message.substring(0, 50)}...`);
      });
    } else {
      console.log('⚠️  No messages found in chat_log');
    }
    
  } catch (error) {
    console.error('❌ Error publishing event:', error);
  } finally {
    // Cleanup
    await replyChannel.unsubscribe();
    await channel.unsubscribe();
  }
}

// Run the test
testEventBasedChat().catch(console.error);