-- Manual script to apply bus_events migration
-- Run this in your Supabase SQL editor

-- Add missing columns to bus_events table required by the worker service
-- The worker expects both channel_id and user_id columns

-- Add user_id column if it doesn't exist
ALTER TABLE bus_events
ADD COLUMN IF NOT EXISTS user_id UUID;

-- Create an alias for channel as channel_id to maintain compatibility
-- This way both 'channel' and 'channel_id' will work
ALTER TABLE bus_events
ADD COLUMN IF NOT EXISTS channel_id TEXT GENERATED ALWAYS AS (channel) STORED;

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_bus_events_user_id ON bus_events(user_id);
CREATE INDEX IF NOT EXISTS idx_bus_events_channel_id ON bus_events(channel_id);

-- Add comments to document the columns
COMMENT ON COLUMN bus_events.user_id IS 'ID of the user who triggered this event';
COMMENT ON COLUMN bus_events.channel_id IS 'Alias for channel column - maintained for backward compatibility';

-- Verify the changes
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'bus_events'
ORDER BY 
    ordinal_position;