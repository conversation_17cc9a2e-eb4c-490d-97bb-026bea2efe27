# GitHub Branch Merge Instructions

## Current Status

The merge script has been created and is ready to use, but it requires a valid GitHub Personal Access Token (PAT) to function.

## What Was Done

1. ✅ Installed the `dotenv` package for loading environment variables
2. ✅ Created `scripts/merge-fix-branch.cjs` - a script that will:
   - Create a pull request from `fix/critical-event-system-issues` to `main`
   - Automatically merge the pull request
   - Delete the source branch after successful merge
3. ✅ Configured the script with the correct repository information:
   - Owner: `BillMoD`
   - Repository: `vibelaunch`

## What Needs to Be Done

### 1. Update the GitHub Token

The `.env` file currently contains a placeholder token. You need to replace it with a valid GitHub Personal Access Token.

1. Open the `.env` file
2. Replace `your-pat-here` with your actual GitHub Personal Access Token
3. Save the file

**Required Token Permissions:**
- `repo` (Full control of private repositories)
- `delete_repo` (Delete repositories) - only if you want to delete branches

### 2. Run the Merge Script

Once you have updated the token, run the merge script:

```bash
node scripts/merge-fix-branch.cjs
```

The script will:
1. Create a pull request titled "Fix: Critical Event System Issues"
2. Merge the pull request into the main branch
3. Delete the `fix/critical-event-system-issues` branch

## Alternative: Manual Merge

If you prefer to merge manually or want to review the changes first:

1. Go to https://github.com/BillMoD/vibelaunch
2. Click on "Pull requests" → "New pull request"
3. Set base branch to `main` and compare branch to `fix/critical-event-system-issues`
4. Review the changes and create the pull request
5. Merge the pull request
6. Optionally delete the branch

## Troubleshooting

If you encounter issues:

1. **Invalid Token Error**: Make sure your GitHub PAT is valid and has the necessary permissions
2. **Branch Not Found**: Ensure the `fix/critical-event-system-issues` branch exists on GitHub
3. **Merge Conflicts**: If there are conflicts, you'll need to resolve them manually on GitHub

## Testing the Token

You can test if your token is valid by running:

```bash
node scripts/test-github-token.cjs
```

This will verify that your token can authenticate with the GitHub API.