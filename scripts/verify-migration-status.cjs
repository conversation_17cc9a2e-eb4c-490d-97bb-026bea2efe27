const { createClient } = require('@supabase/supabase-js');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

async function verifyMigrationStatus() {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in .env.local');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  console.log('Verifying migration status...\n');
  
  let metadataExists = false;
  let postChatUsesBusNotify = false;
  let postAgentReplyUsesBusNotify = false;
  
  // 1. Check if metadata column exists
  try {
    const { data, error } = await supabase
      .rpc('to_regclass', { p_name: 'public.chat_log' })
      .single();
    
    if (!error && data) {
      // Check column existence using information_schema
      const { data: columns, error: colError } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'chat_log')
        .eq('column_name', 'metadata');
      
      if (!colError && columns && columns.length > 0) {
        console.log('✅ Metadata column EXISTS in chat_log table');
        metadataExists = true;
      } else {
        console.log('❌ Metadata column does NOT exist in chat_log table');
      }
    }
  } catch (err) {
    console.error('Error checking metadata column:', err.message);
  }
  
  // 2. Check post_chat function definition
  try {
    const { data, error } = await supabase.rpc('pg_get_functiondef', {
      p_oid: "(SELECT oid FROM pg_proc WHERE proname = 'post_chat' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public') LIMIT 1)::oid"
    });
    
    if (!error && data) {
      console.log('\n✅ post_chat function exists');
      
      // Check if it uses bus_notify
      if (data.includes('bus_notify')) {
        console.log('✅ post_chat is using bus_notify');
        postChatUsesBusNotify = true;
      } else if (data.includes('pg_notify')) {
        console.log('❌ post_chat is still using pg_notify');
      }
      
      // Check if it has metadata parameter
      if (data.includes('p_metadata jsonb')) {
        console.log('✅ post_chat accepts metadata parameter');
      }
    } else {
      console.log('\n❌ post_chat function not found');
    }
  } catch (err) {
    console.error('Error checking post_chat function:', err.message);
  }
  
  // 3. Check post_agent_reply function definition
  try {
    const { data, error } = await supabase.rpc('pg_get_functiondef', {
      p_oid: "(SELECT oid FROM pg_proc WHERE proname = 'post_agent_reply' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public') LIMIT 1)::oid"
    });
    
    if (!error && data) {
      console.log('\n✅ post_agent_reply function exists');
      
      // Check if it uses bus_notify
      if (data.includes('bus_notify')) {
        console.log('✅ post_agent_reply is using bus_notify');
        postAgentReplyUsesBusNotify = true;
      } else if (data.includes('pg_notify')) {
        console.log('❌ post_agent_reply is still using pg_notify');
      }
      
      // Check if it has metadata parameter
      if (data.includes('p_metadata jsonb')) {
        console.log('✅ post_agent_reply accepts metadata parameter');
      }
    } else {
      console.log('\n❌ post_agent_reply function not found');
    }
  } catch (err) {
    console.error('Error checking post_agent_reply function:', err.message);
  }
  
  // 4. Check if bus_notify function exists
  try {
    const { data, error } = await supabase.rpc('to_regprocedure', {
      p_name: 'bus_notify(text,uuid,jsonb)'
    });
    
    if (!error && data) {
      console.log('\n✅ bus_notify function exists with correct signature');
    } else {
      console.log('\n❌ bus_notify function not found or has wrong signature');
    }
  } catch (err) {
    console.error('Error checking bus_notify function:', err.message);
  }
  
  // Summary
  console.log('\n================================================================================');
  console.log('MIGRATION STATUS SUMMARY');
  console.log('================================================================================');
  
  if (metadataExists && postChatUsesBusNotify && postAgentReplyUsesBusNotify) {
    console.log('\n✅ SUCCESS: All migrations have been applied successfully!');
    console.log('\nThe post_chat and post_agent_reply functions are now:');
    console.log('- Using bus_notify instead of pg_notify');
    console.log('- Supporting metadata parameter');
    console.log('- Creating events in bus_events table for webhook processing');
  } else {
    console.log('\n⚠️  INCOMPLETE: Some migrations may not have been applied correctly.');
    console.log('\nIssues found:');
    if (!metadataExists) {
      console.log('- Metadata column is missing from chat_log table');
    }
    if (!postChatUsesBusNotify) {
      console.log('- post_chat function is not using bus_notify');
    }
    if (!postAgentReplyUsesBusNotify) {
      console.log('- post_agent_reply function is not using bus_notify');
    }
  }
  
  console.log('\n================================================================================\n');
}

verifyMigrationStatus()
  .then(() => process.exit(0))
  .catch(err => {
    console.error('Fatal error:', err);
    process.exit(1);
  });