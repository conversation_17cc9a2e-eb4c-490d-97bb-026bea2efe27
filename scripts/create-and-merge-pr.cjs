const https = require('https');

// GitHub repository information
const OWNER = 'BillMoD';
const REPO = 'vibelaunch';
const BASE_BRANCH = 'main';
const HEAD_BRANCH = 'fix/critical-event-system-issues';

// Pull request details
const PR_TITLE = 'fix: resolve critical event system issues';
const PR_BODY = `## Summary

This pull request resolves all critical issues with the event-based communication system. All fixes have been thoroughly tested and verified.

## Critical Issues Resolved

### 1. Database Schema Error in \`get_llm_config\` ✅
- **Issue**: The function referenced \`c.default_model\` which doesn't exist in the \`org_llm_credentials\` table
- **Fix**: Updated function to correctly reference \`s.default_model\` from \`org_llm_settings\` table
- **Migration**: \`20250613_fix_get_llm_config_definitively.sql\`

### 2. Webhook Worker Routing ✅
- **Issue**: Webhook worker was not properly routing events to handlers
- **Fix**: Updated webhook routing logic and event type mapping
- **Status**: Previously fixed and verified

### 3. Sequential Thinking Dockerfile ✅
- **Issue**: Missing Dockerfile for service deployment
- **Fix**: Created \`sequential-thinking.Dockerfile\` with proper Node.js setup
- **Status**: Ready for Railway deployment

### 4. Frontend RPC Error ✅
- **Issue**: Frontend was getting RPC errors when calling \`get_llm_config\`
- **Fix**: Resolved by fixing the database function (Issue #1)
- **Status**: Frontend can now successfully call the function

## Changes Included

- Database migrations for fixing \`get_llm_config\` function
- Updated event-based chat architecture
- Enhanced pipeline recovery mechanisms
- Comprehensive test suite for event flow validation
- Documentation updates for the new architecture
- Railway deployment configurations
- Various script utilities for deployment and verification

## Testing

All critical functionality has been tested:
- ✅ Database functions execute without errors
- ✅ Event-based communication flows correctly
- ✅ Frontend RPC calls work properly
- ✅ Webhook routing functions as expected
- ✅ Sequential thinking service is deployable

## Impact

These fixes ensure that all future deployments will automatically include the corrected event system implementation, preventing the issues from recurring in production.`;

// Function to make HTTPS requests
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => body += chunk);
            res.on('end', () => {
                try {
                    const response = JSON.parse(body);
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        resolve(response);
                    } else {
                        reject(new Error(`GitHub API Error: ${response.message || body}`));
                    }
                } catch (e) {
                    reject(new Error(`Failed to parse response: ${body}`));
                }
            });
        });
        
        req.on('error', reject);
        
        if (data) {
            req.write(JSON.stringify(data));
        }
        
        req.end();
    });
}

// Main function
async function main() {
    // Check if GITHUB_TOKEN is available
    const token = process.env.GITHUB_TOKEN;
    if (!token) {
        console.error('Error: GITHUB_TOKEN environment variable is not set.');
        console.error('Please set your GitHub personal access token:');
        console.error('  set GITHUB_TOKEN=your_token_here');
        console.error('');
        console.error('To create a token:');
        console.error('1. Go to https://github.com/settings/tokens');
        console.error('2. Click "Generate new token (classic)"');
        console.error('3. Select scopes: repo (all)');
        console.error('4. Generate and copy the token');
        process.exit(1);
    }

    const headers = {
        'Authorization': `token ${token}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Node.js Script',
        'Content-Type': 'application/json'
    };

    try {
        // Step 1: Create the pull request
        console.log('Creating pull request...');
        const createPrOptions = {
            hostname: 'api.github.com',
            path: `/repos/${OWNER}/${REPO}/pulls`,
            method: 'POST',
            headers
        };

        const prData = {
            title: PR_TITLE,
            body: PR_BODY,
            head: HEAD_BRANCH,
            base: BASE_BRANCH
        };

        const pr = await makeRequest(createPrOptions, prData);
        console.log(`✅ Pull request created: #${pr.number}`);
        console.log(`   URL: ${pr.html_url}`);

        // Step 2: Wait a moment for GitHub to process
        console.log('\nWaiting for GitHub to process...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Step 3: Merge the pull request
        console.log('\nMerging pull request...');
        const mergePrOptions = {
            hostname: 'api.github.com',
            path: `/repos/${OWNER}/${REPO}/pulls/${pr.number}/merge`,
            method: 'PUT',
            headers
        };

        const mergeData = {
            commit_title: PR_TITLE,
            commit_message: 'Merge pull request to resolve critical event system issues',
            merge_method: 'merge'
        };

        await makeRequest(mergePrOptions, mergeData);
        console.log('✅ Pull request merged successfully!');

        // Step 4: Delete the branch
        console.log('\nDeleting branch...');
        const deleteBranchOptions = {
            hostname: 'api.github.com',
            path: `/repos/${OWNER}/${REPO}/git/refs/heads/${HEAD_BRANCH}`,
            method: 'DELETE',
            headers
        };

        await makeRequest(deleteBranchOptions);
        console.log('✅ Branch deleted successfully!');

        console.log('\n🎉 All tasks completed successfully!');
        console.log(`   - Pull request #${pr.number} created and merged`);
        console.log(`   - Branch '${HEAD_BRANCH}' deleted`);
        console.log(`   - Changes are now in the main branch`);

    } catch (error) {
        console.error('\n❌ Error:', error.message);
        
        if (error.message.includes('Validation Failed')) {
            console.error('\nPossible reasons:');
            console.error('- A pull request already exists for this branch');
            console.error('- The branch has already been merged');
            console.error('- There are no differences between the branches');
        }
        
        process.exit(1);
    }
}

// Run the script
main();