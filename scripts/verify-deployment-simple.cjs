const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = 'https://umsrsnewtfwegtqowmmj.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtc3JzbmV3dGZ3ZWd0cW93bW1qIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczMjgyNDMsImV4cCI6MjA2MjkwNDI0M30.SgjcIrzmP3qaN89i_jPCFQgialB_4G_t4Tj0osRGbP0';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function verifyDeployment() {
  try {
    console.log('=== DEPLOYMENT VERIFICATION ===\n');
    
    // 1. Test database connectivity
    console.log('1. Testing database connectivity...');
    const { data: testQuery, error: testError } = await supabase
      .from('messages')
      .select('id')
      .limit(1);
      
    if (testError) {
      console.error('❌ Database connection failed:', testError);
      return;
    }
    console.log('✅ Database connection successful');
    
    // 2. Check if we can query pipeline_runs table
    console.log('\n2. Checking pipeline_runs table...');
    const { data: pipelines, error: pipelineError } = await supabase
      .from('pipeline_runs')
      .select('id, status, created_at')
      .order('created_at', { ascending: false })
      .limit(5);
      
    if (pipelineError) {
      console.error('❌ Error querying pipeline_runs:', pipelineError);
      if (pipelineError.code === 'PGRST203') {
        console.error('   This is the PGRST203 error - the database fix may not have been applied');
      }
    } else {
      console.log('✅ Pipeline_runs table accessible');
      console.log(`   Found ${pipelines.length} recent pipelines`);
      if (pipelines.length > 0) {
        console.log('   Most recent pipeline:', {
          id: pipelines[0].id,
          status: pipelines[0].status,
          created_at: pipelines[0].created_at
        });
      }
    }
    
    // 3. Check bus_events table
    console.log('\n3. Checking bus_events table...');
    const { data: busEvents, error: busError } = await supabase
      .from('bus_events')
      .select('id, type, created_at')
      .eq('type', 'chat_posted')
      .order('created_at', { ascending: false })
      .limit(5);
      
    if (busError) {
      console.error('❌ Error querying bus_events:', busError);
    } else {
      console.log('✅ Bus_events table accessible');
      console.log(`   Found ${busEvents.length} recent chat_posted events`);
      if (busEvents.length > 0) {
        console.log('   Most recent event:', {
          id: busEvents[0].id,
          type: busEvents[0].type,
          created_at: busEvents[0].created_at
        });
      }
    }
    
    // 4. Test creating a bus event directly
    console.log('\n4. Testing bus event creation...');
    const testEvent = {
      type: 'deployment_verification',
      channel_id: '00000000-0000-0000-0000-000000000000', // dummy UUID
      user_id: '00000000-0000-0000-0000-000000000000', // dummy UUID
      payload: {
        test: true,
        timestamp: new Date().toISOString(),
        purpose: 'Verifying deployment functionality'
      }
    };
    
    const { data: newEvent, error: createError } = await supabase
      .from('bus_events')
      .insert(testEvent)
      .select()
      .single();
      
    if (createError) {
      console.error('❌ Error creating bus event:', createError);
      if (createError.code === 'PGRST203') {
        console.error('   PGRST203 error detected - database fix needed');
      }
    } else {
      console.log('✅ Bus event created successfully');
      console.log('   Event ID:', newEvent.id);
      
      // Clean up test event
      await supabase
        .from('bus_events')
        .delete()
        .eq('id', newEvent.id);
    }
    
    console.log('\n=== VERIFICATION SUMMARY ===');
    console.log('\nFRONTEND:');
    console.log('- Deployed at: https://vibelaunch4-ui.netlify.app');
    console.log('- Database connectivity: ✅');
    
    console.log('\nBACKEND:');
    console.log('- Master agent deployed on Railway: Check Railway dashboard');
    console.log('- Database tables accessible: ' + (pipelineError ? '❌' : '✅'));
    console.log('- Bus events working: ' + (createError ? '❌' : '✅'));
    
    if (pipelineError?.code === 'PGRST203' || createError?.code === 'PGRST203') {
      console.log('\n⚠️  PGRST203 ERROR DETECTED');
      console.log('The database fix has been manually applied by the user.');
      console.log('If you\'re still seeing this error, the fix may need to be reapplied.');
    }
    
    console.log('\nNEXT STEPS:');
    console.log('1. Check Railway logs for master-agent service');
    console.log('2. Try sending a message through the UI');
    console.log('3. Monitor the pipeline_runs table for new entries');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the verification
verifyDeployment();