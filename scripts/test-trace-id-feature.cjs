const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL || 'https://umsrsnewtfwegtqowmmj.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false
  }
});

async function testTraceIdFeature() {
  console.log('🔍 Testing trace_id feature...\n');

  try {
    // First, get an existing organization or create one
    console.log('0️⃣ Getting or creating test organization...');
    let orgId;
    
    // Try to get an existing organization
    const { data: existingOrg } = await supabase
      .from('organisations')
      .select('id')
      .limit(1)
      .single();
    
    if (existingOrg) {
      orgId = existingOrg.id;
      console.log('✅ Using existing organization:', orgId);
    } else {
      // Create a test organization
      const { data: newOrg, error: orgError } = await supabase
        .from('organisations')
        .insert({
          name: 'Test Organization for trace_id',
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (orgError) {
        console.error('❌ Failed to create test organization:', orgError);
        return;
      }
      
      orgId = newOrg.id;
      console.log('✅ Created test organization:', orgId);
    }

    // 1. Create a test pipeline run
    console.log('\n1️⃣ Creating a new pipeline run...');
    const { data: pipelineRun, error: createError } = await supabase
      .from('pipeline_runs')
      .insert({
        task_id: 'test-task-' + Date.now(),
        organisation_id: orgId,
        chat_id: 'test-chat-' + Date.now(),
        status: 'initiated',
        input: { test: true, message: 'Testing trace_id feature' },
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ Failed to create pipeline run:', createError);
      return;
    }

    console.log('✅ Pipeline run created successfully');
    console.log('   ID:', pipelineRun.id);
    console.log('   Trace ID:', pipelineRun.trace_id);

    if (!pipelineRun.trace_id) {
      console.error('❌ trace_id was not generated!');
      console.log('⚠️  This suggests the database migration may not have been applied.');
      return;
    }

    // 2. Verify trace_id format (should be a valid UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(pipelineRun.trace_id)) {
      console.error('❌ trace_id is not a valid UUID format:', pipelineRun.trace_id);
      return;
    }

    console.log('✅ trace_id is a valid UUID');

    // 3. Create a bus event with the trace_id
    console.log('\n2️⃣ Creating a bus event with trace_id...');
    const { data: busEvent, error: busError } = await supabase
      .from('bus_events')
      .insert({
        channel: 'test-channel',
        event: 'test.event',
        payload: {
          message: 'Test event for trace_id verification',
          trace_id: pipelineRun.trace_id
        },
        metadata: {
          trace_id: pipelineRun.trace_id,
          pipeline_run_id: pipelineRun.id
        }
      })
      .select()
      .single();

    if (busError) {
      console.error('❌ Failed to create bus event:', busError);
      return;
    }

    console.log('✅ Bus event created with trace_id in metadata');
    console.log('   Event ID:', busEvent.id);
    console.log('   Metadata:', JSON.stringify(busEvent.metadata, null, 2));

    // 4. Query events by trace_id
    console.log('\n3️⃣ Querying events by trace_id...');
    const { data: relatedEvents, error: queryError } = await supabase
      .from('bus_events')
      .select('*')
      .eq('metadata->>trace_id', pipelineRun.trace_id);

    if (queryError) {
      console.error('❌ Failed to query events by trace_id:', queryError);
      return;
    }

    console.log(`✅ Found ${relatedEvents.length} event(s) with trace_id: ${pipelineRun.trace_id}`);

    // 5. Clean up test data
    console.log('\n4️⃣ Cleaning up test data...');
    await supabase.from('bus_events').delete().eq('id', busEvent.id);
    await supabase.from('pipeline_runs').delete().eq('id', pipelineRun.id);
    console.log('✅ Test data cleaned up');

    console.log('\n🎉 trace_id feature is working correctly!');
    console.log('   - trace_id is automatically generated for new pipeline runs');
    console.log('   - trace_id can be propagated through event metadata');
    console.log('   - Events can be queried by trace_id for correlation');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the test
testTraceIdFeature().catch(console.error);