require('dotenv').config();
const https = require('https');

// Configuration
const GITHUB_TOKEN = process.env.GITHUB_TOKEN;
const OWNER = 'BillMoD'; // GitHub username
const REPO = 'vibelaunch'; // Repository name
const SOURCE_BRANCH = 'fix/critical-event-system-issues';
const TARGET_BRANCH = 'main';

if (!GITHUB_TOKEN) {
  console.error('Error: GITHUB_TOKEN not found in environment variables');
  process.exit(1);
}

// Helper function to make GitHub API requests
function makeGitHubRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.github.com',
      path: `/repos/${OWNER}/${REPO}${path}`,
      method: method,
      headers: {
        'Authorization': `token ${GITHUB_TOKEN}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Node.js-Merge-Script',
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            resolve(responseData ? JSON.parse(responseData) : null);
          } catch (e) {
            resolve(responseData);
          }
        } else {
          reject(new Error(`GitHub API error: ${res.statusCode} - ${responseData}`));
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function createPullRequest() {
  console.log(`Creating pull request from ${SOURCE_BRANCH} to ${TARGET_BRANCH}...`);
  
  const prData = {
    title: 'Fix: Critical Event System Issues',
    body: `This PR merges the fixes for the critical event system issues.

## Changes included:
- Fixed event-based communication system
- Resolved authentication issues
- Updated database migrations
- Fixed various system integration issues

This branch contains all the recent fixes that have been tested and verified.`,
    head: SOURCE_BRANCH,
    base: TARGET_BRANCH
  };

  try {
    const pr = await makeGitHubRequest('POST', '/pulls', prData);
    console.log(`Pull request #${pr.number} created successfully!`);
    return pr;
  } catch (error) {
    console.error('Error creating pull request:', error.message);
    throw error;
  }
}

async function mergePullRequest(prNumber) {
  console.log(`Merging pull request #${prNumber}...`);
  
  const mergeData = {
    commit_title: `Merge pull request #${prNumber} from ${SOURCE_BRANCH}`,
    commit_message: 'Merging critical event system fixes into main branch',
    merge_method: 'merge' // Can be 'merge', 'squash', or 'rebase'
  };

  try {
    await makeGitHubRequest('PUT', `/pulls/${prNumber}/merge`, mergeData);
    console.log(`Pull request #${prNumber} merged successfully!`);
  } catch (error) {
    console.error('Error merging pull request:', error.message);
    throw error;
  }
}

async function deleteBranch() {
  console.log(`Deleting branch ${SOURCE_BRANCH}...`);
  
  try {
    await makeGitHubRequest('DELETE', `/git/refs/heads/${SOURCE_BRANCH}`);
    console.log(`Branch ${SOURCE_BRANCH} deleted successfully!`);
  } catch (error) {
    console.error('Error deleting branch:', error.message);
    // Don't throw here as the merge was successful
  }
}

async function main() {
  try {
    console.log('Starting merge process...');
    console.log(`Repository: ${OWNER}/${REPO}`);
    console.log(`Source branch: ${SOURCE_BRANCH}`);
    console.log(`Target branch: ${TARGET_BRANCH}`);
    console.log('');

    // Step 1: Create pull request
    const pr = await createPullRequest();
    
    // Step 2: Merge pull request
    await mergePullRequest(pr.number);
    
    // Step 3: Delete source branch
    await deleteBranch();
    
    console.log('\n✅ Merge process completed successfully!');
    console.log(`The ${SOURCE_BRANCH} branch has been merged into ${TARGET_BRANCH}.`);
  } catch (error) {
    console.error('\n❌ Merge process failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();