-- Create Library Storage Bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'library-assets',
  'library-assets', 
  false,
  52428800,
  ARRAY[
    'audio/mpeg',
    'audio/wav',
    'audio/ogg',
    'audio/mp3',
    'audio/flac',
    'audio/aac',
    'audio/webm',
    'audio/x-m4a',
    'application/octet-stream'
  ]::text[]
)
ON CONFLICT (id) DO UPDATE
SET 
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;