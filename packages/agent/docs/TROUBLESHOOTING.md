# Master Agent Troubleshooting Guide
## Practical Solutions for Common Issues

> **Purpose**: This guide provides step-by-step solutions for common issues senior developers encounter when working with the Master Agent package. Based on real implementation experience, not theoretical problems.

## 🚨 Critical System Issues

### 1. Master Agent Not Starting

**Symptoms**:
- Service fails to start
- Port binding errors
- Database connection failures

**Diagnostic Steps**:
```bash
# Check if port is already in use
lsof -i :8090

# Check environment variables
echo $SUPABASE_URL
echo $SUPABASE_SERVICE_ROLE_KEY
echo $ENCRYPTION_KEY

# Check logs for startup errors
DEBUG=vibelaunch:* npm run dev
```

**Common Causes & Solutions**:

#### Missing Environment Variables
```bash
# Required environment variables checklist
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...
ENCRYPTION_KEY=your-32-character-encryption-key-here!!
DEFAULT_ORG_ID=00000000-0000-0000-0000-000000000000
NODE_ENV=development
PORT=8090
```

#### Port Conflicts
```bash
# Kill process using port 8090
kill -9 $(lsof -t -i:8090)

# Use different port
PORT=8091 npm run dev
```

#### Database Connection Issues
```typescript
// Test database connection
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const { data, error } = await supabase.from('profiles').select('count');
console.log('DB Test:', { data, error });
```

### 2. Sequential Thinking Infinite Loops

**Symptoms**:
- High CPU usage
- Memory leaks
- Unresponsive service
- Endless thought generation

**Diagnostic Steps**:
```bash
# Monitor service resources
top -p $(pgrep -f "master-agent")

# Check thought generation logs
DEBUG=vibelaunch:sequential-thinking npm run dev

# Check database for runaway thoughts
psql $DATABASE_URL -c "
  SELECT pipeline_run_id, COUNT(*) as thought_count 
  FROM thoughts 
  WHERE created_at > NOW() - INTERVAL '1 hour' 
  GROUP BY pipeline_run_id 
  ORDER BY thought_count DESC;
"
```

**Solutions**:

#### Thought Limit Configuration
```typescript
// packages/agent/src/sequential-thinking-fixed.ts
const MAX_THOUGHTS = 10; // Ensure this is set appropriately

// Add additional safety checks
class SequentialThinkingService {
  async processThinkingRequest(request: ThinkingRequest): Promise<void> {
    let thoughtCount = 0;
    const startTime = Date.now();
    const MAX_EXECUTION_TIME = 300000; // 5 minutes
    
    while (needsMoreThinking && thoughtCount < MAX_THOUGHTS) {
      // Safety: Check execution time
      if (Date.now() - startTime > MAX_EXECUTION_TIME) {
        console.warn('Sequential thinking timeout reached');
        break;
      }
      
      // Generate thought with timeout
      const thought = await Promise.race([
        this.generateThought(context, thoughtCount + 1),
        this.timeoutPromise(60000) // 1 minute per thought
      ]);
      
      thoughtCount++;
    }
  }
  
  private timeoutPromise(ms: number): Promise<never> {
    return new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Thought generation timeout')), ms)
    );
  }
}
```

#### Memory Management
```typescript
// Clear thought context periodically
class SequentialThinkingService {
  private thoughtCache = new Map<string, ThoughtContext>();
  
  async cleanupOldThoughts(): Promise<void> {
    const cutoff = Date.now() - (60 * 60 * 1000); // 1 hour
    
    for (const [runId, context] of this.thoughtCache.entries()) {
      if (context.createdAt < cutoff) {
        this.thoughtCache.delete(runId);
      }
    }
  }
}
```

### 3. Agent Registry Empty

**Symptoms**:
- No agents available for contracts
- "No suitable agents found" errors
- Empty agent list in UI

**Diagnostic Steps**:
```sql
-- Check agent_registry table
SELECT * FROM agent_registry WHERE status = 'active';

-- Check agent performance data
SELECT * FROM agent_performance ORDER BY created_at DESC LIMIT 10;

-- Check for RLS policy issues
SELECT * FROM agent_registry; -- This might fail if RLS is blocking
```

**Solutions**:

#### Populate Agent Registry
```typescript
// packages/agent/src/scripts/populate-agents.ts
async function populateAgentRegistry() {
  const agents = [
    {
      id: 'content-creator-pro',
      name: 'Content Creator Pro',
      role: 'content_creator',
      capabilities: ['content_creation', 'copywriting'],
      status: 'active',
      webhook_url: null,
      description: 'Expert in creating engaging content',
      metadata: {
        specialties: ['blog_posts', 'articles', 'newsletters'],
        languages: ['english'],
        tone_preferences: ['professional', 'casual', 'technical']
      }
    },
    // ... more agents
  ];
  
  for (const agent of agents) {
    await supabase.from('agent_registry').upsert(agent);
  }
}
```

#### Check RLS Policies
```sql
-- Verify RLS policies allow agent access
SELECT * FROM pg_policies WHERE tablename = 'agent_registry';

-- Test with service role (should work)
SET ROLE service_role;
SELECT * FROM agent_registry;
```

### 4. MCP Connection Failures

**Symptoms**:
- Cross-service communication failures
- Sequential thinking not responding
- Event publishing failures

**Diagnostic Steps**:
```typescript
// Test MCP connectivity
class MCPDiagnostics {
  async testMCPConnection(): Promise<void> {
    try {
      await this.mcpConnector.sendMessage('sequential-thinking', {
        jsonrpc: '2.0',
        method: 'health_check',
        id: 'test-123'
      });
      console.log('MCP connection successful');
    } catch (error) {
      console.error('MCP connection failed:', error);
    }
  }
}
```

**Solutions**:

#### Service Discovery Issues
```typescript
// Check service registration
class ServiceRegistry {
  async registerService(serviceName: string, endpoint: string): Promise<void> {
    await this.database.services.upsert({
      name: serviceName,
      endpoint,
      status: 'active',
      last_heartbeat: new Date().toISOString()
    });
  }
  
  async discoverService(serviceName: string): Promise<string | null> {
    const service = await this.database.services
      .select('endpoint')
      .eq('name', serviceName)
      .eq('status', 'active')
      .single();
    
    return service?.endpoint || null;
  }
}
```

## ⚡ Performance Issues

### 1. Slow Response Times

**Symptoms**:
- API responses taking > 5 seconds
- UI showing loading states for extended periods
- Timeout errors

**Diagnostic Steps**:
```typescript
// Add performance monitoring
class PerformanceMonitor {
  async measureEndpoint(endpointName: string, operation: () => Promise<any>): Promise<any> {
    const startTime = Date.now();
    
    try {
      const result = await operation();
      const duration = Date.now() - startTime;
      
      console.log(`${endpointName} completed in ${duration}ms`);
      
      if (duration > 5000) {
        console.warn(`Slow operation detected: ${endpointName} took ${duration}ms`);
      }
      
      return result;
    } catch (error) {
      console.error(`${endpointName} failed after ${Date.now() - startTime}ms:`, error);
      throw error;
    }
  }
}
```

**Solutions**:

#### Database Query Optimization
```typescript
// Add database connection pooling
class DatabaseManager {
  private pool: Pool;
  
  constructor() {
    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      max: 20, // Maximum pool size
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });
  }
  
  async query(text: string, params?: any[]): Promise<any> {
    const client = await this.pool.connect();
    try {
      const start = Date.now();
      const result = await client.query(text, params);
      const duration = Date.now() - start;
      
      if (duration > 1000) {
        console.warn(`Slow query (${duration}ms):`, text);
      }
      
      return result;
    } finally {
      client.release();
    }
  }
}
```

#### Caching Implementation
```typescript
// Add Redis caching for frequent queries
class CacheManager {
  private redis: Redis;
  
  async cacheAgentRegistry(): Promise<void> {
    const agents = await this.database.agent_registry.select('*');
    await this.redis.setex('agent_registry', 300, JSON.stringify(agents)); // 5 minutes
  }
  
  async getCachedAgents(): Promise<Agent[] | null> {
    const cached = await this.redis.get('agent_registry');
    return cached ? JSON.parse(cached) : null;
  }
}
```

### 2. Memory Leaks

**Symptoms**:
- Increasing memory usage over time
- Service crashes with out-of-memory errors
- Slow garbage collection

**Diagnostic Steps**:
```bash
# Monitor memory usage
node --inspect packages/agent/src/index.ts

# Check for memory leaks
node --trace-gc packages/agent/src/index.ts

# Use heap profiling
node --heap-prof packages/agent/src/index.ts
```

**Solutions**:

#### Event Listener Cleanup
```typescript
class MasterAgent {
  private activeSubscriptions = new Set<() => void>();
  
  async start(): Promise<void> {
    // Properly clean up event listeners
    const unsubscribe = this.busManager.subscribe('chat_posted', this.handleChatMessage);
    this.activeSubscriptions.add(unsubscribe);
  }
  
  async shutdown(): Promise<void> {
    // Clean up all subscriptions
    for (const unsubscribe of this.activeSubscriptions) {
      unsubscribe();
    }
    this.activeSubscriptions.clear();
  }
}
```

## 🔐 Security Issues

### 1. Authentication Failures

**Symptoms**:
- Valid users getting 401 errors
- Token validation failures
- Inconsistent authentication behavior

**Diagnostic Steps**:
```typescript
// Test token validation
class AuthDiagnostics {
  async testTokenValidation(token: string): Promise<void> {
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser(token);
      
      if (error) {
        console.error('Token validation failed:', error);
        return;
      }
      
      console.log('Token valid for user:', user.email);
      
      // Test profile lookup
      const { data: profile } = await this.supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
        
      console.log('Profile:', profile);
    } catch (error) {
      console.error('Auth test failed:', error);
    }
  }
}
```

**Solutions**:

#### Token Cache Issues
```typescript
// Clear auth cache if needed
class AuthCache {
  async clearUserCache(userId: string): Promise<void> {
    await this.redis.del(`auth:${userId}`);
    console.log(`Cleared auth cache for user ${userId}`);
  }
  
  async clearAllAuthCache(): Promise<void> {
    const keys = await this.redis.keys('auth:*');
    if (keys.length > 0) {
      await this.redis.del(...keys);
      console.log(`Cleared ${keys.length} auth cache entries`);
    }
  }
}
```

### 2. Encryption Failures

**Symptoms**:
- API keys cannot be decrypted
- "Invalid encryption key" errors
- LLM configuration failures

**Diagnostic Steps**:
```typescript
// Test encryption/decryption
class EncryptionDiagnostics {
  async testEncryption(): Promise<void> {
    const testData = 'test-api-key-12345';
    const orgId = 'test-org';
    
    try {
      // Test encryption
      const encrypted = await this.encryption.encryptApiKey(testData, orgId);
      console.log('Encryption successful:', encrypted);
      
      // Test decryption
      const decrypted = await this.encryption.decryptApiKey(encrypted);
      console.log('Decryption successful:', decrypted === testData);
    } catch (error) {
      console.error('Encryption test failed:', error);
    }
  }
}
```

**Solutions**:

#### Encryption Key Issues
```bash
# Verify encryption key length
echo ${#ENCRYPTION_KEY}  # Should be >= 32

# Generate new encryption key if needed
openssl rand -base64 32
```

## 🔄 Integration Issues

### 1. UI Not Receiving Real-time Updates

**Symptoms**:
- UI shows stale data
- Real-time subscriptions not working
- Events not appearing in browser

**Diagnostic Steps**:
```typescript
// Test event publishing
class EventDiagnostics {
  async testEventPublishing(): Promise<void> {
    const testEvent = {
      event: 'test_event',
      organisation_id: 'test-org',
      timestamp: new Date().toISOString(),
      payload: { message: 'test' }
    };
    
    try {
      await this.busManager.publish('test', testEvent);
      console.log('Event published successfully');
    } catch (error) {
      console.error('Event publishing failed:', error);
    }
  }
}
```

**Solutions**:

#### WebSocket Connection Issues
```typescript
// Check WebSocket health
class WebSocketDiagnostics {
  async checkWebSocketHealth(): Promise<void> {
    const ws = new WebSocket('ws://localhost:8090');
    
    ws.on('open', () => {
      console.log('WebSocket connection successful');
      ws.close();
    });
    
    ws.on('error', (error) => {
      console.error('WebSocket connection failed:', error);
    });
  }
}
```

## 📋 Debugging Checklists

### Startup Issues Checklist
- [ ] All environment variables set
- [ ] Database accessible
- [ ] Port not in use
- [ ] Encryption key valid
- [ ] Dependencies installed

### Performance Issues Checklist
- [ ] Check database connection pool
- [ ] Monitor memory usage
- [ ] Check for memory leaks
- [ ] Review slow queries
- [ ] Verify caching working

### Security Issues Checklist
- [ ] Test token validation
- [ ] Verify encryption/decryption
- [ ] Check RLS policies
- [ ] Test rate limiting
- [ ] Verify input validation

### Integration Issues Checklist
- [ ] Test event publishing
- [ ] Check WebSocket connections
- [ ] Verify service discovery
- [ ] Test cross-service communication
- [ ] Check real-time subscriptions

## 🛠️ Debugging Tools and Commands

### Essential Debug Commands
```bash
# Start with full debugging
DEBUG=vibelaunch:* npm run dev

# Specific component debugging
DEBUG=vibelaunch:master-agent npm run dev
DEBUG=vibelaunch:agent-registry npm run dev
DEBUG=vibelaunch:sequential-thinking npm run dev

# Database debugging
DEBUG=vibelaunch:database npm run dev

# Security debugging
DEBUG=vibelaunch:security npm run dev
```

### Health Check Scripts
```typescript
// packages/agent/src/scripts/health-check.ts
async function healthCheck(): Promise<void> {
  console.log('🔍 Running Master Agent Health Check...\n');
  
  // Database connectivity
  try {
    await testDatabaseConnection();
    console.log('✅ Database connection: OK');
  } catch (error) {
    console.log('❌ Database connection: FAILED -', error.message);
  }
  
  // Agent registry
  try {
    const agents = await getActiveAgents();
    console.log(`✅ Agent registry: OK (${agents.length} active agents)`);
  } catch (error) {
    console.log('❌ Agent registry: FAILED -', error.message);
  }
  
  // Sequential thinking
  try {
    await testSequentialThinking();
    console.log('✅ Sequential thinking: OK');
  } catch (error) {
    console.log('❌ Sequential thinking: FAILED -', error.message);
  }
  
  // Security services
  try {
    await testSecurityServices();
    console.log('✅ Security services: OK');
  } catch (error) {
    console.log('❌ Security services: FAILED -', error.message);
  }
}
```

### Log Analysis Tools
```bash
# Search for specific errors
grep -n "ERROR" logs/master-agent.log | tail -20

# Monitor real-time logs
tail -f logs/master-agent.log | grep -E "(ERROR|WARN|sequential-thinking)"

# Check authentication issues
grep -n "Authentication\|401\|403" logs/master-agent.log
```

This troubleshooting guide provides practical, step-by-step solutions based on real implementation challenges. It focuses on the most common issues that senior developers encounter when working with the Master Agent package in practice.