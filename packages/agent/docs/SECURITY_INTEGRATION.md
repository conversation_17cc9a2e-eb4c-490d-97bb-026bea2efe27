# Security Service Integration Guide
## Complete Implementation Patterns for VibeLaunch Security Stack

> **Purpose**: This guide provides practical implementation patterns for integrating VibeLaunch's security services. While high-level security best practices are documented elsewhere, this focuses on the actual code patterns and integration strategies used in the Master Agent package.

## 🔐 Security Architecture Overview

VibeLaunch implements a layered security approach with four core services that work together:

```mermaid
graph TB
    Request[Incoming Request] --> RL[Rate Limiter]
    RL --> Auth[Authentication]
    Auth --> Val[Input Validation]
    Val --> Enc[Encryption Service]
    Enc --> BL[Business Logic]
    
    RL --> Logs[Security Logs]
    Auth --> Logs
    Val --> Logs
    Enc --> Logs
    
    Logs --> Monitor[Monitoring]
```

## 🚨 Security Service Integration Patterns

### 1. Request Security Pipeline

Every request flows through a standardized security pipeline:

```typescript
// packages/agent/src/security/security-pipeline.ts
class SecurityPipeline {
  constructor(
    private rateLimiter: RateLimiterService,
    private authenticator: AuthService,
    private validator: InputValidationService,
    private encryption: EncryptionService
  ) {}

  async processRequest(request: IncomingRequest): Promise<SecureRequest> {
    try {
      // Step 1: Rate limiting (fail fast)
      await this.rateLimiter.checkLimits(request.ip, request.endpoint);
      
      // Step 2: Authentication
      const user = await this.authenticator.validateAndExtract(request.headers);
      
      // Step 3: Input validation and sanitization
      const cleanData = await this.validator.validateAndSanitize(request.body, request.schema);
      
      // Step 4: Decrypt sensitive fields if present
      const processedData = await this.encryption.decryptSensitiveFields(cleanData, user.organization_id);
      
      return {
        user,
        data: processedData,
        metadata: {
          ip: request.ip,
          userAgent: request.userAgent,
          timestamp: new Date().toISOString(),
          securityPassed: true
        }
      };
    } catch (error) {
      await this.logSecurityEvent('request_blocked', {
        ip: request.ip,
        endpoint: request.endpoint,
        reason: error.message,
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }
}
```

### 2. Rate Limiting Implementation

**Real Implementation Pattern**:
```typescript
// packages/agent/src/security/rate-limiter.ts
import { RateLimiterRedis } from 'rate-limiter-flexible';

class RateLimiterService {
  private limiters: Map<string, RateLimiterRedis>;
  
  constructor() {
    // Different limits for different endpoints
    this.limiters.set('chat', new RateLimiterRedis({
      keyPrefix: 'rl_chat',
      points: 30, // 30 requests
      duration: 60, // per minute
      blockDuration: 300, // block for 5 minutes
    }));
    
    this.limiters.set('contracts', new RateLimiterRedis({
      keyPrefix: 'rl_contracts',
      points: 100, // 100 requests
      duration: 3600, // per hour
      blockDuration: 3600, // block for 1 hour
    }));
  }
  
  async checkLimits(identifier: string, endpoint: string): Promise<void> {
    const limiter = this.limiters.get(endpoint) || this.limiters.get('default');
    
    try {
      await limiter.consume(identifier);
    } catch (rejRes) {
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      throw new RateLimitError(`Rate limit exceeded. Try again in ${secs} seconds`);
    }
  }
  
  // Integration with Master Agent
  async checkUserLimits(userId: string, organizationId: string, endpoint: string): Promise<void> {
    // Check both user and organization limits
    await Promise.all([
      this.checkLimits(`user:${userId}`, endpoint),
      this.checkLimits(`org:${organizationId}`, endpoint)
    ]);
  }
}

// Usage in Master Agent
class MasterAgent {
  async processMessage(request: ChatRequest): Promise<void> {
    // Rate limiting is first line of defense
    await this.rateLimiter.checkUserLimits(
      request.user_id,
      request.organization_id,
      'chat'
    );
    
    // Continue with message processing...
  }
}
```

### 3. Authentication and Authorization

**JWT Token Validation Pattern**:
```typescript
// packages/agent/src/security/auth-service.ts
class AuthService {
  constructor(
    private supabaseClient: SupabaseClient,
    private cache: AuthCache
  ) {}
  
  async validateAndExtract(headers: Headers): Promise<AuthenticatedUser> {
    const authHeader = headers.authorization;
    if (!authHeader?.startsWith('Bearer ')) {
      throw new AuthenticationError('Missing or invalid authorization header');
    }
    
    const token = authHeader.substring(7);
    
    // Check cache first (5-minute TTL)
    const cachedUser = await this.cache.get(token);
    if (cachedUser) {
      return cachedUser;
    }
    
    // Validate with Supabase
    const { data: { user }, error } = await this.supabaseClient.auth.getUser(token);
    
    if (error || !user) {
      throw new AuthenticationError('Invalid or expired token');
    }
    
    // Get organization context
    const { data: profile } = await this.supabaseClient
      .from('profiles')
      .select('organization_id, role')
      .eq('id', user.id)
      .single();
    
    if (!profile) {
      throw new AuthenticationError('User profile not found');
    }
    
    const authenticatedUser: AuthenticatedUser = {
      id: user.id,
      email: user.email!,
      organization_id: profile.organization_id,
      role: profile.role,
      token
    };
    
    // Cache for future requests
    await this.cache.set(token, authenticatedUser, 300); // 5 minutes
    
    return authenticatedUser;
  }
  
  // Authorization check helper
  hasPermission(user: AuthenticatedUser, resource: string, action: string): boolean {
    const permissions = ROLE_PERMISSIONS[user.role] || [];
    return permissions.includes(`${resource}:${action}`) || permissions.includes('*:*');
  }
}

// Role-based permissions
const ROLE_PERMISSIONS = {
  owner: ['*:*'], // Full access
  admin: ['contracts:*', 'agents:read', 'analytics:read'],
  member: ['contracts:create', 'contracts:read', 'agents:read'],
  guest: ['contracts:read', 'agents:read']
};
```

### 4. Input Validation with Zod

**Comprehensive Validation Pattern**:
```typescript
// packages/agent/src/security/input-validation.ts
import { z } from 'zod';

class InputValidationService {
  // Chat message validation
  private chatMessageSchema = z.object({
    message: z.string()
      .min(1, 'Message cannot be empty')
      .max(5000, 'Message too long')
      .refine(msg => !this.containsMaliciousContent(msg), 'Invalid content'),
    user_id: z.string().uuid('Invalid user ID'),
    organization_id: z.string().uuid('Invalid organization ID'),
    metadata: z.object({
      source: z.enum(['web_ui', 'api', 'mobile']),
      session_id: z.string().optional()
    }).optional()
  });
  
  // Contract creation validation
  private contractSchema = z.object({
    title: z.string().min(5).max(200),
    description: z.string().min(20).max(5000),
    budget: z.number().positive().max(1000000),
    deadline: z.string().datetime(),
    requirements: z.record(z.unknown()).optional()
  });
  
  async validateAndSanitize<T>(data: unknown, schema: z.ZodSchema<T>): Promise<T> {
    try {
      // Parse and validate
      const validatedData = schema.parse(data);
      
      // Additional sanitization
      return this.sanitizeObject(validatedData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map(e => `${e.path.join('.')}: ${e.message}`);
        throw new ValidationError(`Validation failed: ${errorMessages.join(', ')}`);
      }
      throw error;
    }
  }
  
  private sanitizeObject<T>(obj: T): T {
    if (typeof obj === 'string') {
      return this.sanitizeString(obj) as T;
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item)) as T;
    }
    
    if (typeof obj === 'object' && obj !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = this.sanitizeObject(value);
      }
      return sanitized;
    }
    
    return obj;
  }
  
  private sanitizeString(str: string): string {
    return str
      .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocols
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }
  
  private containsMaliciousContent(message: string): boolean {
    const maliciousPatterns = [
      /<script/i,
      /javascript:/i,
      /data:text\/html/i,
      /vbscript:/i,
      /<iframe/i,
      /<object/i,
      /<embed/i
    ];
    
    return maliciousPatterns.some(pattern => pattern.test(message));
  }
}

// Usage in Master Agent
class MasterAgent {
  async processMessage(rawRequest: unknown): Promise<void> {
    // Validate input first
    const validatedRequest = await this.validator.validateAndSanitize(
      rawRequest,
      this.chatMessageSchema
    );
    
    // Now safe to process
    await this.handleValidatedMessage(validatedRequest);
  }
}
```

### 5. Encryption Service Integration

**API Key Encryption Pattern**:
```typescript
// packages/agent/src/security/encryption.ts
import crypto from 'crypto';

class EncryptionService {
  private algorithm = 'aes-256-gcm';
  private keyLength = 32;
  
  constructor(private masterKey: string) {
    if (!masterKey || masterKey.length < 32) {
      throw new Error('Master encryption key must be at least 32 characters');
    }
  }
  
  // Encrypt LLM API keys per organization
  async encryptApiKey(apiKey: string, organizationId: string): Promise<EncryptedApiKey> {
    const iv = crypto.randomBytes(16);
    const salt = crypto.randomBytes(16);
    
    // Derive key specific to organization
    const derivedKey = crypto.pbkdf2Sync(this.masterKey, salt, 100000, this.keyLength, 'sha256');
    
    const cipher = crypto.createCipher(this.algorithm, derivedKey);
    cipher.setAAD(Buffer.from(organizationId));
    
    let encrypted = cipher.update(apiKey, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      salt: salt.toString('hex'),
      authTag: authTag.toString('hex'),
      organizationId
    };
  }
  
  async decryptApiKey(encryptedApiKey: EncryptedApiKey): Promise<string> {
    const { encrypted, iv, salt, authTag, organizationId } = encryptedApiKey;
    
    // Derive the same key
    const derivedKey = crypto.pbkdf2Sync(
      this.masterKey,
      Buffer.from(salt, 'hex'),
      100000,
      this.keyLength,
      'sha256'
    );
    
    const decipher = crypto.createDecipher(this.algorithm, derivedKey);
    decipher.setAAD(Buffer.from(organizationId));
    decipher.setAuthTag(Buffer.from(authTag, 'hex'));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
  
  // Process request data and decrypt sensitive fields
  async decryptSensitiveFields(data: any, organizationId: string): Promise<any> {
    if (!data || typeof data !== 'object') return data;
    
    const result = { ...data };
    
    // Look for encrypted fields (convention: end with '_encrypted')
    for (const [key, value] of Object.entries(data)) {
      if (key.endsWith('_encrypted') && typeof value === 'object' && value !== null) {
        try {
          const decrypted = await this.decryptApiKey(value as EncryptedApiKey);
          const originalKey = key.replace('_encrypted', '');
          result[originalKey] = decrypted;
          delete result[key]; // Remove encrypted version
        } catch (error) {
          // Log but don't crash - might be corrupted data
          console.warn(`Failed to decrypt field ${key}:`, error.message);
        }
      }
    }
    
    return result;
  }
}

// Usage for LLM configuration
class LLMConfigurationService {
  async saveProviderConfig(config: LLMProviderConfig, user: AuthenticatedUser): Promise<void> {
    // Encrypt API key before saving
    const encryptedApiKey = await this.encryption.encryptApiKey(
      config.apiKey,
      user.organization_id
    );
    
    await this.database.llm_configurations.upsert({
      organization_id: user.organization_id,
      provider: config.provider,
      model: config.model,
      api_key_encrypted: encryptedApiKey,
      settings: config.settings,
      updated_by: user.id
    });
  }
  
  async getProviderConfig(provider: string, organizationId: string): Promise<LLMProviderConfig> {
    const config = await this.database.llm_configurations
      .select('*')
      .eq('organization_id', organizationId)
      .eq('provider', provider)
      .single();
    
    if (!config) throw new Error('Provider configuration not found');
    
    // Decrypt API key
    const apiKey = await this.encryption.decryptApiKey(config.api_key_encrypted);
    
    return {
      provider: config.provider,
      model: config.model,
      apiKey,
      settings: config.settings
    };
  }
}
```

## 🔒 Complete Security Integration Example

Here's how all security services work together in a real endpoint:

```typescript
// packages/agent/src/routes/secure-chat.ts
export class SecureChatRoute {
  constructor(
    private securityPipeline: SecurityPipeline,
    private masterAgent: MasterAgent,
    private auditLogger: AuditLogger
  ) {}
  
  async handleChatMessage(req: Request, res: Response): Promise<void> {
    let securityContext: SecurityContext | null = null;
    
    try {
      // 1. Security pipeline processes the request
      const secureRequest = await this.securityPipeline.processRequest({
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        headers: req.headers,
        body: req.body,
        endpoint: 'chat',
        schema: this.chatMessageSchema
      });
      
      securityContext = {
        user: secureRequest.user,
        ip: req.ip,
        timestamp: new Date().toISOString()
      };
      
      // 2. Audit log the secure request
      await this.auditLogger.logSecureAccess('chat_message', securityContext);
      
      // 3. Process the business logic
      const result = await this.masterAgent.processMessage(secureRequest.data);
      
      // 4. Audit log the successful processing
      await this.auditLogger.logBusinessAction('message_processed', {
        ...securityContext,
        messageId: result.messageId
      });
      
      res.json({ 
        success: true, 
        messageId: result.messageId,
        status: 'queued'
      });
      
    } catch (error) {
      // 5. Security error handling and logging
      if (error instanceof SecurityError) {
        await this.auditLogger.logSecurityViolation(error.type, {
          ...securityContext,
          error: error.message,
          details: error.details
        });
        
        res.status(error.statusCode).json({
          error: 'Security violation',
          message: error.publicMessage
        });
      } else {
        // Log unexpected errors
        await this.auditLogger.logSystemError('chat_processing_error', {
          ...securityContext,
          error: error.message
        });
        
        res.status(500).json({
          error: 'Internal error',
          message: 'An unexpected error occurred'
        });
      }
    }
  }
}
```

## 📊 Security Monitoring and Alerting

**Real-time Security Monitoring**:
```typescript
class SecurityMonitor {
  private alertThresholds = {
    failed_auth_attempts: 10, // per 5 minutes
    rate_limit_violations: 50, // per minute
    validation_failures: 100, // per minute
    encryption_failures: 5 // per hour
  };
  
  async checkSecurityMetrics(): Promise<void> {
    const metrics = await this.gatherSecurityMetrics();
    
    for (const [metric, value] of Object.entries(metrics)) {
      const threshold = this.alertThresholds[metric as keyof typeof this.alertThresholds];
      
      if (value > threshold) {
        await this.triggerSecurityAlert(metric, value, threshold);
      }
    }
  }
  
  private async triggerSecurityAlert(metric: string, value: number, threshold: number): Promise<void> {
    const alert: SecurityAlert = {
      type: 'threshold_exceeded',
      metric,
      value,
      threshold,
      timestamp: new Date().toISOString(),
      severity: this.calculateSeverity(metric, value, threshold)
    };
    
    // Send to monitoring system
    await this.sendAlert(alert);
    
    // Log for audit trail
    await this.auditLogger.logSecurityAlert(alert);
  }
}
```

## 🧪 Security Testing Patterns

**Testing Security Components**:
```typescript
describe('Security Pipeline Integration', () => {
  it('should block requests with invalid tokens', async () => {
    const invalidRequest = {
      headers: { authorization: 'Bearer invalid-token' },
      body: { message: 'test' },
      ip: '127.0.0.1'
    };
    
    await expect(securityPipeline.processRequest(invalidRequest))
      .rejects
      .toThrow(AuthenticationError);
  });
  
  it('should rate limit excessive requests', async () => {
    const request = createValidRequest();
    
    // Make requests up to the limit
    for (let i = 0; i < 30; i++) {
      await securityPipeline.processRequest(request);
    }
    
    // Next request should be rate limited
    await expect(securityPipeline.processRequest(request))
      .rejects
      .toThrow(RateLimitError);
  });
  
  it('should decrypt encrypted fields correctly', async () => {
    const encryptedApiKey = await encryption.encryptApiKey('test-key', 'org-123');
    const requestWithEncrypted = {
      ...validRequest,
      body: { 
        config: { 
          provider: 'openai',
          api_key_encrypted: encryptedApiKey 
        }
      }
    };
    
    const secureRequest = await securityPipeline.processRequest(requestWithEncrypted);
    
    expect(secureRequest.data.config.api_key).toBe('test-key');
    expect(secureRequest.data.config.api_key_encrypted).toBeUndefined();
  });
});
```

## 🔍 Security Troubleshooting Guide

### Common Security Issues

1. **Encryption Failures**
   - **Symptom**: API keys cannot be decrypted
   - **Cause**: Wrong encryption key or corrupted data
   - **Solution**: Check `ENCRYPTION_KEY` environment variable

2. **Rate Limiting False Positives**
   - **Symptom**: Legitimate users being blocked
   - **Cause**: Shared IP addresses or aggressive limits
   - **Solution**: Implement user-based limiting alongside IP-based

3. **Token Validation Failures**
   - **Symptom**: Valid users getting authentication errors
   - **Cause**: Token cache issues or Supabase connectivity
   - **Solution**: Clear auth cache and check Supabase connection

This security integration guide provides the practical implementation patterns that are missing from the high-level security documentation. Next, similar detailed guides should be created for the UI package state management and the types package coverage reality.