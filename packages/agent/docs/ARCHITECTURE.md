# Master Agent Package Architecture

## Overview

The Master Agent package (`packages/agent/`) is the **central coordinator** for VibeLaunch's multi-agent marketplace system. It serves as the primary entry point for user requests and orchestrates the entire agent ecosystem.

## Core Components

### 1. **Master Agent Class** (`src/master-agent.ts`)

The `MasterAgent` class is the heart of the system with these key responsibilities:

#### Service Orchestration
- **Conversation Management**: Tracks multi-turn conversations with users
- **Task Decomposition**: Breaks down high-level requests into specialized agent tasks  
- **Agent Coordination**: Manages the lifecycle of specialized agent invocations
- **Result Synthesis**: Aggregates responses from multiple agents into coherent replies

#### Internal Architecture
```typescript
class MasterAgent {
  private registry: AgentRegistry                     // Agent discovery & capabilities
  private activePipelines: Map<string, PipelineRun>   // Active task executions
  private userThreads: Map<string, string>            // Chat to pipeline mapping
  private conversationStates: Map<string, ConversationState> // Conversation context
  private dynamicLLMConfigs: Map<string, any>         // Per-org LLM settings
  private busChannels: Map<string, RealtimeChannel>   // Real-time event channels
}
```

### 2. **Agent Registry** (`src/agent-registry.ts`)

Manages the marketplace of available specialized agents:

- **Agent Discovery**: Maintains registry of available agents and their capabilities
- **Capability Matching**: Finds agents that can handle specific task requirements
- **Health Monitoring**: Tracks agent availability and performance metrics
- **Dynamic Registration**: Allows agents to register/unregister at runtime

### 3. **Pipeline Manager** (`src/pipeline-manager.ts`)

Manages the execution lifecycle of marketing tasks:

- **Pipeline Creation**: Sets up execution context for complex tasks
- **Status Tracking**: Monitors progress through task execution stages
- **Resource Management**: Handles cleanup and resource allocation
- **Persistence**: Stores pipeline state in PostgreSQL

### 4. **MCP Connector** (`src/mcp-connector-improved.ts`)

Implements the Model Context Protocol for agent communication:

- **Message Routing**: Routes messages between agents and external services
- **Protocol Compliance**: Ensures MCP 1.0 standard compliance
- **Event Broadcasting**: Publishes events to the system event bus
- **Error Handling**: Manages communication failures and retries

## Data Flow Architecture

### 1. **User Message Processing**

```mermaid
graph TD
    A[User Message] --> B[Master Agent]
    B --> C[Conversation State]
    C --> D{Conversation Stage?}
    D -->|Initial/Gathering| E[Extract Client Info]
    D -->|Ready to Propose| F[Generate Proposal]
    D -->|Task Approved| G[Create Pipeline Run]
    G --> H[Begin Sequential Thinking]
    H --> I[Dispatch to Specialized Agents]
    I --> J[Synthesize Results]
    J --> K[Send Agent Reply]
```

### 2. **Event Flow Patterns**

The Master Agent integrates with multiple event systems:

#### Bus Events (PostgreSQL NOTIFY/LISTEN)
- **chat_posted**: Incoming user messages
- **task_completed**: Agent task completion notifications
- **agent_status**: Agent availability changes

#### MCP Protocol Events
- **task**: High-level user requests
- **tool_call**: Agent invocations
- **tool_result**: Agent responses
- **settings_response**: LLM configuration updates

### 3. **Multi-Tenant Event Routing**

```typescript
// Organization-specific event subscription
private subscribeToOrgBusEvents(orgId: string): void {
  const channelName = `bus-agent-${orgId}`;
  const channel = supabase.channel(channelName, {
    config: {
      broadcast: { ack: true, self: false },
      presence: { key: '' }
    }
  });
  
  channel.on('broadcast', { event: 'chat_posted' }, async (payload) => {
    // Process org-specific chat events
  });
}
```

## Security Integration

### 1. **Encryption Service Integration**

The Master Agent initializes and uses the encryption service for secure LLM credential management:

```typescript
// Initialize encryption service at startup
const masterEncryptionKey = process.env.MASTER_ENCRYPTION_KEY;
EncryptionService.initialize(masterEncryptionKey);

// Decrypt LLM credentials per organization
private getLLMConfig(orgId: string): any {
  const dynamicConfig = this.dynamicLLMConfigs.get(orgId);
  if (dynamicConfig) {
    // Credentials are already decrypted from webhook
    return dynamicConfig;
  }
  // Fall back to environment variables
}
```

### 2. **Multi-Tenant Security Boundaries**

- **Organization Isolation**: All operations scoped to organization ID
- **RLS Bypass**: Uses service role key for cross-tenant operations
- **Event Filtering**: Ensures events are only processed by authorized organizations

### 3. **Input Validation**

```typescript
// UUID validation for task IDs
const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
if (!uuidRegex.test(task_id)) {
  logger.warn(`Invalid UUID format for task_id: ${task_id}, generating new UUID`);
  task_id = randomUUID();
}
```

## Sequential Thinking Integration

The Master Agent orchestrates complex reasoning through the Sequential Thinking service:

### 1. **Thought Storage Pattern**

```typescript
await mcpConnector.callTool('storeThought', {
  organisation_id: orgId,
  thought: {
    pipeline_run_id: task_id,
    agent_role: 'MasterAgent',
    thought_number: 1,
    total_thoughts: 5,
    next_thought_needed: true,
    content: `Task received: ${JSON.stringify(input)}. Beginning analysis...`,
    confidence: 0.9
  }
});
```

### 2. **Progressive Reasoning**

The Master Agent breaks down complex decisions into sequential thoughts:

1. **Initial Analysis**: Understanding the user request
2. **Capability Assessment**: Determining required agent types
3. **Agent Selection**: Choosing optimal specialized agents
4. **Task Distribution**: Coordinating parallel agent execution
5. **Result Synthesis**: Combining agent outputs
6. **Response Generation**: Creating user-friendly replies

## LLM Provider Integration

### 1. **Dynamic Provider Management**

The Master Agent supports per-organization LLM configurations:

```typescript
private getLLMConfig(orgId: string): any {
  // Priority order:
  // 1. Dynamic config from webhook (latest)
  // 2. Stored settings (cached)
  // 3. Environment variables (fallback)
  
  const dynamicConfig = this.dynamicLLMConfigs.get(orgId);
  if (dynamicConfig) {
    return dynamicConfig;
  }
  
  const settings = this.settings.get(orgId);
  if (settings && settings.llm_config) {
    return settings.llm_config;
  }
  
  // Fallback to environment
  return {
    provider: process.env.LLM_PROVIDER || 'openai',
    api_key: process.env.OPENAI_API_KEY,
    default_model: process.env.LLM_MODEL || 'gpt-4'
  };
}
```

### 2. **Provider-Agnostic Design**

The architecture supports multiple LLM providers:
- **OpenAI**: GPT-4, GPT-3.5-turbo
- **Anthropic**: Claude models
- **Google AI**: Gemini models

## Conversation State Management

### 1. **Conversation Stages**

The Master Agent manages sophisticated conversation flows:

```typescript
interface ConversationState {
  stage: 'initial' | 'gathering_info' | 'ready_to_propose' | 'task_approved';
  clientInfo: {
    businessName?: string;
    industry?: string;
    targetAudience?: string;
    currentChallenges?: string[];
    marketingGoals?: string[];
    budget?: string;
    timeline?: string;
  };
  questionsAsked: string[];
  conversationHistory: Array<{ role: 'agent' | 'user'; content: string }>;
  proposalMade: boolean;
  autoMode: boolean;
}
```

### 2. **Information Extraction**

The Master Agent uses pattern matching to extract business information:

```typescript
private extractClientInfo(message: string, state: ConversationState): void {
  // Business name extraction
  const businessNameMatch = message.match(/(?:my business|company|organization)\s+(?:is\s+)?([A-Z][A-Za-z\s&]+)/);
  
  // Industry detection
  const industries = ['tech', 'retail', 'healthcare', 'finance'];
  industries.forEach(industry => {
    if (message.toLowerCase().includes(industry)) {
      state.clientInfo.industry = industry;
    }
  });
  
  // Goal and challenge extraction
  // ... pattern matching logic
}
```

## Error Handling & Resilience

### 1. **Agent Error Recovery**

```typescript
private async handleAgentError(pipelineRun: PipelineRun, toolCallId: string, error: unknown): Promise<void> {
  // Store error details
  if (!pipelineRun.agent_errors) pipelineRun.agent_errors = {};
  pipelineRun.agent_errors[toolCallId] = agentError;
  
  // Determine criticality
  const isCritical = this.isAgentCritical(pipelineRun, toolCallId);
  
  if (isCritical) {
    // Abort pipeline execution
    await updatePipelineStatus(pipelineRun.id, 'error');
  } else {
    // Continue with remaining agents
    const allOthersResponded = this.checkAllOtherAgentsResponded(pipelineRun, toolCallId);
    if (allOthersResponded) {
      await this.synthesizeResults(pipelineRun);
    }
  }
}
```

### 2. **Circuit Breaker Pattern**

The Master Agent implements resilience patterns:
- **Timeout Management**: Prevents indefinite waits for agent responses
- **Graceful Degradation**: Continues operation with partial agent responses
- **Error Propagation**: Proper error context preservation

## Performance Considerations

### 1. **Memory Management**

- **Pipeline Cleanup**: Automatic cleanup of completed pipelines
- **Conversation State**: TTL for inactive conversation states
- **Channel Management**: Proper subscription/unsubscription lifecycle

### 2. **Concurrency Patterns**

```typescript
// Parallel agent invocation
for (const agent of availableAgents) {
  await this.invokeAgent(pipelineRun, agent);
}

// Concurrent result processing
const allAgentsResponded = this.checkAllAgentsResponded(pipelineRun);
if (allAgentsResponded) {
  await this.synthesizeResults(pipelineRun);
}
```

## Deployment Architecture

### 1. **Railway Integration**

The Master Agent is designed for Railway deployment:

```typescript
// Railway environment detection
console.log('Environment:', {
  RAILWAY_ENVIRONMENT: process.env.RAILWAY_ENVIRONMENT,
  RAILWAY_SERVICE_NAME: process.env.RAILWAY_SERVICE_NAME,
  NODE_ENV: process.env.NODE_ENV,
  DEPLOYMENT_VERSION: '2025-06-04-v1'
});
```

### 2. **Health Monitoring**

```typescript
// Periodic heartbeat
setInterval(() => {
  logger.debug('[MasterAgent] Heartbeat - Service is running');
}, 30000);

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.log('[MasterAgent] SIGTERM received, cleaning up...');
  await masterAgent.cleanup();
  process.exit(0);
});
```

## Integration Points

### 1. **Database Integration**

- **Pipeline Storage**: PostgreSQL for pipeline run persistence
- **Agent Registry**: Dynamic agent registration and discovery
- **Event Bus**: PostgreSQL NOTIFY/LISTEN for real-time events

### 2. **External Services**

- **Sequential Thinking Service**: Complex reasoning orchestration
- **Specialized Agents**: Content creation, SEO, design, etc.
- **LLM Providers**: OpenAI, Anthropic, Google AI

### 3. **UI Integration**

- **Real-time Updates**: WebSocket connections for live status
- **Chat Interface**: Bidirectional conversation handling
- **Progress Tracking**: Pipeline status visualization

## Monitoring & Observability

### 1. **Logging Strategy**

```typescript
logger.log(`Created pipeline run ${pipelineRun.id} for task ${task_id}`);
logger.error(`Error dispatching task for pipeline ${pipelineRun.id}:`, errorMessage);
logger.warn(`Received tool result for unknown tool_call_id: ${toolCallId}`);
```

### 2. **Metrics Collection**

- **Pipeline Metrics**: Success rate, execution time, agent utilization
- **Conversation Metrics**: Stage progression, approval rates
- **Error Metrics**: Failure modes, recovery success rates

This architecture enables VibeLaunch to function as a sophisticated AI marketplace where the Master Agent acts as an intelligent coordinator, managing complex multi-agent interactions while maintaining security, scalability, and user experience quality.