import { SupabaseClient } from '@supabase/supabase-js';
import crypto from 'crypto';

// Types
export interface WebhookQueueItem {
  id: string;
  event_name: string;
  organisation_id: string;
  payload: unknown;
  webhook_url: string;
  status: string;
  attempts: number;
  max_attempts: number;
  created_at: string;
  last_error?: string | null;
  next_retry_at?: string | null;
}

export interface LLMConfig {
  provider: string;
  api_key: string;
  default_model: string;
  openai_org_id?: string;
}

export interface WebhookPayload {
  event: string;
  organisation_id: string;
  payload: unknown;
  timestamp: string;
  signature?: string;
  llm_config?: LLMConfig; // Add LLM config to webhook payload
}

export interface ProcessorOptions {
  batchSize?: number;
  webhookSecret?: string;
  maxConcurrent?: number;
}

export interface ProcessorStats {
  processedCount: number;
  errorCount: number;
  lastProcessTime: Date;
  isProcessing: boolean;
}

export class WebhookQueueProcessor {
  private supabase: SupabaseClient;
  private webhookSecret: string;
  private batchSize: number;
  private maxConcurrent: number;
  private stats: ProcessorStats;
  private isShuttingDown: boolean = false;
  
  // Events that should be handled by other services (not this worker)
  // Updated to include chat_posted since it's now handled by the event-based architecture
  private readonly SKIP_EVENTS = ['agent_reply', 'chat-relay', 'chat_posted'];

  constructor(supabase: SupabaseClient, options: ProcessorOptions = {}) {
    this.supabase = supabase;
    this.webhookSecret = options.webhookSecret || process.env.RAILWAY_WEBHOOK_SECRET || 'your-webhook-secret';
    this.batchSize = options.batchSize || 10;
    this.maxConcurrent = options.maxConcurrent || 5;
    this.stats = {
      processedCount: 0,
      errorCount: 0,
      lastProcessTime: new Date(),
      isProcessing: false
    };
  }

  // Generate webhook signature for security using proper HMAC
  private async generateSignature(payload: string, secret: string): Promise<string> {
    const encoder = new TextEncoder();
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(secret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );
    
    const signature = await crypto.subtle.sign(
      'HMAC',
      key,
      encoder.encode(payload)
    );
    
    return Array.from(new Uint8Array(signature))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  // Fetch LLM configuration for an organization
  private async fetchLLMConfig(orgId: string): Promise<LLMConfig | null> {
    try {
      console.log(`[Webhook] Fetching LLM config for org ${orgId}`);
      
      // Call the get_llm_config function
      const { data, error } = await this.supabase
        .rpc('get_llm_config', { p_org_id: orgId });
      
      if (error) {
        console.error(`[Webhook] Error fetching LLM config for org ${orgId}:`, error);
        return null;
      }
      
      if (!data || data.length === 0) {
        console.warn(`[Webhook] No LLM config found for org ${orgId}`);
        return null;
      }
      
      // Return the first config (organizations typically have one LLM provider)
      const config = data[0];
      console.log(`[Webhook] Found LLM config for org ${orgId}: provider=${config.provider}`);
      
      return {
        provider: config.provider,
        api_key: config.api_key,
        default_model: config.default_model,
        openai_org_id: config.openai_org_id
      };
    } catch (error) {
      console.error(`[Webhook] Exception fetching LLM config for org ${orgId}:`, error);
      return null;
    }
  }

  // Send webhook with error handling
  private async sendWebhook(item: WebhookQueueItem): Promise<{ success: boolean; error?: string }> {
    try {
      // Fetch LLM configuration for the organization
      const llmConfig = await this.fetchLLMConfig(item.organisation_id);
      
      // Create webhook payload
      const webhookPayload: WebhookPayload = {
        event: item.event_name,
        organisation_id: item.organisation_id,
        payload: item.payload,
        timestamp: item.created_at,
      };
      
      // Include LLM config if available
      if (llmConfig) {
        webhookPayload.llm_config = llmConfig;
      } else {
        console.warn(`[Webhook] Processing webhook without LLM config for org ${item.organisation_id}`);
      }

      // Generate signature
      const timestamp = Date.now().toString();
      const signaturePayload = JSON.stringify({
        event: item.event_name,
        data: item.payload,
        timestamp
      });
      
      const signature = await this.generateSignature(signaturePayload + timestamp, this.webhookSecret);
      
      // Update webhook URL to use the new /webhook-event endpoint
      let targetUrl = item.webhook_url;
      
      // For events that should go to the webhook-event endpoint
      if (item.event_name === 'thought_appended' ||
          item.event_name === 'invoke_sequential_thinking') {
        // Replace /chat or /webhook with /webhook-event
        if (targetUrl.includes('/chat')) {
          targetUrl = targetUrl.replace('/chat', '/webhook-event');
        } else if (targetUrl.includes('/webhook')) {
          targetUrl = targetUrl.replace('/webhook', '/webhook-event');
        } else {
          // If no endpoint specified, append /webhook-event
          targetUrl = targetUrl.replace(/\/$/, '') + '/webhook-event';
        }
        console.log(`[Webhook] Routing ${item.event_name} to webhook-event endpoint: ${targetUrl}`);
      }
      
      console.log(`[Webhook] Sending ${item.event_name} to ${targetUrl} (attempt ${item.attempts + 1}/${item.max_attempts})`);
      console.log(`[Webhook] Organisation: ${item.organisation_id}, Webhook ID: ${item.id}`);
      
      // Build data payload
      const basePayload = (item.payload as Record<string, any>) || {};
      const dataPayload: Record<string, any> = {
        ...basePayload,
        organisation_id: item.organisation_id
      };

      // Send in the appropriate format based on event type
      const response = await fetch(targetUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Event': item.event_name,
          'X-Webhook-Timestamp': timestamp,
          'X-Webhook-Signature': signature
        },
        body: JSON.stringify({
          event: item.event_name,
          data: dataPayload,
          timestamp: new Date().toISOString(),
          organisation_id: item.organisation_id
        }),
        signal: AbortSignal.timeout(30000) // 30 second timeout
      });

      if (response.ok) {
        const responseText = await response.text();
        console.log(`[Webhook] ✅ Successfully delivered ${item.event_name} to ${targetUrl}`);
        console.log(`[Webhook] Response: ${responseText.substring(0, 200)}${responseText.length > 200 ? '...' : ''}`);
        return { success: true };
      } else {
        const responseText = await response.text();
        const error = `HTTP ${response.status}: ${response.statusText} - ${responseText}`;
        console.error(`[Webhook] ❌ Failed to deliver ${item.event_name} to ${targetUrl}`);
        console.error(`[Webhook] Error: ${error}`);
        return { success: false, error };
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      console.error(`[Webhook] Error sending webhook:`, errorMsg);
      return { success: false, error: errorMsg };
    }
  }

  // Process a single webhook item
  private async processWebhookItem(webhook: WebhookQueueItem): Promise<void> {
    console.log(`[WebhookWorker] Processing webhook: ${webhook.event_name} (ID: ${webhook.id})`);
    
    // Skip specific events that are handled by other services
    if (this.SKIP_EVENTS.includes(webhook.event_name)) {
      console.log(`[WebhookWorker] Skipping ${webhook.event_name} - handled by other service`);
      
      // Mark as skipped without processing
      await this.supabase
        .from('webhook_queue')
        .update({
          status: 'skipped',
          processed_at: new Date().toISOString(),
          delivered_at: new Date().toISOString()
        })
        .eq('id', webhook.id);
      
      return;
    }

    // Update status to processing
    await this.supabase
      .from('webhook_queue')
      .update({
        status: 'processing',
        processed_at: new Date().toISOString()
      })
      .eq('id', webhook.id);

    // Send the webhook
    const result = await this.sendWebhook(webhook);

    if (result.success) {
      // Mark as delivered
      await this.supabase
        .from('webhook_queue')
        .update({
          status: 'delivered',
          delivered_at: new Date().toISOString(),
          attempts: webhook.attempts + 1
        })
        .eq('id', webhook.id);

      // Also update the webhook_deliveries table for compatibility
      await this.supabase
        .from('webhook_deliveries')
        .insert({
          webhook_url: webhook.webhook_url,
          event: webhook.event_name,
          payload: {
            event: webhook.event_name,
            organisation_id: webhook.organisation_id,
            payload: webhook.payload,
            timestamp: webhook.created_at
          },
          attempts: webhook.attempts + 1,
          status: 'delivered',
          created_at: webhook.created_at,
          delivered_at: new Date().toISOString()
        });

      this.stats.processedCount++;
      console.log(`[WebhookWorker] 📊 Stats - Processed: ${this.stats.processedCount}, Errors: ${this.stats.errorCount}`);
    } else {
      // Update with error and calculate next retry
      const newAttempts = webhook.attempts + 1;
      const nextRetryAt = new Date(Date.now() + Math.pow(2, newAttempts) * 1000).toISOString();

      await this.supabase
        .from('webhook_queue')
        .update({
          status: newAttempts >= webhook.max_attempts ? 'failed' : 'pending',
          attempts: newAttempts,
          last_error: result.error,
          next_retry_at: nextRetryAt
        })
        .eq('id', webhook.id);

      this.stats.errorCount++;
    }
  }

  // Process webhook queue batch
  public async processBatch(): Promise<void> {
    if (this.stats.isProcessing || this.isShuttingDown) {
      console.log('[Queue] Already processing or shutting down, skipping this cycle');
      return;
    }

    this.stats.isProcessing = true;
    this.stats.lastProcessTime = new Date();

    try {
      // Fetch pending webhooks
      const { data: pendingWebhooks, error: fetchError } = await this.supabase
        .from('webhook_queue')
        .select('*')
        .in('status', ['pending', 'failed'])
        .lte('next_retry_at', new Date().toISOString())
        .order('created_at', { ascending: true })
        .limit(this.batchSize);

      if (fetchError) {
        console.error('[Queue] Error fetching webhooks:', fetchError);
        this.stats.errorCount++;
        return;
      }

      if (!pendingWebhooks || pendingWebhooks.length === 0) {
        console.log('[Queue] No pending webhooks to process');
        return;
      }

      console.log(`[Queue] 🔄 Processing ${pendingWebhooks.length} webhooks`);
      console.log(`[Queue] Events: ${pendingWebhooks.map(w => `${w.event_name}(${w.status})`).join(', ')}`);

      // Process webhooks with concurrency control
      const chunks = [];
      for (let i = 0; i < pendingWebhooks.length; i += this.maxConcurrent) {
        chunks.push(pendingWebhooks.slice(i, i + this.maxConcurrent));
      }

      for (const chunk of chunks) {
        if (this.isShuttingDown) {
          console.log('[Queue] Shutdown requested, stopping processing');
          break;
        }
        
        await Promise.all(
          chunk.map(webhook => this.processWebhookItem(webhook))
        );
      }
    } catch (error) {
      console.error('[Queue] Error processing webhooks:', error);
      this.stats.errorCount++;
    } finally {
      this.stats.isProcessing = false;
    }
  }

  // Get current stats
  public getStats(): ProcessorStats {
    return { ...this.stats };
  }

  // Graceful shutdown
  public shutdown(): void {
    console.log('[Queue] Shutdown requested');
    this.isShuttingDown = true;
  }

  // Check if processor is ready
  public isReady(): boolean {
    return !this.isShuttingDown && !this.stats.isProcessing;
  }
}