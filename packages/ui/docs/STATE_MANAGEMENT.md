# UI State Management Strategy

## Overview

The VibeLaunch UI package implements a **hybrid state management approach** that combines React Context, custom hooks, and local state to handle different types of application state. This document outlines the patterns, decisions, and implementation details.

## State Management Architecture

### 1. **State Categories & Strategies**

| State Type | Strategy | Location | Reasoning |
|------------|----------|----------|-----------|
| **Authentication** | React Context | `AuthContext.tsx` | Global, needs cross-component access |
| **Real-time Events** | Custom Hooks | `useBusSubscription.ts` | Event-driven, subscription-based |
| **LLM Configuration** | Custom Hooks + Context | `useLlm.ts` + `McpLlmProvider.tsx` | Per-org settings, complex state |
| **Chat Messages** | Custom Hooks | `useChat.ts` | Real-time, scoped to conversation |
| **Component UI** | Local State | Individual components | Transient, component-specific |
| **Theme/Settings** | React Context | `ThemeContext.tsx` | Global preferences |

### 2. **Decision Matrix: When to Use What**

```typescript
// ✅ Use React Context for:
// - Global application state
// - Authentication/user data
// - Theme/appearance settings
// - Configuration that many components need

// ✅ Use Custom Hooks for:
// - Data fetching with real-time subscriptions
// - Complex stateful logic
// - Reusable state patterns
// - Integration with external services

// ✅ Use Local State for:
// - Form inputs
// - UI component state (modals, toggles)
// - Temporary/transient data
// - Performance-critical components
```

## Context Implementation Patterns

### 1. **Authentication Context** (`src/context/AuthContext.tsx`)

**Purpose**: Global authentication state and user session management

```typescript
interface AuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}
```

**Key Implementation Details**:

#### Multi-Tenant Organization Handling
```typescript
// Organization ID persistence across page refreshes
if (session?.user) {
  const { data: orgRows } = await supabase
    .from('organisations_users')
    .select('organisation_id')
    .eq('user_id', session.user.id)
    .limit(1);

  if (orgRows && orgRows.length > 0) {
    const orgId = orgRows[0].organisation_id;
    // Store both legacy and new keys for compatibility
    localStorage.setItem('currentOrganisationId', orgId);
    localStorage.setItem('currentOrganisation', orgId);
  }
}
```

#### Session Management
```typescript
useEffect(() => {
  // Get initial session
  const getInitialSession = async () => {
    const { data: { session } } = await supabase.auth.getSession();
    setSession(session);
    setUser(session?.user ?? null);
  };

  // Listen for auth state changes
  const { data: { subscription } } = supabase.auth.onAuthStateChange(
    (_event, sessionParam) => {
      setSession(sessionParam);
      setUser(sessionParam?.user ?? null);
      setIsLoading(false);
    }
  );

  return () => subscription.unsubscribe();
}, []);
```

### 2. **LLM Provider Context** (`src/contexts/McpLlmProvider.tsx`)

**Purpose**: Per-organization LLM configuration and provider management

```typescript
interface McpLlmContextType {
  provider: string | null;
  model: string | null;
  apiKey: string | null;
  isConfigured: boolean;
  isLoading: boolean;
  error: string | null;
  updateConfig: (config: LlmConfig) => Promise<void>;
}
```

**Key Features**:
- Organization-scoped LLM settings
- Encrypted API key management
- Provider switching (OpenAI, Anthropic, Google)
- Real-time configuration updates

### 3. **Theme Context** (`src/context/ThemeContext.tsx`)

**Purpose**: Application-wide theme and appearance settings

```typescript
interface ThemeContextType {
  theme: 'light' | 'dark' | 'system';
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  effectiveTheme: 'light' | 'dark';
}
```

## Custom Hooks Patterns

### 1. **Real-time Event Subscription** (`src/hooks/useBusSubscription.ts`)

**Purpose**: Subscribe to real-time events from the system bus

```typescript
export function useBusSubscription<T = unknown>(events: BusEvent[]): {
  events: Record<BusEvent, BusEnvelope<T>[]>;
  error: string | null;
} {
  const [eventStream, setEventStream] = useState<Record<BusEvent, BusEnvelope<T>[]>>(() => {
    const initial = {} as Record<BusEvent, BusEnvelope<T>[]>;
    events.forEach(e => { initial[e] = []; });
    return initial;
  });

  const eventsKey = useMemo(() => events.sort().join(','), [events]);

  useEffect(() => {
    const unsubscribers = events.map(event =>
      mcpSubscribe<T>([event], (envelope) => {
        setEventStream(prev => ({
          ...prev,
          [event]: [...prev[event], envelope]
        }));
      }, getOrgId())
    );

    return () => unsubscribers.forEach(unsub => unsub());
  }, [eventsKey, events]);

  return { events: eventStream, error };
}
```

**Key Implementation Details**:

#### Event Deduplication
```typescript
// Memoize events key to avoid unnecessary re-subscriptions
const eventsKey = useMemo(() => events.sort().join(','), [events]);
```

#### Organization Scoping
```typescript
// All event subscriptions are scoped to current organization
mcpSubscribe<T>([event], callback, getOrgId())
```

#### Cleanup Management
```typescript
// Proper cleanup prevents memory leaks
return () => unsubscribers.forEach(unsub => unsub());
```

### 2. **Chat Hook** (`src/hooks/useChat.ts`)

**Purpose**: Manage chat conversation state and real-time updates

```typescript
export function useChat(chatId: string) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Real-time message subscription
  useEffect(() => {
    const channel = supabase
      .channel(`chat_${chatId}`)
      .on('broadcast', { event: 'agent_reply' }, (payload) => {
        const newMessage: ChatMessage = {
          id: randomUUID(),
          chat_id: chatId,
          sender: 'agent',
          message: payload.message,
          timestamp: new Date().toISOString()
        };
        setMessages(prev => [...prev, newMessage]);
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [chatId]);

  const sendMessage = useCallback(async (message: string) => {
    // Implementation for sending messages
  }, [chatId]);

  return { messages, isLoading, error, sendMessage };
}
```

### 3. **LLM Configuration Hook** (`src/hooks/useLLMConfiguration.ts`)

**Purpose**: Manage LLM provider settings with encryption

```typescript
export function useLLMConfiguration() {
  const [config, setConfig] = useState<LLMConfiguration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const updateConfiguration = useCallback(async (newConfig: LLMConfiguration) => {
    try {
      setIsLoading(true);
      
      // Encrypt API keys before storage
      const encryptedConfig = {
        ...newConfig,
        api_key: await encryptApiKey(newConfig.api_key)
      };
      
      const { error } = await supabase
        .from('llm_configurations')
        .upsert({
          organisation_id: getOrgId(),
          ...encryptedConfig
        });

      if (error) throw error;
      
      setConfig(newConfig);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update configuration');
    } finally {
      setIsLoading(false);
    }
  }, []);

  return { config, isLoading, error, updateConfiguration };
}
```

## Real-time Integration Patterns

### 1. **Supabase Real-time Subscriptions**

**Pattern**: Direct WebSocket subscriptions for database changes

```typescript
// Subscribe to table changes
useEffect(() => {
  const channel = supabase
    .channel('chat_log_changes')
    .on('postgres_changes', 
      { event: 'INSERT', schema: 'public', table: 'chat_log' },
      (payload) => {
        if (payload.new.chat_id === chatId) {
          setMessages(prev => [...prev, payload.new as ChatMessage]);
        }
      }
    )
    .subscribe();

  return () => supabase.removeChannel(channel);
}, [chatId]);
```

### 2. **Bus Event Integration**

**Pattern**: System-wide event bus for complex interactions

```typescript
// Listen for bus events across the application
const { events } = useBusSubscription(['task_created', 'agent_response', 'pipeline_completed']);

useEffect(() => {
  const taskEvents = events.task_created;
  if (taskEvents.length > 0) {
    const latestTask = taskEvents[taskEvents.length - 1];
    // Handle new task creation
  }
}, [events.task_created]);
```

### 3. **MCP Protocol Integration**

**Pattern**: Model Context Protocol for agent communication

```typescript
// MCP message handling
const { sendMcpMessage, mcpResponse } = useMcp();

const handleUserInput = useCallback(async (input: string) => {
  await sendMcpMessage({
    type: 'task',
    data: {
      task_id: randomUUID(),
      stage: 'initial',
      input: { message: input, chat_id: chatId }
    }
  });
}, [chatId, sendMcpMessage]);
```

## Performance Optimization Patterns

### 1. **Memoization Strategies**

```typescript
// Memoize expensive computations
const processedMessages = useMemo(() => {
  return messages.map(msg => ({
    ...msg,
    formattedTime: formatTimestamp(msg.timestamp),
    avatar: generateAvatar(msg.sender)
  }));
}, [messages]);

// Memoize event handlers
const handleSendMessage = useCallback(async (message: string) => {
  // Implementation
}, [chatId, orgId]);
```

### 2. **Subscription Optimization**

```typescript
// Optimize real-time subscriptions
const eventsKey = useMemo(() => 
  events.sort().join(','), 
  [events]
);

useEffect(() => {
  // Only re-subscribe when events actually change
}, [eventsKey]); // Not [events] which changes on every render
```

### 3. **Lazy Loading**

```typescript
// Lazy load heavy components
const LazySettingsPanel = lazy(() => import('./SettingsPanel'));
const LazyMarketplace = lazy(() => import('../pages/MarketplacePage'));

// Usage with Suspense
<Suspense fallback={<LoadingScreen />}>
  <LazySettingsPanel />
</Suspense>
```

## Error Handling Patterns

### 1. **Context Error Boundaries**

```typescript
// Error handling in contexts
const signIn = async (email: string, password: string) => {
  try {
    setIsLoading(true);
    setError(null);
    const { session } = await supabaseSignIn(email, password);
    setSession(session);
  } catch (error) {
    console.error('Error signing in:', error);
    setError(error instanceof Error ? error.message : 'Failed to sign in');
    throw error; // Re-throw for component handling
  } finally {
    setIsLoading(false);
  }
};
```

### 2. **Hook Error Management**

```typescript
// Centralized error handling in hooks
export function useChat(chatId: string) {
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => setError(null), []);
  
  const sendMessage = useCallback(async (message: string) => {
    try {
      setError(null);
      // Send message implementation
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Failed to send message:', err);
    }
  }, [chatId]);

  return { messages, error, clearError, sendMessage };
}
```

### 3. **Graceful Degradation**

```typescript
// Handle missing data gracefully
const { user, isLoading } = useAuth();
const orgId = getOrgId();

if (isLoading) {
  return <LoadingScreen />;
}

if (!user) {
  return <LoginPage />;
}

if (!orgId) {
  return <OrganizationSetup />;
}

// Main application
return <Dashboard />;
```

## Data Flow Patterns

### 1. **Top-Down Data Flow**

```mermaid
graph TD
    A[App.tsx] --> B[AuthProvider]
    B --> C[ThemeProvider]
    C --> D[Layout]
    D --> E[Dashboard]
    E --> F[ChatInterface]
    F --> G[MessageList]
    F --> H[MessageInput]
```

### 2. **Event-Driven Updates**

```mermaid
graph LR
    A[User Input] --> B[useChat Hook]
    B --> C[Supabase API]
    C --> D[Real-time Event]
    D --> E[useBusSubscription]
    E --> F[UI Update]
```

### 3. **Cross-Component Communication**

```typescript
// Pattern: Context + Events for complex interactions
const ChatToTaskProvider = ({ children }) => {
  const [taskContext, setTaskContext] = useState(null);
  
  // Listen for chat-to-task events
  const { events } = useBusSubscription(['task_requirement']);
  
  useEffect(() => {
    const latestRequirement = events.task_requirement.at(-1);
    if (latestRequirement) {
      setTaskContext(latestRequirement.payload);
    }
  }, [events.task_requirement]);
  
  return (
    <ChatToTaskContext.Provider value={{ taskContext, setTaskContext }}>
      {children}
    </ChatToTaskContext.Provider>
  );
};
```

## Testing Patterns

### 1. **Context Testing**

```typescript
// Mock context providers for testing
const MockAuthProvider = ({ children, mockUser = null }) => {
  const mockValue = {
    user: mockUser,
    session: null,
    isLoading: false,
    error: null,
    signIn: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn()
  };
  
  return (
    <AuthContext.Provider value={mockValue}>
      {children}
    </AuthContext.Provider>
  );
};
```

### 2. **Hook Testing**

```typescript
// Test custom hooks with renderHook
import { renderHook, act } from '@testing-library/react';

test('useChat should handle message sending', async () => {
  const { result } = renderHook(() => useChat('test-chat-id'));
  
  await act(async () => {
    await result.current.sendMessage('Hello world');
  });
  
  expect(result.current.messages).toHaveLength(1);
  expect(result.current.error).toBeNull();
});
```

## Migration Strategies

### 1. **From Zustand to Context**

```typescript
// Before: Zustand store
const useAuthStore = create((set) => ({
  user: null,
  setUser: (user) => set({ user })
}));

// After: React Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuth must be used within AuthProvider');
  return context;
};
```

### 2. **Local State to Custom Hook**

```typescript
// Before: Local state in component
const [messages, setMessages] = useState([]);
const [isLoading, setIsLoading] = useState(false);

// After: Custom hook
const { messages, isLoading, sendMessage } = useChat(chatId);
```

## Best Practices

### 1. **Context Design**

- **Single Responsibility**: One context per domain (auth, theme, etc.)
- **Minimal API**: Only expose what consumers need
- **Type Safety**: Always provide TypeScript interfaces
- **Error Boundaries**: Handle context errors gracefully

### 2. **Hook Design**

- **Pure Functions**: Hooks should be predictable and testable
- **Cleanup**: Always clean up subscriptions and timers
- **Dependencies**: Minimize useEffect dependencies
- **Memoization**: Use useMemo and useCallback for performance

### 3. **Performance**

- **Lazy Loading**: Split code at route boundaries
- **Subscription Optimization**: Avoid unnecessary re-subscriptions
- **State Normalization**: Keep related data together
- **Selective Updates**: Only update components that need it

### 4. **Real-time Integration**

- **Organization Scoping**: All subscriptions must respect multi-tenancy
- **Connection Management**: Handle WebSocket disconnections
- **Event Deduplication**: Prevent duplicate event processing
- **Graceful Degradation**: Work without real-time when needed

This state management strategy provides VibeLaunch with a scalable, maintainable, and performant foundation for complex real-time multi-tenant interactions while keeping the codebase organized and testable.