# Types Package: Coverage Assessment & Build Issues

## Current Type Coverage Reality

### **Shared Types Package** (`packages/types/`)

#### ✅ **What's Actually Shared**
```typescript
// packages/types/src/index.ts exports:
├── bus.ts          // Bus events & envelopes (19 types)
├── mcp.ts          // MCP protocol types (15+ types)  
├── profile.ts      // User profile types
└── supabase.ts     // Database types
```

**Total Shared Types**: ~40 type definitions

#### ⚠️ **Build System Issues**

**Problem**: The types package uses a custom build script instead of standard TypeScript compilation:

```json
// packages/types/package.json
"scripts": {
  "build": "node direct-build.js",        // ❌ Custom build script
  "build:tsc": "rimraf dist && tsc...",   // ✅ Standard TypeScript build (unused)
}
```

**Root Cause**: Module resolution issues with NodeNext resolution strategy

### **Local Type Definitions Scattered Across Packages**

#### 🚨 **UI Package Local Types** (`packages/ui/src/`)
```typescript
// These should be shared but aren't:
├── types/mcp.ts           // Duplicate MCP types
├── types/bus.ts           // Duplicate bus types  
├── context/AuthContext.tsx // Auth-specific types
├── contexts/McpLlmProvider.tsx // LLM config types
├── config/providers.ts     // Provider configuration types
├── utils/diagnostics.ts    // Diagnostic types
└── bus-event-augment.d.ts  // Bus event augmentations
```

**Estimated Local Types**: ~30+ type definitions that should be shared

#### 🚨 **Agent Package Local Types** (`packages/agent/src/`)
```typescript
// Local types that should be shared:
├── master-agent.ts        // ConversationState, AgentMetadata
├── agent-types.ts         // AgentCapability, PipelineRun
├── agent-registry.ts      // Registry types
├── security/*.ts          // Security-related types
└── lib/llm.ts            // LLM provider types
```

**Estimated Local Types**: ~25+ type definitions

#### 🚨 **Other Packages**
```typescript
// Sequential Thinking
packages/sequential-thinking/src/
├── index.ts               // Thought types
└── web-server.ts          // Server types

// Worker Package
packages/worker/src/
├── webhookQueueWorker.ts  // Queue types

// Redis Streams
packages/redis-streams/src/
├── types/database.ts      // Event types
└── schemas/               // Schema types
```

**Total Local Types**: ~15+ additional type definitions

## Type Coverage Analysis

### **Coverage by Category**

| Category | Shared Types | Local Types | Duplication | Status |
|----------|-------------|-------------|-------------|--------|
| **Database/Supabase** | ✅ Shared | ⚠️ Some local | Low | Good |
| **Bus Events** | ✅ Shared | ❌ Duplicated in UI | High | Poor |
| **MCP Protocol** | ✅ Shared | ❌ Duplicated in UI | High | Poor |
| **Agent System** | ❌ Not shared | ✅ All local | None | Missing |
| **LLM Integration** | ❌ Not shared | ✅ All local | Medium | Missing |
| **Authentication** | ❌ Not shared | ✅ All local | Low | Missing |
| **UI Components** | ❌ Not shared | ✅ All local | Low | Missing |
| **Security** | ❌ Not shared | ✅ All local | Low | Missing |

### **Duplication Analysis**

#### **Critical Duplications** (Same types defined in multiple places)

1. **MCP Protocol Types**
   - **Shared**: `packages/types/src/mcp.ts`
   - **Duplicate**: `packages/ui/src/types/mcp.ts`
   - **Issue**: UI package doesn't use shared types due to import issues

2. **Bus Event Types**
   - **Shared**: `packages/types/src/bus.ts`
   - **Duplicate**: `packages/ui/src/types/bus.ts`
   - **Issue**: Event augmentation requires local definitions

3. **LLM Configuration Types**
   - **Agent**: `packages/agent/src/master-agent.ts` (LLMConfig interface)
   - **UI**: `packages/ui/src/contexts/McpLlmProvider.tsx` (LlmConfig interface)
   - **Issue**: Same concept, different naming and structure

## Build System Issues

### **1. Custom Build Script Problems**

```javascript
// packages/types/direct-build.js
// Custom build script that bypasses TypeScript compiler
// Issues:
// - No type checking during build
// - Non-standard module output
// - Inconsistent with other packages
```

### **2. Module Resolution Issues**

```typescript
// packages/agent/src/master-agent.ts (lines 41-105)
// Workaround for import issues:
enum MCPMessageType {
  task = 'task',
  tool_call = 'tool_call',
  // ... local redefinition of shared types
}

// Comment: "NodeNext module resolution issues"
```

### **3. Import Path Inconsistencies**

```typescript
// Some packages use relative imports:
import { BusEvent } from '../types/bus.ts';

// Others try to use shared types:
import { BusEvent } from '@vibelaunch/types';

// But fall back to local definitions when imports fail
```

### **4. TypeScript Configuration Issues**

```json
// tsconfig.json issues across packages:
{
  "moduleResolution": "NodeNext",  // Causes import issues
  "module": "NodeNext",           // Not compatible with build tools
  "type": "module"                // Conflicts with some dependencies
}
```

## Missing Shared Types Analysis

### **High Priority Missing Types**

#### **1. Agent System Types**
```typescript
// Should be in @vibelaunch/types but currently local:
interface ConversationState {
  stage: 'initial' | 'gathering_info' | 'ready_to_propose' | 'task_approved';
  clientInfo: ClientInfo;
  questionsAsked: string[];
  conversationHistory: Array<{ role: 'agent' | 'user'; content: string }>;
  proposalMade: boolean;
  autoMode: boolean;
}

interface PipelineRun {
  id: string;
  task_id: string;
  organisation_id: string;
  chat_id: string;
  status: PipelineStatus;
  // ... 15+ more properties
}

type AgentCapability = 
  | 'content_creation'
  | 'visual_design' 
  | 'marketing_strategy'
  | 'software_development'
  | 'seo_optimization';
```

#### **2. LLM Provider Types**
```typescript
// Currently duplicated across packages:
interface LLMConfiguration {
  provider: 'openai' | 'anthropic' | 'google';
  apiKey: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  organizationId?: string; // OpenAI specific
}

interface LLMResponse {
  content: string;
  tokenUsage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: string;
}
```

#### **3. Security Types**
```typescript
// Currently only in agent package:
interface EncryptedCredential {
  encryptedData: string;
  iv: string;
  authTag: string;
  algorithm: 'aes-256-gcm';
}

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}
```

#### **4. Real-time Event Types**
```typescript
// Bus event augmentations scattered across packages:
interface ChatToTaskEvent {
  chat_id: string;
  task_id: string;
  user_message: string;
  extracted_requirements: TaskRequirement[];
}

interface AgentStatusEvent {
  agent_id: string;
  status: 'online' | 'offline' | 'busy';
  capabilities: AgentCapability[];
  performance_metrics: AgentMetrics;
}
```

## Migration Strategy

### **Phase 1: Fix Build System** (1 week)

#### **1.1 Replace Custom Build Script**
```bash
# Remove custom build
rm packages/types/direct-build.js

# Update package.json
{
  "scripts": {
    "build": "tsc -p tsconfig.build.json --declaration --outDir dist",
    "dev": "tsc -p tsconfig.build.json --watch"
  }
}
```

#### **1.2 Fix TypeScript Configuration**
```json
// packages/types/tsconfig.build.json
{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "moduleResolution": "bundler",  // Instead of NodeNext
    "module": "ES2022",            // Instead of NodeNext
    "declaration": true,
    "outDir": "dist"
  },
  "exclude": ["dist", "__tests__", "*.test.ts"]
}
```

#### **1.3 Fix Import Resolution**
```typescript
// Update packages/types/src/index.ts
export type * from './bus.js';
export type * from './mcp.js';
export * from './profile.js';
export * from './supabase.js';

// Add new exports
export type * from './agent.js';      // New
export type * from './llm.js';        // New  
export type * from './security.js';   // New
export type * from './realtime.js';   // New
```

### **Phase 2: Consolidate Duplicated Types** (1 week)

#### **2.1 Remove Duplicated MCP Types**
```typescript
// Remove packages/ui/src/types/mcp.ts
// Update all imports to use @vibelaunch/types

// Before:
import { MCPMessage } from '../types/mcp';

// After:
import { MCPMessage } from '@vibelaunch/types';
```

#### **2.2 Remove Duplicated Bus Types**
```typescript
// Remove packages/ui/src/types/bus.ts
// Consolidate event augmentations in shared types

// packages/types/src/bus.ts
export interface ChatToTaskEvent extends BusEnvelope {
  // Moved from UI package
}
```

### **Phase 3: Add Missing Shared Types** (2 weeks)

#### **3.1 Agent System Types**
```typescript
// packages/types/src/agent.ts
export interface ConversationState { /* ... */ }
export interface PipelineRun { /* ... */ }
export type AgentCapability = /* ... */;
export interface AgentMetadata { /* ... */ }
export interface AgentPerformance { /* ... */ }
```

#### **3.2 LLM Provider Types**
```typescript
// packages/types/src/llm.ts
export interface LLMConfiguration { /* ... */ }
export interface LLMResponse { /* ... */ }
export interface LLMProvider { /* ... */ }
export type LLMProviderType = 'openai' | 'anthropic' | 'google';
```

#### **3.3 Security Types**
```typescript
// packages/types/src/security.ts
export interface EncryptedCredential { /* ... */ }
export interface RateLimitConfig { /* ... */ }
export interface SecurityAuditLog { /* ... */ }
```

### **Phase 4: Update All Packages** (1 week)

#### **4.1 Update Agent Package**
```typescript
// packages/agent/src/master-agent.ts
// Remove local type definitions
// import { ConversationState, PipelineRun } from '@vibelaunch/types';
```

#### **4.2 Update UI Package**
```typescript
// packages/ui/src/contexts/McpLlmProvider.tsx
// Remove local LLM types
// import { LLMConfiguration } from '@vibelaunch/types';
```

#### **4.3 Update Other Packages**
```typescript
// Update sequential-thinking, worker, redis-streams packages
// Replace local types with shared types where applicable
```

## Testing Strategy for Types

### **1. Type Testing**
```typescript
// packages/types/__tests__/type-coverage.test.ts
import { expectType, expectError } from 'tsd';
import type { ConversationState, LLMConfiguration } from '../src';

// Test type compatibility
expectType<ConversationState>({
  stage: 'initial',
  clientInfo: {},
  questionsAsked: [],
  conversationHistory: [],
  proposalMade: false,
  autoMode: false
});

// Test type errors
expectError<LLMConfiguration>({
  provider: 'invalid-provider' // Should error
});
```

### **2. Build Testing**
```bash
# Test that all packages can import shared types
pnpm build:types
pnpm --filter "@vibelaunch/*" build
```

### **3. Import Testing**
```typescript
// Test imports work from all packages
import { BusEvent, MCPMessage, ConversationState } from '@vibelaunch/types';
```

## Expected Outcomes

### **After Migration**

#### **Type Distribution**
| Category | Before | After | Improvement |
|----------|--------|-------|-------------|
| **Shared Types** | 40 | 85+ | +112% |
| **Duplicated Types** | 15+ | 0 | -100% |
| **Local Types** | 70+ | 25 | -64% |
| **Total Coverage** | 40% shared | 80% shared | +100% |

#### **Build System**
- ✅ Standard TypeScript compilation
- ✅ Proper type checking during build
- ✅ Consistent module output
- ✅ No more custom build scripts

#### **Developer Experience**
- ✅ Single source of truth for types
- ✅ Better IDE autocomplete and error checking
- ✅ Easier refactoring across packages
- ✅ Consistent type definitions

### **Success Metrics**

#### **Phase 1 Success**
- [ ] Types package builds with standard TypeScript compiler
- [ ] All packages can import from @vibelaunch/types
- [ ] No build errors in any package

#### **Phase 2 Success**
- [ ] Zero duplicate type definitions
- [ ] All MCP and Bus types use shared definitions
- [ ] Import paths consistent across packages

#### **Phase 3 Success**
- [ ] 80%+ of types are shared
- [ ] Agent, LLM, and Security types available shared
- [ ] New type definitions added to shared package

#### **Phase 4 Success**
- [ ] All packages use shared types where applicable
- [ ] Build system stable across all packages
- [ ] Type coverage improved by 100%

## Implementation Commands

### **Setup Testing**
```bash
# Install type testing tools
pnpm add -D tsd @types/jest

# Create type tests
mkdir -p packages/types/__tests__
touch packages/types/__tests__/type-coverage.test.ts
```

### **Migration Commands**
```bash
# Phase 1: Fix build system
cd packages/types
rm direct-build.js
# Update package.json scripts

# Phase 2: Remove duplicates
rm packages/ui/src/types/mcp.ts
rm packages/ui/src/types/bus.ts

# Phase 3: Add new shared types
touch packages/types/src/agent.ts
touch packages/types/src/llm.ts
touch packages/types/src/security.ts

# Phase 4: Test everything builds
pnpm build
```

## Current Reality vs Goals

**Current State**:
- 40 shared types
- 70+ local types  
- 15+ duplicate definitions
- Custom build system causing issues
- ~40% type sharing

**Target State**:
- 85+ shared types
- 25 necessary local types
- 0 duplicate definitions  
- Standard TypeScript build
- ~80% type sharing

This migration will significantly improve type safety, reduce duplication, and create a more maintainable codebase with proper type sharing across all packages.