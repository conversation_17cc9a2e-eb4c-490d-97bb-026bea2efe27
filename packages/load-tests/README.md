# Load Testing Package

Performance and load testing suite for VibeLaunch using K6, providing comprehensive testing scenarios for system validation and capacity planning.

## Features

- **User Journey Testing**: End-to-end user workflow validation
- **Stress Testing**: System behavior under high load
- **Spike Testing**: Response to sudden traffic increases  
- **Soak Testing**: Long-duration stability testing
- **Redis Streams Testing**: Event processing performance
- **Performance Reporting**: Detailed metrics and analysis

## Prerequisites

### Install K6

```bash
# macOS
brew install k6

# Ubuntu/Debian
sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
sudo apt-get update
sudo apt-get install k6

# Windows
choco install k6
```

### Environment Setup

Create `.env` file with test environment configuration:

```bash
# VibeLaunch test environment
BASE_URL=http://localhost:5173
API_URL=http://localhost:8090
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key

# Test user credentials
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=testpassword123

# Load test configuration
MAX_VUS=100
TEST_DURATION=5m
```

## Testing Scenarios

### 1. User Journey Testing

Tests complete user workflows end-to-end:

```bash
# Basic user journey test
pnpm run test:user-journey

# Baseline performance test (10 users, 5 minutes)
pnpm run test:baseline
```

**User Journey Includes**:
- User registration and login
- Master Agent chat interaction
- Contract creation and bidding
- Real-time updates verification

### 2. Stress Testing

Tests system under high concurrent load:

```bash
# Stress test with ramping load
pnpm run test:stress

# Spike test for sudden traffic bursts
pnpm run test:spike
```

**Test Profile**:
- Ramp up to 500 concurrent users
- Hold peak load for 10 minutes
- Monitor system degradation points

### 3. Soak Testing

Long-duration testing for stability validation:

```bash
# 24-hour soak test
pnpm run test:soak
```

**Validates**:
- Memory leak detection
- Performance degradation over time
- Resource cleanup effectiveness

### 4. Redis Streams Performance

Tests event processing capabilities:

```bash
# Redis Streams stress test
pnpm run test:redis-stress
```

**Measures**:
- Event publishing throughput
- Consumer lag and processing time
- Dead letter queue behavior

## Test Configuration

### Load Profiles

Test configurations are defined in `src/config/`:

```javascript
// src/config/stress.json
{
  "scenarios": {
    "stress_test": {
      "executor": "ramping-vus",
      "stages": [
        { "duration": "2m", "target": 100 },
        { "duration": "5m", "target": 500 },
        { "duration": "2m", "target": 0 }
      ]
    }
  },
  "thresholds": {
    "http_req_duration": ["p(95)<2000"],
    "http_req_failed": ["rate<0.1"]
  }
}
```

### Custom Scenarios

Create custom test scenarios in `src/scenarios/`:

```javascript
// src/scenarios/custom-test.js
import { check } from 'k6';
import http from 'k6/http';

export let options = {
  vus: 10,
  duration: '5m',
};

export default function() {
  // Your custom test logic
  let response = http.get(`${__ENV.API_URL}/health`);
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });
}
```

## Performance Metrics

### Key Metrics Tracked

- **Response Time**: p50, p95, p99 percentiles
- **Throughput**: Requests per second
- **Error Rate**: Failed request percentage
- **Concurrent Users**: Active virtual users
- **System Resources**: CPU, memory, database connections

### Thresholds

Default performance thresholds:

```javascript
export let options = {
  thresholds: {
    // 95% of requests must complete within 2 seconds
    'http_req_duration': ['p(95)<2000'],
    
    // Error rate must be below 1%
    'http_req_failed': ['rate<0.01'],
    
    // Average response time below 500ms
    'http_req_duration{expected_response:true}': ['avg<500'],
    
    // Check success rate above 95%
    'checks': ['rate>0.95'],
  },
};
```

## Results and Reporting

### Real-time Monitoring

During test execution, monitor:

```bash
# K6 provides real-time metrics during test
# ✓ [█████████████████████████████████████] 10/10 VUs  5m0s/5m0s

# Monitor these key indicators:
# - Response times stay under thresholds
# - Error rates remain low
# - System resources don't saturate
```

### Performance Reports

Generate detailed reports after testing:

```bash
# Generate HTML performance report
pnpm run report

# View results
open reports/performance-report.html
```

### CI/CD Integration

Add performance gates to your pipeline:

```yaml
# .github/workflows/performance-test.yml
- name: Performance Test
  run: |
    cd packages/load-tests
    pnpm run test:baseline
    
    # Fail if performance degrades
    if [ $? -ne 0 ]; then
      echo "Performance test failed!"
      exit 1
    fi
```

## Common Test Patterns

### Authentication Testing

```javascript
export function authenticateUser() {
  let loginResponse = http.post(`${baseUrl}/auth/signin`, {
    email: __ENV.TEST_USER_EMAIL,
    password: __ENV.TEST_USER_PASSWORD,
  });
  
  check(loginResponse, {
    'login successful': (r) => r.status === 200,
  });
  
  return loginResponse.json('access_token');
}
```

### Chat Interaction Testing

```javascript
export function testChatInteraction(authToken) {
  let chatResponse = http.post(
    `${apiUrl}/chat`,
    JSON.stringify({
      message: 'Hello, Master Agent',
      conversation_id: generateUUID(),
    }),
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    }
  );
  
  check(chatResponse, {
    'chat response received': (r) => r.status === 200,
    'response time acceptable': (r) => r.timings.duration < 3000,
  });
}
```

### Real-time Testing

```javascript
import ws from 'k6/ws';

export function testRealTimeUpdates() {
  let response = ws.connect(`wss://${__ENV.SUPABASE_URL}/realtime/v1/websocket`, 
    function (socket) {
      socket.on('open', function open() {
        socket.send(JSON.stringify({
          topic: 'realtime:chat_log',
          event: 'phx_join',
          payload: {},
          ref: '1'
        }));
      });
      
      socket.on('message', function (message) {
        let data = JSON.parse(message);
        check(data, {
          'realtime message received': (d) => d.event === 'postgres_changes',
        });
      });
    }
  );
}
```

## Performance Baselines

### Expected Performance Targets

| Metric | Target | Critical Threshold |
|--------|--------|--------------------|
| Response Time (p95) | < 1s | < 2s |
| Error Rate | < 0.1% | < 1% |
| Concurrent Users | 500+ | 100+ |
| Throughput | 1000 req/s | 100 req/s |
| Memory Usage | < 2GB | < 4GB |

### Benchmarking

Regular benchmark tests to track performance trends:

```bash
# Weekly performance benchmark
pnpm run test:baseline 2>&1 | tee "benchmark-$(date +%Y%m%d).log"

# Compare with previous results
./scripts/compare-benchmarks.sh
```

## Troubleshooting

### Common Issues

**High Response Times**:
```bash
# Check if database is the bottleneck
# Monitor database connections during test
psql $SUPABASE_URL -c "SELECT count(*) FROM pg_stat_activity;"
```

**Memory Leaks**:
```bash
# Monitor memory usage during soak tests
# Set up memory alerts in monitoring
```

**Network Issues**:
```bash
# Test network latency to services
ping your-api-domain.com
curl -w "@curl-format.txt" -o /dev/null -s http://your-api-url/health
```

### Performance Debugging

```javascript
// Add detailed timing to identify bottlenecks
export default function() {
  let start = Date.now();
  
  let response = http.get(`${baseUrl}/api/endpoint`);
  
  let duration = Date.now() - start;
  console.log(`Request took ${duration}ms`);
  
  if (duration > 1000) {
    console.log(`Slow request detected: ${response.url}`);
  }
}
```

## Integration with Monitoring

### Prometheus Metrics

Export test results to Prometheus:

```javascript
// Custom metrics for monitoring integration
import { Counter, Gauge } from 'k6/metrics';

let errorRate = new Gauge('vibelaunch_test_error_rate');
let responseTime = new Gauge('vibelaunch_test_response_time');

export default function() {
  // ... test logic
  
  errorRate.add(response.status !== 200 ? 1 : 0);
  responseTime.add(response.timings.duration);
}
```

### Alert Integration

Set up alerts for performance regressions:

```yaml
# Alertmanager rule
- alert: PerformanceRegression
  expr: vibelaunch_test_response_time > 2000
  for: 2m
  labels:
    severity: warning
  annotations:
    summary: "Performance test shows regression"
```

## Scripts Reference

- `test:user-journey` - Complete user workflow test
- `test:redis-stress` - Redis Streams performance test
- `test:baseline` - Standard performance baseline
- `test:stress` - High-load stress testing
- `test:spike` - Sudden traffic spike testing
- `test:soak` - Long-duration stability test
- `report` - Generate performance reports
- `lint` - Code quality checks

## Contributing

1. Follow K6 testing best practices
2. Add tests for new features
3. Maintain performance baselines
4. Document test scenarios
5. Update thresholds as system evolves

## Related Documentation

- [Performance Optimization Guide](../../docs/05-guides/best-practices/performance.md)
- [System Architecture](../../docs/02-architecture/SYSTEM_ARCHITECTURE.md)
- [Monitoring Setup](../monitoring/README.md)
- [Database Optimization](../database-optimization/README.md)

## License

MIT - See LICENSE file for details