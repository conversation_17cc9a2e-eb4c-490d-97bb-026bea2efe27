# Server Configuration
PORT=8091
NODE_ENV=development
LOG_LEVEL=debug

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...your-service-role-key

# LLM Configuration (optional fallback)
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...

# Railway Environment (for deployed services)
RAILWAY_ENVIRONMENT=development
RAILWAY_SERVICE_NAME=sequential-thinking